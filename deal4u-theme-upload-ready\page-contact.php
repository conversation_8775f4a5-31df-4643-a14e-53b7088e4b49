<?php
/**
 * Template Name: Contact Page
 */

get_header(); ?>

<main class="main-content">
    <!-- Contact Header -->
    <section style="padding: 4rem 0; background: linear-gradient(135deg, #1e293b, #334155, #475569); color: white; position: relative; overflow: hidden;">
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0); background-size: 20px 20px; opacity: 0.3;"></div>
        <div class="container" style="position: relative; z-index: 1;">
            <div style="text-align: center; max-width: 800px; margin: 0 auto;">
                <div style="display: inline-flex; align-items: center; padding: 0.75rem 1.5rem; background: rgba(59, 130, 246, 0.2); color: #60a5fa; border-radius: 2rem; font-size: 0.875rem; font-weight: 600; margin-bottom: 2rem; backdrop-filter: blur(10px); border: 1px solid rgba(59, 130, 246, 0.3);">
                    <i class="fas fa-headset" style="margin-right: 0.5rem;"></i>
                    24/7 Customer Support
                </div>
                <h1 style="font-size: 3.5rem; font-weight: bold; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff, #cbd5e1); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Get in Touch</h1>
                <p style="font-size: 1.25rem; color: #cbd5e1; line-height: 1.6;">Have a question, need support, or want to learn more about our products? Our dedicated team is here to help you every step of the way.</p>
            </div>
        </div>
    </section>

    <!-- Contact Content -->
    <section style="padding: 5rem 0; background: linear-gradient(135deg, #f8fafc, #e2e8f0);">
        <div class="container">
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; max-width: 1400px; margin: 0 auto;">

                <!-- Contact Form -->
                <div style="background: white; padding: 3rem; border-radius: 1.5rem; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1); border: 1px solid #e2e8f0; position: relative; overflow: hidden;">
                    <div style="position: absolute; top: -50%; right: -50%; width: 200px; height: 200px; background: linear-gradient(45deg, #dbeafe, #bfdbfe); border-radius: 50%; opacity: 0.1;"></div>
                    <div style="position: relative; z-index: 1;">
                        <div style="display: flex; align-items: center; margin-bottom: 2rem;">
                            <div style="width: 3.5rem; height: 3.5rem; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 1rem; display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
                                <i class="fas fa-paper-plane" style="color: white; font-size: 1.25rem;"></i>
                            </div>
                            <div>
                                <h2 style="font-size: 1.75rem; font-weight: bold; color: #1e293b; margin: 0;">Send us a Message</h2>
                                <p style="color: #64748b; margin: 0; font-size: 0.9rem;">We'll get back to you within 24 hours</p>
                            </div>
                        </div>

                        <?php
                        // Display success/error messages
                        if (isset($_GET['message'])) {
                            if ($_GET['message'] == 'sent') {
                                echo '<div style="background: linear-gradient(135deg, #f0fdf4, #dcfce7); border: 2px solid #22c55e; padding: 1.5rem; border-radius: 1rem; margin-bottom: 2rem; box-shadow: 0 4px 12px rgba(34, 197, 94, 0.2);">
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 2.5rem; height: 2.5rem; background: #22c55e; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
                                                <i class="fas fa-check" style="color: white; font-size: 1rem;"></i>
                                            </div>
                                            <div>
                                                <p style="color: #15803d; margin: 0; font-weight: 600; font-size: 1.1rem;">Message sent successfully!</p>
                                                <p style="color: #16a34a; margin: 0; font-size: 0.9rem;">We\'ll get back to you within 24 hours.</p>
                                            </div>
                                        </div>
                                      </div>';
                            } elseif ($_GET['message'] == 'error') {
                                echo '<div style="background: linear-gradient(135deg, #fef2f2, #fee2e2); border: 2px solid #ef4444; padding: 1.5rem; border-radius: 1rem; margin-bottom: 2rem; box-shadow: 0 4px 12px rgba(239, 68, 68, 0.2);">
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 2.5rem; height: 2.5rem; background: #ef4444; border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
                                                <i class="fas fa-exclamation-triangle" style="color: white; font-size: 1rem;"></i>
                                            </div>
                                            <div>
                                                <p style="color: #dc2626; margin: 0; font-weight: 600; font-size: 1.1rem;">Error sending message</p>
                                                <p style="color: #ef4444; margin: 0; font-size: 0.9rem;">Please try again or contact us directly.</p>
                                            </div>
                                        </div>
                                      </div>';
                            }
                        }
                        ?>

                        <form method="post" action="" style="display: flex; flex-direction: column; gap: 1.5rem;">
                            <?php wp_nonce_field('deal4u_contact_form', 'contact_nonce'); ?>
                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1.5rem;">
                                <div>
                                    <label style="display: block; font-weight: 600; color: #1e293b; margin-bottom: 0.75rem; font-size: 0.9rem;">First Name *</label>
                                    <input type="text" name="first_name" style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 0.75rem; font-size: 1rem; transition: all 0.3s; background: #f8fafc;" placeholder="John" required value="<?php echo isset($_POST['first_name']) ? esc_attr($_POST['first_name']) : ''; ?>" onfocus="this.style.borderColor='#3b82f6'; this.style.background='white'; this.style.boxShadow='0 0 0 3px rgba(59, 130, 246, 0.1)'" onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f8fafc'; this.style.boxShadow='none'">
                                </div>
                                <div>
                                    <label style="display: block; font-weight: 600; color: #1e293b; margin-bottom: 0.75rem; font-size: 0.9rem;">Last Name *</label>
                                    <input type="text" name="last_name" style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 0.75rem; font-size: 1rem; transition: all 0.3s; background: #f8fafc;" placeholder="Doe" required value="<?php echo isset($_POST['last_name']) ? esc_attr($_POST['last_name']) : ''; ?>" onfocus="this.style.borderColor='#3b82f6'; this.style.background='white'; this.style.boxShadow='0 0 0 3px rgba(59, 130, 246, 0.1)'" onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f8fafc'; this.style.boxShadow='none'">
                                </div>
                            </div>

                            <div>
                                <label style="display: block; font-weight: 600; color: #1e293b; margin-bottom: 0.75rem; font-size: 0.9rem;">Email Address *</label>
                                <input type="email" name="email" style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 0.75rem; font-size: 1rem; transition: all 0.3s; background: #f8fafc;" placeholder="<EMAIL>" required value="<?php echo isset($_POST['email']) ? esc_attr($_POST['email']) : ''; ?>" onfocus="this.style.borderColor='#3b82f6'; this.style.background='white'; this.style.boxShadow='0 0 0 3px rgba(59, 130, 246, 0.1)'" onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f8fafc'; this.style.boxShadow='none'">
                            </div>

                            <div>
                                <label style="display: block; font-weight: 600; color: #1e293b; margin-bottom: 0.75rem; font-size: 0.9rem;">Phone Number <span style="color: #64748b; font-weight: 400;">(Optional)</span></label>
                                <input type="tel" name="phone" style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 0.75rem; font-size: 1rem; transition: all 0.3s; background: #f8fafc;" placeholder="+447447186806" value="<?php echo isset($_POST['phone']) ? esc_attr($_POST['phone']) : ''; ?>" onfocus="this.style.borderColor='#3b82f6'; this.style.background='white'; this.style.boxShadow='0 0 0 3px rgba(59, 130, 246, 0.1)'" onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f8fafc'; this.style.boxShadow='none'">
                            </div>

                            <div>
                                <label style="display: block; font-weight: 600; color: #1e293b; margin-bottom: 0.75rem; font-size: 0.9rem;">Subject *</label>
                                <select name="subject" style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 0.75rem; font-size: 1rem; background: #f8fafc; transition: all 0.3s; cursor: pointer;" required onfocus="this.style.borderColor='#3b82f6'; this.style.background='white'; this.style.boxShadow='0 0 0 3px rgba(59, 130, 246, 0.1)'" onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f8fafc'; this.style.boxShadow='none'">
                                    <option value="">Select a subject</option>
                                    <option value="general" <?php selected(isset($_POST['subject']) ? $_POST['subject'] : '', 'general'); ?>>💬 General Inquiry</option>
                                    <option value="order" <?php selected(isset($_POST['subject']) ? $_POST['subject'] : '', 'order'); ?>>📦 Order Support</option>
                                    <option value="technical" <?php selected(isset($_POST['subject']) ? $_POST['subject'] : '', 'technical'); ?>>🔧 Technical Support</option>
                                    <option value="billing" <?php selected(isset($_POST['subject']) ? $_POST['subject'] : '', 'billing'); ?>>💳 Billing Question</option>
                                    <option value="return" <?php selected(isset($_POST['subject']) ? $_POST['subject'] : '', 'return'); ?>>↩️ Return/Refund</option>
                                    <option value="partnership" <?php selected(isset($_POST['subject']) ? $_POST['subject'] : '', 'partnership'); ?>>🤝 Partnership Inquiry</option>
                                    <option value="wholesale" <?php selected(isset($_POST['subject']) ? $_POST['subject'] : '', 'wholesale'); ?>>🏢 Wholesale/Bulk Orders</option>
                                </select>
                            </div>

                            <div>
                                <label style="display: block; font-weight: 600; color: #1e293b; margin-bottom: 0.75rem; font-size: 0.9rem;">Message *</label>
                                <textarea name="message" rows="6" style="width: 100%; padding: 1rem; border: 2px solid #e2e8f0; border-radius: 0.75rem; font-size: 1rem; resize: vertical; transition: all 0.3s; background: #f8fafc; font-family: inherit;" placeholder="Please describe how we can help you..." required onfocus="this.style.borderColor='#3b82f6'; this.style.background='white'; this.style.boxShadow='0 0 0 3px rgba(59, 130, 246, 0.1)'" onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f8fafc'; this.style.boxShadow='none'"><?php echo isset($_POST['message']) ? esc_textarea($_POST['message']) : ''; ?></textarea>
                            </div>

                            <button type="submit" name="send_contact_message" style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; font-weight: bold; padding: 1rem 2rem; border-radius: 0.75rem; border: none; cursor: pointer; font-size: 1.1rem; transition: all 0.3s; transform: scale(1); box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3); width: 100%;" onmouseover="this.style.transform='scale(1.02)'; this.style.boxShadow='0 8px 20px rgba(59, 130, 246, 0.4)'" onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.3)'">
                                <i class="fas fa-paper-plane" style="margin-right: 0.75rem;"></i>Send Message
                            </button>

                            <?php if (current_user_can('administrator')): ?>
                            <div style="margin-top: 1.5rem; padding: 1rem; background: #fef3c7; border: 1px solid #f59e0b; border-radius: 0.75rem;">
                                <p style="margin: 0 0 1rem 0; color: #92400e; font-size: 0.9rem; font-weight: 600;">
                                    <i class="fas fa-tools" style="margin-right: 0.5rem;"></i>Admin Tools
                                </p>
                                <a href="<?php echo add_query_arg('test_email', '1'); ?>" style="display: inline-flex; align-items: center; padding: 0.5rem 1rem; background: #f59e0b; color: white; font-weight: 600; border-radius: 0.5rem; text-decoration: none; font-size: 0.85rem; transition: all 0.3s;" onmouseover="this.style.background='#d97706'" onmouseout="this.style.background='#f59e0b'">
                                    <i class="fas fa-envelope-open-text" style="margin-right: 0.5rem;"></i>Test Email Function
                                </a>
                            </div>
                            <?php endif; ?>
                        </form>
                    </div>
                </div>

                <!-- Contact Information -->
                <div>
                    <div style="margin-bottom: 3rem;">
                        <div style="display: flex; align-items: center; margin-bottom: 1.5rem;">
                            <div style="width: 3.5rem; height: 3.5rem; background: linear-gradient(135deg, #22c55e, #16a34a); border-radius: 1rem; display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
                                <i class="fas fa-info-circle" style="color: white; font-size: 1.25rem;"></i>
                            </div>
                            <div>
                                <h2 style="font-size: 1.75rem; font-weight: bold; color: #1e293b; margin: 0;">Contact Information</h2>
                                <p style="color: #64748b; margin: 0; font-size: 0.9rem;">Multiple ways to reach our support team</p>
                            </div>
                        </div>
                    </div>

                    <div style="display: flex; flex-direction: column; gap: 1.5rem;">

                        <!-- Email Support -->
                        <div style="background: white; padding: 2rem; border-radius: 1.2rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08); border: 1px solid #e2e8f0; transition: all 0.3s; position: relative; overflow: hidden;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.12)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.08)'">
                            <div style="position: absolute; top: -50%; right: -50%; width: 100px; height: 100px; background: linear-gradient(45deg, #dbeafe, #bfdbfe); border-radius: 50%; opacity: 0.2;"></div>
                            <div style="display: flex; align-items: center; position: relative; z-index: 1;">
                                <div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 1rem; display: flex; align-items: center; justify-content: center; color: white; margin-right: 1.5rem; box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);">
                                    <i class="fas fa-envelope" style="font-size: 1.25rem;"></i>
                                </div>
                                <div style="flex: 1;">
                                    <h3 style="font-weight: bold; color: #1e293b; margin-bottom: 0.5rem; font-size: 1.2rem;">Email Support</h3>
                                    <p style="color: #3b82f6; font-weight: 600; margin-bottom: 0.25rem; font-size: 1.1rem;"><EMAIL></p>
                                    <p style="color: #64748b; margin: 0; font-size: 0.85rem;">Response within 24 hours</p>
                                </div>
                            </div>
                        </div>

                        <!-- Phone Support -->
                        <div style="background: white; padding: 2rem; border-radius: 1.2rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08); border: 1px solid #e2e8f0; transition: all 0.3s; position: relative; overflow: hidden;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.12)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.08)'">
                            <div style="position: absolute; top: -50%; right: -50%; width: 100px; height: 100px; background: linear-gradient(45deg, #dcfce7, #bbf7d0); border-radius: 50%; opacity: 0.2;"></div>
                            <div style="display: flex; align-items: center; position: relative; z-index: 1;">
                                <div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #22c55e, #16a34a); border-radius: 1rem; display: flex; align-items: center; justify-content: center; color: white; margin-right: 1.5rem; box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);">
                                    <i class="fas fa-phone" style="font-size: 1.25rem;"></i>
                                </div>
                                <div style="flex: 1;">
                                    <h3 style="font-weight: bold; color: #1e293b; margin-bottom: 0.5rem; font-size: 1.2rem;">Phone Support</h3>
                                    <p style="color: #22c55e; font-weight: 600; margin-bottom: 0.25rem; font-size: 1.1rem;">+447447186806</p>
                                    <p style="color: #64748b; margin: 0; font-size: 0.85rem;">Available 24/7</p>
                                </div>
                            </div>
                        </div>

                        <!-- Business Address -->
                        <div style="background: white; padding: 2rem; border-radius: 1.2rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08); border: 1px solid #e2e8f0; transition: all 0.3s; position: relative; overflow: hidden;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 15px 35px rgba(0, 0, 0, 0.12)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.08)'">
                            <div style="position: absolute; top: -50%; right: -50%; width: 100px; height: 100px; background: linear-gradient(45deg, #fef3c7, #fde68a); border-radius: 50%; opacity: 0.2;"></div>
                            <div style="display: flex; align-items: center; position: relative; z-index: 1;">
                                <div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 1rem; display: flex; align-items: center; justify-content: center; color: white; margin-right: 1.5rem; box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);">
                                    <i class="fas fa-map-marker-alt" style="font-size: 1.25rem;"></i>
                                </div>
                                <div style="flex: 1;">
                                    <h3 style="font-weight: bold; color: #1e293b; margin-bottom: 0.5rem; font-size: 1.2rem;">Business Address</h3>
                                    <p style="color: #f59e0b; font-weight: 600; margin-bottom: 0.25rem; line-height: 1.4;">Fröbelstraße 12<br>41515 Grevenbroich<br>Germany</p>
                                    <p style="color: #64748b; margin: 0; font-size: 0.85rem;">Registered business location</p>
                                </div>
                            </div>
                        </div>



                        <!-- WhatsApp Support -->
                        <div style="background: linear-gradient(135deg, #22c55e, #16a34a); color: white; padding: 2.5rem; border-radius: 1.2rem; text-align: center; box-shadow: 0 10px 25px rgba(34, 197, 94, 0.2); position: relative; overflow: hidden;">
                            <div style="position: absolute; top: -50%; right: -50%; width: 150px; height: 150px; background: rgba(255, 255, 255, 0.1); border-radius: 50%;"></div>
                            <div style="position: relative; z-index: 1;">
                                <div style="display: inline-flex; align-items: center; justify-content: center; width: 4rem; height: 4rem; background: rgba(255, 255, 255, 0.2); border-radius: 1rem; margin-bottom: 1.5rem; backdrop-filter: blur(10px);">
                                    <i class="fab fa-whatsapp" style="font-size: 1.5rem;"></i>
                                </div>
                                <h3 style="font-weight: bold; margin-bottom: 0.75rem; font-size: 1.3rem;">WhatsApp Support</h3>
                                <p style="margin-bottom: 1.5rem; color: #dcfce7; font-size: 0.95rem;">Get instant help via WhatsApp chat</p>
                                <a href="https://wa.me/447447186806?text=Hello%20Deal4u!%20I%20need%20help%20with..." target="_blank" style="background: white; color: #22c55e; font-weight: bold; padding: 1rem 2rem; border-radius: 0.75rem; text-decoration: none; display: inline-flex; align-items: center; transition: all 0.3s; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);" onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 8px 20px rgba(0, 0, 0, 0.15)'" onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(0, 0, 0, 0.1)'">
                                    <i class="fab fa-whatsapp" style="margin-right: 0.75rem; font-size: 1.1rem;"></i>Start Chat
                                </a>
                                <p style="margin-top: 1rem; color: #bbf7d0; font-size: 0.8rem;">Usually responds within minutes</p>
                            </div>
                        </div>

                        <!-- Business Hours -->
                        <div style="background: white; padding: 2rem; border-radius: 1.2rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08); border: 1px solid #e2e8f0; text-align: center;">
                            <div style="display: inline-flex; align-items: center; justify-content: center; width: 4rem; height: 4rem; background: linear-gradient(135deg, #8b5cf6, #7c3aed); border-radius: 1rem; margin-bottom: 1.5rem;">
                                <i class="fas fa-clock" style="color: white; font-size: 1.25rem;"></i>
                            </div>
                            <h3 style="font-weight: bold; color: #1e293b; margin-bottom: 1rem; font-size: 1.2rem;">Business Hours</h3>
                            <div style="color: #64748b; line-height: 1.6;">
                                <p style="margin: 0; font-size: 1.1rem;"><strong style="color: #1e293b;">24/7 Support Available</strong></p>
                            </div>
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </section>

    <!-- FAQ Section -->
    <section style="padding: 4rem 0; background: white;">
        <div class="container">
            <div style="text-align: center; margin-bottom: 3rem;">
                <div style="display: inline-flex; align-items: center; padding: 0.75rem 1.5rem; background: #fef3c7; color: #d97706; border-radius: 2rem; font-size: 0.875rem; font-weight: 600; margin-bottom: 1.5rem;">
                    <i class="fas fa-question-circle" style="margin-right: 0.5rem;"></i>
                    Quick Help
                </div>
                <h2 style="font-size: 2.5rem; font-weight: bold; color: #1e293b; margin-bottom: 1rem;">Frequently Asked Questions</h2>
                <p style="color: #64748b; max-width: 600px; margin: 0 auto; font-size: 1.1rem;">Find quick answers to common questions</p>
            </div>

            <div style="max-width: 800px; margin: 0 auto; display: grid; gap: 1rem;">
                <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 1rem; padding: 1.5rem;">
                    <h4 style="color: #1e293b; font-weight: 600; margin-bottom: 0.5rem;">How quickly do you respond to messages?</h4>
                    <p style="color: #64748b; margin: 0; font-size: 0.9rem;">We provide 24/7 support! Email responses typically within a few hours, and WhatsApp messages are usually answered within minutes at any time of day.</p>
                </div>
                <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 1rem; padding: 1.5rem;">
                    <h4 style="color: #1e293b; font-weight: 600; margin-bottom: 0.5rem;">What information should I include in my message?</h4>
                    <p style="color: #64748b; margin: 0; font-size: 0.9rem;">Please include your order number (if applicable), a detailed description of your issue, and any relevant screenshots or photos to help us assist you better.</p>
                </div>
                <div style="background: #f8fafc; border: 1px solid #e2e8f0; border-radius: 1rem; padding: 1.5rem;">
                    <h4 style="color: #1e293b; font-weight: 600; margin-bottom: 0.5rem;">Do you offer phone support?</h4>
                    <p style="color: #64748b; margin: 0; font-size: 0.9rem;">Yes! You can call us at +447447186806 anytime - we're available 24/7. For instant messaging, WhatsApp is also available around the clock.</p>
                </div>
            </div>
        </div>
    </section>
</main>

<?php get_footer(); ?>
