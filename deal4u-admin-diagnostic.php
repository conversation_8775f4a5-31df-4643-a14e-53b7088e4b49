<?php
/**
 * Deal4u WordPress Admin Diagnostic Tool
 * 
 * Add this code to your functions.php file temporarily
 * Then access via WordPress admin: Tools > Deal4u Diagnostic
 */

// Add admin menu for diagnostics
add_action('admin_menu', 'deal4u_add_diagnostic_menu');

function deal4u_add_diagnostic_menu() {
    add_management_page(
        'Deal4u Diagnostic',
        'Deal4u Diagnostic', 
        'manage_options',
        'deal4u-diagnostic',
        'deal4u_diagnostic_page'
    );
}

function deal4u_diagnostic_page() {
    if (!current_user_can('manage_options')) {
        wp_die('You do not have sufficient permissions to access this page.');
    }
    
    ?>
    <div class="wrap">
        <h1>🔧 Deal4u Theme Diagnostic Report</h1>
        <p><strong>Generated:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        
        <style>
            .diagnostic-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
            .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
            .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
            .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
            .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
            .status-ok { color: #28a745; font-weight: bold; }
            .status-error { color: #dc3545; font-weight: bold; }
            .status-warning { color: #ffc107; font-weight: bold; }
            table { width: 100%; border-collapse: collapse; margin: 10px 0; }
            th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
            th { background-color: #f2f2f2; }
        </style>

        <div class="diagnostic-section info">
            <h2>🎨 Active Theme Information</h2>
            <?php
            $current_theme = wp_get_theme();
            echo '<div><strong>Theme Name:</strong> ' . $current_theme->get('Name') . '</div>';
            echo '<div><strong>Theme Directory:</strong> ' . $current_theme->get_stylesheet() . '</div>';
            echo '<div><strong>Theme Path:</strong> ' . $current_theme->get_stylesheet_directory() . '</div>';
            echo '<div><strong>Theme Version:</strong> ' . $current_theme->get('Version') . '</div>';
            ?>
        </div>

        <div class="diagnostic-section">
            <h2>📁 Critical Files Check</h2>
            <table>
                <tr><th>File</th><th>Status</th><th>Size</th><th>Last Modified</th></tr>
                <?php
                $theme_dir = get_template_directory();
                $critical_files = [
                    'functions.php' => 'Theme functions',
                    'index.php' => 'Homepage template',
                    'js/theme.js' => 'Theme JavaScript'
                ];
                
                foreach ($critical_files as $file => $description) {
                    $full_path = $theme_dir . '/' . $file;
                    if (file_exists($full_path)) {
                        $size = number_format(filesize($full_path)) . ' bytes';
                        $modified = date('Y-m-d H:i:s', filemtime($full_path));
                        $status = '<span class="status-ok">✓ Exists</span>';
                    } else {
                        $size = '-';
                        $modified = '-';
                        $status = '<span class="status-error">✗ Missing</span>';
                    }
                    echo "<tr><td><strong>$file</strong><br><small>$description</small></td>";
                    echo "<td>$status</td><td>$size</td><td>$modified</td></tr>";
                }
                ?>
            </table>
        </div>

        <div class="diagnostic-section">
            <h2>🔧 Functions.php Analysis</h2>
            <?php
            $functions_file = get_template_directory() . '/functions.php';
            if (file_exists($functions_file)) {
                $content = file_get_contents($functions_file);
                
                // Check for specific functions
                $checks = [
                    'Timer Function' => strpos($content, 'countdown') !== false || strpos($content, 'timer') !== false,
                    'Product Query Function' => strpos($content, 'deal4u_get_flash_sale_products') !== false,
                    'Error Handling' => strpos($content, 'set_error_handler') !== false,
                    'WooCommerce Support' => strpos($content, 'woocommerce') !== false
                ];
                
                echo '<table>';
                echo '<tr><th>Feature</th><th>Status</th></tr>';
                foreach ($checks as $feature => $exists) {
                    $status = $exists ? 
                        '<span class="status-ok">✓ Present</span>' : 
                        '<span class="status-error">✗ Missing</span>';
                    echo "<tr><td>$feature</td><td>$status</td></tr>";
                }
                echo '</table>';
                
                echo '<div><strong>File Size:</strong> ' . number_format(filesize($functions_file)) . ' bytes</div>';
                echo '<div><strong>Last Modified:</strong> ' . date('Y-m-d H:i:s', filemtime($functions_file)) . '</div>';
            } else {
                echo '<div class="error">❌ functions.php file not found!</div>';
            }
            ?>
        </div>

        <div class="diagnostic-section">
            <h2>🛒 WooCommerce Status</h2>
            <?php
            if (class_exists('WooCommerce')) {
                echo '<div class="success">✓ WooCommerce is active</div>';
                echo '<div><strong>WooCommerce Version:</strong> ' . WC()->version . '</div>';
                
                // Check products
                $product_count = wp_count_posts('product');
                echo '<div><strong>Total Products:</strong> ' . $product_count->publish . '</div>';
                
                // Check for products on sale
                global $wpdb;
                $sale_products = $wpdb->get_var(
                    "SELECT COUNT(*) FROM {$wpdb->postmeta} 
                     WHERE meta_key = '_sale_price' AND meta_value != ''"
                );
                echo '<div><strong>Products on Sale:</strong> ' . $sale_products . '</div>';
                
            } else {
                echo '<div class="error">❌ WooCommerce is not active!</div>';
            }
            ?>
        </div>

        <div class="diagnostic-section">
            <h2>🚨 Current Issues Detection</h2>
            <?php
            $issues = [];
            
            // Check if timer function exists
            $functions_content = file_exists(get_template_directory() . '/functions.php') ? 
                file_get_contents(get_template_directory() . '/functions.php') : '';
            
            if (strpos($functions_content, 'countdown') === false && strpos($functions_content, 'timer') === false) {
                $issues[] = '🔴 Timer functionality missing from functions.php';
            }
            
            if (strpos($functions_content, 'deal4u_get_flash_sale_products') === false) {
                $issues[] = '🔴 Improved product query function missing';
            }
            
            if (!class_exists('WooCommerce')) {
                $issues[] = '🔴 WooCommerce plugin not active';
            }
            
            if (empty($issues)) {
                echo '<div class="success">✅ No critical issues detected!</div>';
            } else {
                foreach ($issues as $issue) {
                    echo '<div class="error">' . $issue . '</div>';
                }
            }
            ?>
        </div>

        <div class="diagnostic-section warning">
            <h2>💡 Recommended Actions</h2>
            <ol>
                <li><strong>Upload Latest functions.php:</strong> Ensure your local functions.php with timer and product fixes is uploaded to the active theme directory</li>
                <li><strong>Clear Cache:</strong> Clear all caching plugins and server cache</li>
                <li><strong>Test Timer:</strong> Check if countdown timer is working on homepage</li>
                <li><strong>Test Products:</strong> Verify 4 products show in "Direct From Suppliers" section</li>
                <li><strong>Check Error Logs:</strong> Review WordPress error logs for any fatal errors</li>
            </ol>
        </div>

        <div class="diagnostic-section info">
            <h2>📋 Quick Test Links</h2>
            <p><a href="<?php echo home_url(); ?>" target="_blank">🏠 Test Homepage</a></p>
            <p><a href="<?php echo home_url('/shop'); ?>" target="_blank">🛒 Test Shop Page</a></p>
            <p><a href="<?php echo admin_url('admin.php?page=wc-status'); ?>" target="_blank">📊 WooCommerce Status</a></p>
        </div>
    </div>
    <?php
}
?>
