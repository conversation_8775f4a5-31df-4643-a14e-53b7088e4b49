<?php
/**
 * Deal4u WordPress Theme Troubleshooting Tool
 * 
 * This file helps diagnose issues between local and live WordPress installations
 * Upload this file to your WordPress root directory and access it via browser
 * 
 * Usage: https://yourdomain.com/deal4u-troubleshoot.php
 */

// Security check - only allow access from specific IPs or with password
$allowed_ips = ['127.0.0.1', '::1']; // Add your IP here
$access_password = 'deal4u2025'; // Change this password

if (!in_array($_SERVER['REMOTE_ADDR'], $allowed_ips) && 
    (!isset($_GET['password']) || $_GET['password'] !== $access_password)) {
    die('Access denied. Add ?password=deal4u2025 to URL or access from localhost.');
}

// Start output buffering for clean display
ob_start();

// Include WordPress
if (file_exists('./wp-config.php')) {
    require_once('./wp-config.php');
    require_once('./wp-load.php');
} else {
    die('WordPress not found. Place this file in your WordPress root directory.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Deal4u Theme Troubleshooting Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        h1, h2 { color: #333; }
        h2 { border-bottom: 2px solid #007cba; padding-bottom: 5px; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        .grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
        .status-ok { color: #28a745; font-weight: bold; }
        .status-error { color: #dc3545; font-weight: bold; }
        .status-warning { color: #ffc107; font-weight: bold; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .file-check { margin: 5px 0; }
        .version-mismatch { background: #ffebee; padding: 5px; border-radius: 3px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Deal4u Theme Troubleshooting Report</h1>
        <p><strong>Generated:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
        <p><strong>Domain:</strong> <?php echo home_url(); ?></p>

        <?php
        // Function to check file existence and get info
        function check_file($path, $description) {
            if (file_exists($path)) {
                $size = filesize($path);
                $modified = date('Y-m-d H:i:s', filemtime($path));
                $hash = substr(md5_file($path), 0, 8);
                return [
                    'exists' => true,
                    'size' => $size,
                    'modified' => $modified,
                    'hash' => $hash,
                    'description' => $description
                ];
            }
            return ['exists' => false, 'description' => $description];
        }

        // Function to get WordPress and plugin versions
        function get_versions() {
            global $wp_version;
            $versions = [
                'wordpress' => $wp_version,
                'php' => PHP_VERSION,
                'mysql' => function_exists('mysqli_get_server_info') ? mysqli_get_server_info() : 'Unknown'
            ];
            
            if (class_exists('WooCommerce')) {
                $versions['woocommerce'] = WC()->version;
            }
            
            return $versions;
        }

        // Function to check theme files
        function check_theme_files() {
            $theme_dir = get_template_directory();
            $files_to_check = [
                'style.css' => 'Main theme stylesheet',
                'functions.php' => 'Theme functions and hooks',
                'index.php' => 'Homepage template',
                'header.php' => 'Site header template',
                'footer.php' => 'Site footer template',
                'js/theme.js' => 'Main theme JavaScript',
                'woocommerce/archive-product.php' => 'Shop page template',
                'woocommerce/content-product.php' => 'Product card template',
                'woocommerce/single-product.php' => 'Single product template'
            ];
            
            $results = [];
            foreach ($files_to_check as $file => $description) {
                $full_path = $theme_dir . '/' . $file;
                $results[$file] = check_file($full_path, $description);
            }
            
            return $results;
        }

        // Function to check for errors in logs
        function check_error_logs() {
            $log_files = [
                WP_CONTENT_DIR . '/debug.log',
                ini_get('error_log'),
                '/var/log/apache2/error.log',
                '/var/log/nginx/error.log'
            ];
            
            $errors = [];
            foreach ($log_files as $log_file) {
                if ($log_file && file_exists($log_file) && is_readable($log_file)) {
                    $content = file_get_contents($log_file);
                    if (strpos($content, 'deal4u') !== false || strpos($content, 'Deal4u') !== false) {
                        $lines = explode("\n", $content);
                        $recent_errors = array_slice($lines, -50); // Last 50 lines
                        $errors[$log_file] = array_filter($recent_errors, function($line) {
                            return stripos($line, 'deal4u') !== false && 
                                   (stripos($line, 'error') !== false || stripos($line, 'fatal') !== false);
                        });
                    }
                }
            }
            
            return $errors;
        }

        // Function to check database for theme-related data
        function check_database() {
            global $wpdb;
            
            $results = [];
            
            // Check active theme
            $active_theme = get_option('stylesheet');
            $results['active_theme'] = $active_theme;
            
            // Check theme options
            $theme_options = $wpdb->get_results(
                "SELECT option_name, option_value FROM {$wpdb->options} 
                 WHERE option_name LIKE '%deal4u%' OR option_name LIKE '%theme%'"
            );
            $results['theme_options'] = $theme_options;
            
            // Check for WooCommerce products
            if (class_exists('WooCommerce')) {
                $product_count = wp_count_posts('product');
                $results['product_count'] = $product_count;
                
                // Check for products on sale
                $sale_products = $wpdb->get_var(
                    "SELECT COUNT(*) FROM {$wpdb->postmeta} 
                     WHERE meta_key = '_sale_price' AND meta_value != ''"
                );
                $results['sale_products'] = $sale_products;
            }
            
            return $results;
        }

        // Start diagnostics
        echo '<div class="section info">';
        echo '<h2>🔍 System Information</h2>';
        $versions = get_versions();
        echo '<div class="grid">';
        echo '<div>';
        echo '<h3>Software Versions</h3>';
        foreach ($versions as $software => $version) {
            echo "<div><strong>" . ucfirst($software) . ":</strong> $version</div>";
        }
        echo '</div>';
        echo '<div>';
        echo '<h3>Server Information</h3>';
        echo '<div><strong>Server:</strong> ' . $_SERVER['SERVER_SOFTWARE'] . '</div>';
        echo '<div><strong>Document Root:</strong> ' . $_SERVER['DOCUMENT_ROOT'] . '</div>';
        echo '<div><strong>WordPress Path:</strong> ' . ABSPATH . '</div>';
        echo '<div><strong>Theme Directory:</strong> ' . get_template_directory() . '</div>';
        echo '</div>';
        echo '</div>';
        echo '</div>';

        // Check active theme
        echo '<div class="section">';
        echo '<h2>🎨 Active Theme Information</h2>';
        $current_theme = wp_get_theme();
        echo '<div class="grid">';
        echo '<div>';
        echo '<h3>Current Theme</h3>';
        echo '<div><strong>Name:</strong> ' . $current_theme->get('Name') . '</div>';
        echo '<div><strong>Version:</strong> ' . $current_theme->get('Version') . '</div>';
        echo '<div><strong>Directory:</strong> ' . $current_theme->get_stylesheet() . '</div>';
        echo '<div><strong>Path:</strong> ' . $current_theme->get_stylesheet_directory() . '</div>';
        echo '</div>';
        echo '<div>';
        echo '<h3>Theme Support</h3>';
        $supports = [
            'woocommerce' => 'WooCommerce',
            'post-thumbnails' => 'Featured Images',
            'title-tag' => 'Title Tag',
            'custom-logo' => 'Custom Logo'
        ];
        foreach ($supports as $feature => $label) {
            $status = current_theme_supports($feature) ? 
                '<span class="status-ok">✓ Supported</span>' : 
                '<span class="status-error">✗ Not Supported</span>';
            echo "<div><strong>$label:</strong> $status</div>";
        }
        echo '</div>';
        echo '</div>';
        echo '</div>';

        // Check theme files
        echo '<div class="section">';
        echo '<h2>📁 Theme Files Status</h2>';
        $theme_files = check_theme_files();
        echo '<table>';
        echo '<tr><th>File</th><th>Status</th><th>Size</th><th>Last Modified</th><th>Hash</th></tr>';
        foreach ($theme_files as $file => $info) {
            $status = $info['exists'] ? 
                '<span class="status-ok">✓ Exists</span>' : 
                '<span class="status-error">✗ Missing</span>';
            $size = $info['exists'] ? number_format($info['size']) . ' bytes' : '-';
            $modified = $info['exists'] ? $info['modified'] : '-';
            $hash = $info['exists'] ? $info['hash'] : '-';
            echo "<tr><td><strong>$file</strong><br><small>{$info['description']}</small></td>";
            echo "<td>$status</td><td>$size</td><td>$modified</td><td><code>$hash</code></td></tr>";
        }
        echo '</table>';
        echo '</div>';
        ?>

        <div class="section">
            <h2>🔌 Plugin Status</h2>
            <div class="grid">
                <div>
                    <h3>Active Plugins</h3>
                    <?php
                    $active_plugins = get_option('active_plugins');
                    foreach ($active_plugins as $plugin) {
                        $plugin_data = get_plugin_data(WP_PLUGIN_DIR . '/' . $plugin);
                        echo '<div class="file-check">';
                        echo '<strong>' . $plugin_data['Name'] . '</strong> v' . $plugin_data['Version'];
                        echo '</div>';
                    }
                    ?>
                </div>
                <div>
                    <h3>Required Plugins Status</h3>
                    <?php
                    $required_plugins = [
                        'WooCommerce' => class_exists('WooCommerce'),
                        'WordPress SEO' => class_exists('WPSEO_Options'),
                        'Contact Form 7' => class_exists('WPCF7')
                    ];
                    foreach ($required_plugins as $plugin => $active) {
                        $status = $active ? 
                            '<span class="status-ok">✓ Active</span>' : 
                            '<span class="status-warning">⚠ Not Active</span>';
                        echo "<div><strong>$plugin:</strong> $status</div>";
                    }
                    ?>
                </div>
            </div>
        </div>

        <?php
        // Check database
        echo '<div class="section">';
        echo '<h2>🗄️ Database Status</h2>';
        $db_info = check_database();
        echo '<div class="grid">';
        echo '<div>';
        echo '<h3>Theme Configuration</h3>';
        echo '<div><strong>Active Theme:</strong> ' . $db_info['active_theme'] . '</div>';
        if (isset($db_info['product_count'])) {
            echo '<div><strong>Total Products:</strong> ' . $db_info['product_count']->publish . '</div>';
            echo '<div><strong>Products on Sale:</strong> ' . $db_info['sale_products'] . '</div>';
        }
        echo '</div>';
        echo '<div>';
        echo '<h3>Theme Options</h3>';
        if (!empty($db_info['theme_options'])) {
            foreach (array_slice($db_info['theme_options'], 0, 10) as $option) {
                $value = strlen($option->option_value) > 50 ? 
                    substr($option->option_value, 0, 50) . '...' : 
                    $option->option_value;
                echo '<div><strong>' . $option->option_name . ':</strong> ' . esc_html($value) . '</div>';
            }
        } else {
            echo '<div>No theme-specific options found</div>';
        }
        echo '</div>';
        echo '</div>';
        echo '</div>';

        // Check for errors
        echo '<div class="section">';
        echo '<h2>🚨 Error Log Analysis</h2>';
        $error_logs = check_error_logs();
        if (!empty($error_logs)) {
            foreach ($error_logs as $log_file => $errors) {
                if (!empty($errors)) {
                    echo '<h3>Errors in: ' . basename($log_file) . '</h3>';
                    echo '<pre>';
                    foreach (array_slice($errors, -10) as $error) {
                        echo esc_html($error) . "\n";
                    }
                    echo '</pre>';
                }
            }
        } else {
            echo '<div class="success">✓ No Deal4u-related errors found in accessible log files</div>';
        }
        echo '</div>';

        // Performance check
        echo '<div class="section">';
        echo '<h2>⚡ Performance Check</h2>';
        echo '<div class="grid">';
        echo '<div>';
        echo '<h3>Memory Usage</h3>';
        echo '<div><strong>Current:</strong> ' . number_format(memory_get_usage(true) / 1024 / 1024, 2) . ' MB</div>';
        echo '<div><strong>Peak:</strong> ' . number_format(memory_get_peak_usage(true) / 1024 / 1024, 2) . ' MB</div>';
        echo '<div><strong>Limit:</strong> ' . ini_get('memory_limit') . '</div>';
        echo '</div>';
        echo '<div>';
        echo '<h3>Cache Status</h3>';
        $cache_plugins = [
            'W3 Total Cache' => class_exists('W3_Plugin_TotalCache'),
            'WP Super Cache' => function_exists('wp_cache_is_enabled'),
            'WP Rocket' => class_exists('WP_Rocket\\Engine\\Optimization\\LazyRenderContent\\Frontend\\Processor\\Dom'),
            'Object Cache' => wp_using_ext_object_cache()
        ];
        foreach ($cache_plugins as $cache => $active) {
            $status = $active ? 
                '<span class="status-ok">✓ Active</span>' : 
                '<span class="status-warning">⚠ Not Active</span>';
            echo "<div><strong>$cache:</strong> $status</div>";
        }
        echo '</div>';
        echo '</div>';
        echo '</div>';
        ?>

        <div class="section warning">
            <h2>🔧 Recommended Actions</h2>
            <ol>
                <li><strong>File Synchronization:</strong> Compare file hashes between local and live environments</li>
                <li><strong>Clear Cache:</strong> Clear all caching plugins and server-side cache</li>
                <li><strong>Check Error Logs:</strong> Review recent error logs for theme-related issues</li>
                <li><strong>Plugin Conflicts:</strong> Temporarily deactivate plugins to identify conflicts</li>
                <li><strong>Database Cleanup:</strong> Remove orphaned theme options and transients</li>
                <li><strong>File Permissions:</strong> Ensure proper file permissions (644 for files, 755 for directories)</li>
            </ol>
        </div>

        <div class="section info">
            <h2>📋 Quick Fixes</h2>
            <h3>If Timer Not Working:</h3>
            <pre>// Add to functions.php
add_action('wp_footer', function() {
    if (is_front_page()) {
        echo '&lt;script&gt;/* Timer JavaScript code */&lt;/script&gt;';
    }
});</pre>
            
            <h3>If Products Not Showing:</h3>
            <pre>// Check WooCommerce product query
$products = wc_get_products([
    'limit' => 4,
    'status' => 'publish',
    'meta_query' => [
        [
            'key' => '_sale_price',
            'value' => '',
            'compare' => '!='
        ]
    ]
]);</pre>
        </div>

        <div class="section">
            <h2>🔄 File Comparison Tool</h2>
            <p>To compare local vs live files, upload your local functions.php and run:</p>
            <pre>
// Compare file hashes
$local_hash = 'YOUR_LOCAL_FILE_HASH';
$live_hash = md5_file(get_template_directory() . '/functions.php');
echo $local_hash === $live_hash ? 'Files match' : 'Files differ';
            </pre>
        </div>

        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
            <p><strong>Report completed at:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>
            <p><strong>Next steps:</strong> Review each section above and address any issues marked in red or yellow.</p>
        </div>
    </div>
</body>
</html>
