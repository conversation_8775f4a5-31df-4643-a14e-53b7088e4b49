<?php
/**
 * Template Name: Login Page
 */

// SECURITY: Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header(); ?>

<main class="main-content">
    <!-- <PERSON>gin Header -->
    <section style="padding: 3rem 0; background: linear-gradient(to right, #2563eb, #9333ea); color: white;">
        <div class="container">
            <div style="text-align: center;">
                <h1 style="font-size: 3rem; font-weight: bold; margin-bottom: 1rem;">Welcome Back</h1>
                <p style="font-size: 1.25rem; color: #bfdbfe;">Sign in to your Deal4u account</p>
            </div>
        </div>
    </section>

    <!-- Login Form -->
    <section style="padding: 4rem 0; background: #f9fafb;">
        <div class="container">
            <div style="max-width: 28rem; margin: 0 auto;">
                
                <!-- Login Card -->
                <div style="background: white; padding: 3rem; border-radius: 1rem; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);">
                    <div style="text-align: center; margin-bottom: 2rem;">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">🔐</div>
                        <h2 style="font-size: 2rem; font-weight: bold; color: #111827; margin-bottom: 0.5rem;">Sign In</h2>
                        <p style="color: #6b7280;">Access your Deal4u account</p>
                    </div>

                    <?php
                    // Check if user is already logged in
                    if (is_user_logged_in()) {
                        $redirect_url = function_exists('wc_get_page_permalink') ? wc_get_page_permalink('myaccount') : home_url('/my-account/');
                        echo '<div style="background: #22c55e; color: white; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem; text-align: center;">
                                <i class="fas fa-check-circle" style="margin-right: 0.5rem;"></i>
                                You are already logged in! Redirecting to My Account...
                              </div>';

                        // IMPROVED: Use proper WordPress redirect instead of inline JavaScript
                        echo '<meta http-equiv="refresh" content="2;url=' . esc_url($redirect_url) . '">';

                        // Add to footer for better performance
                        add_action('wp_footer', function() use ($redirect_url) {
                            echo '<script>
                                setTimeout(function(){
                                    window.location.href = "' . esc_js($redirect_url) . '";
                                }, 2000);
                            </script>';
                        });
                    } else {
                        // Use WooCommerce login form if available
                        if (function_exists('woocommerce_login_form')) {
                            echo '<style>
                                .woocommerce-form-login {
                                    background: white;
                                    padding: 2rem;
                                    border-radius: 1rem;
                                    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
                                }
                                .woocommerce-form-login .form-row {
                                    margin-bottom: 1rem;
                                }
                                .woocommerce-form-login label {
                                    display: block;
                                    font-weight: 500;
                                    color: #374151;
                                    margin-bottom: 0.5rem;
                                }
                                .woocommerce-form-login input[type="text"],
                                .woocommerce-form-login input[type="email"],
                                .woocommerce-form-login input[type="password"] {
                                    width: 100%;
                                    padding: 0.75rem 1rem;
                                    border: 1px solid #d1d5db;
                                    border-radius: 0.5rem;
                                    font-size: 1rem;
                                    box-sizing: border-box;
                                }
                                .woocommerce-form-login .button {
                                    background: linear-gradient(to right, #2563eb, #9333ea);
                                    color: white;
                                    font-weight: bold;
                                    padding: 0.75rem 1.5rem;
                                    border-radius: 0.5rem;
                                    border: none;
                                    cursor: pointer;
                                    font-size: 1rem;
                                    transition: all 0.3s;
                                    width: 100%;
                                }
                                .woocommerce-form-login .button:hover {
                                    transform: scale(1.02);
                                }
                            </style>';

                            // Custom styled WooCommerce login form
                            $args = array(
                                'message'  => '',
                                'redirect' => function_exists('wc_get_page_permalink') ? wc_get_page_permalink('myaccount') : home_url('/my-account/'),
                                'hidden'   => false
                            );

                            woocommerce_login_form($args);
                        } else {
                            // Fallback to WordPress login if WooCommerce not available
                            echo '<div style="background: #fef3c7; border: 1px solid #f59e0b; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem;">
                                    <p style="color: #92400e; margin: 0;">WooCommerce is not active. Please activate WooCommerce for full functionality.</p>
                                  </div>';

                            // Simple WordPress login form
                            wp_login_form(array(
                                'redirect' => home_url('/my-account/'),
                                'form_id' => 'deal4u-login-form',
                                'label_username' => 'Username or Email',
                                'label_password' => 'Password',
                                'label_remember' => 'Remember Me',
                                'label_log_in' => 'Sign In',
                                'remember' => true
                            ));
                        }
                    }
                    ?>

                    <!-- WooCommerce login form will be displayed above -->

                    <!-- Register Link -->
                    <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #e5e7eb;">
                        <p style="color: #6b7280; margin-bottom: 1rem;">Don't have an account?</p>
                        <a href="<?php echo esc_url(home_url('/register/')); ?>" 
                           style="color: #2563eb; font-weight: 500; text-decoration: none; display: inline-flex; align-items: center; transition: color 0.3s;" 
                           onmouseover="this.style.color='#1d4ed8'" onmouseout="this.style.color='#2563eb'">
                            <i class="fas fa-user-plus" style="margin-right: 0.5rem;"></i>Create Account
                        </a>
                    </div>
                </div>

                <!-- Features -->
                <div style="margin-top: 3rem;">
                    <h3 style="text-align: center; font-size: 1.25rem; font-weight: bold; color: #111827; margin-bottom: 2rem;">Welcome Back to Deal4u</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        
                        <div style="background: white; padding: 1.5rem; border-radius: 0.5rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">🛒</div>
                            <h4 style="font-weight: 600; color: #111827; margin-bottom: 0.5rem;">Your Cart</h4>
                            <p style="color: #6b7280; font-size: 0.875rem;">Continue where you left off</p>
                        </div>

                        <div style="background: white; padding: 1.5rem; border-radius: 0.5rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">📦</div>
                            <h4 style="font-weight: 600; color: #111827; margin-bottom: 0.5rem;">Order History</h4>
                            <p style="color: #6b7280; font-size: 0.875rem;">Track your past orders</p>
                        </div>

                        <div style="background: white; padding: 1.5rem; border-radius: 0.5rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">❤️</div>
                            <h4 style="font-weight: 600; color: #111827; margin-bottom: 0.5rem;">Wishlist</h4>
                            <p style="color: #6b7280; font-size: 0.875rem;">Access your saved items</p>
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </section>
</main>

<?php get_footer(); ?>
