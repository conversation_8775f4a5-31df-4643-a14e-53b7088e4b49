<?php
/**
 * Template Name: Register Page
 */

// SECURITY: Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

get_header(); ?>

<main class="main-content">
    <!-- Register Header -->
    <section style="padding: 3rem 0; background: linear-gradient(to right, #2563eb, #9333ea); color: white;">
        <div class="container">
            <div style="text-align: center;">
                <h1 style="font-size: 3rem; font-weight: bold; margin-bottom: 1rem;">Create Account</h1>
                <p style="font-size: 1.25rem; color: #bfdbfe;">Join Deal4u and start shopping today!</p>
            </div>
        </div>
    </section>

    <!-- Register Form -->
    <section style="padding: 4rem 0; background: #f9fafb;">
        <div class="container">
            <div style="max-width: 28rem; margin: 0 auto;">
                
                <!-- Register Card -->
                <div style="background: white; padding: 3rem; border-radius: 1rem; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);">
                    <div style="text-align: center; margin-bottom: 2rem;">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">🛍️</div>
                        <h2 style="font-size: 2rem; font-weight: bold; color: #111827; margin-bottom: 0.5rem;">Join Deal4u</h2>
                        <p style="color: #6b7280;">Create your account to access exclusive deals</p>
                    </div>

                    <?php
                    // Check if user is already logged in
                    if (is_user_logged_in()) {
                        echo '<div style="background: #22c55e; color: white; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1rem; text-align: center;">
                                <i class="fas fa-check-circle" style="margin-right: 0.5rem;"></i>
                                You are already logged in! Redirecting to My Account...
                              </div>';
                        // FIXED: Use our custom my-account page
                        $redirect_url = home_url('/my-account/');
                        echo '<meta http-equiv="refresh" content="2;url=' . esc_url($redirect_url) . '">';

                        add_action('wp_footer', function() use ($redirect_url) {
                            echo '<script>
                                setTimeout(function(){
                                    window.location.href = "' . esc_js($redirect_url) . '";
                                }, 2000);
                            </script>';
                        });
                    } else {
                        // FIXED: Custom e-commerce registration form with proper fields

                        // Display errors if any
                        $errors = get_transient('deal4u_registration_errors');
                        if ($errors) {
                            echo '<div style="background: #fef2f2; border: 1px solid #ef4444; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1.5rem;">
                                    <h4 style="color: #dc2626; margin-bottom: 0.5rem;">Registration Errors:</h4>
                                    <ul style="color: #dc2626; margin: 0; padding-left: 1.5rem;">';
                            foreach ($errors as $error) {
                                echo '<li>' . esc_html($error) . '</li>';
                            }
                            echo '</ul></div>';
                            delete_transient('deal4u_registration_errors');
                        } else {
                            echo '<div style="background: #f0fdf4; border: 1px solid #22c55e; padding: 1rem; border-radius: 0.5rem; margin-bottom: 1.5rem; text-align: center;">
                                    <p style="color: #15803d; margin: 0;"><strong>✅ Create Your Deal4u Account</strong> - Join thousands of satisfied customers!</p>
                                  </div>';
                        }

                        // Custom e-commerce registration form
                        ?>
                        <form method="post" action="" style="display: flex; flex-direction: column; gap: 1rem;">
                            <?php wp_nonce_field('deal4u_register', 'deal4u_register_nonce'); ?>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem;">
                                <div>
                                    <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">First Name *</label>
                                    <input type="text" name="first_name" style="width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; box-sizing: border-box;" placeholder="John" required value="<?php echo isset($_POST['first_name']) ? esc_attr($_POST['first_name']) : ''; ?>">
                                </div>
                                <div>
                                    <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Last Name *</label>
                                    <input type="text" name="last_name" style="width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; box-sizing: border-box;" placeholder="Doe" required value="<?php echo isset($_POST['last_name']) ? esc_attr($_POST['last_name']) : ''; ?>">
                                </div>
                            </div>

                            <div>
                                <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Email Address *</label>
                                <input type="email" name="email" style="width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; box-sizing: border-box;" placeholder="<EMAIL>" required value="<?php echo isset($_POST['email']) ? esc_attr($_POST['email']) : ''; ?>">
                            </div>

                            <div>
                                <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Password *</label>
                                <input type="password" name="password" style="width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; box-sizing: border-box;" placeholder="Create a secure password" required minlength="6">
                                <p style="font-size: 0.75rem; color: #6b7280; margin-top: 0.25rem;">Minimum 6 characters</p>
                            </div>

                            <div>
                                <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Confirm Password *</label>
                                <input type="password" name="confirm_password" style="width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem; box-sizing: border-box;" placeholder="Confirm your password" required>
                            </div>

                            <div style="display: flex; align-items: flex-start; gap: 0.5rem;">
                                <input type="checkbox" id="terms" name="terms" required style="width: 1rem; height: 1rem; margin-top: 0.25rem; flex-shrink: 0;">
                                <label for="terms" style="font-size: 0.875rem; color: #6b7280; line-height: 1.4;">
                                    I agree to the <a href="<?php echo home_url('/terms/'); ?>" style="color: #2563eb; text-decoration: none;">Terms of Service</a> and <a href="<?php echo home_url('/privacy/'); ?>" style="color: #2563eb; text-decoration: none;">Privacy Policy</a>
                                </label>
                            </div>

                            <div style="display: flex; align-items: flex-start; gap: 0.5rem;">
                                <input type="checkbox" id="newsletter" name="newsletter" style="width: 1rem; height: 1rem; margin-top: 0.25rem; flex-shrink: 0;">
                                <label for="newsletter" style="font-size: 0.875rem; color: #6b7280; line-height: 1.4;">
                                    📧 Subscribe to our newsletter for exclusive deals and updates
                                </label>
                            </div>

                            <button type="submit" name="deal4u_register" style="background: linear-gradient(to right, #2563eb, #9333ea); color: white; font-weight: bold; padding: 1rem 1.5rem; border-radius: 0.5rem; border: none; cursor: pointer; font-size: 1rem; transition: all 0.3s; margin-top: 0.5rem;" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                <i class="fas fa-user-plus" style="margin-right: 0.5rem;"></i>Create My Account
                            </button>
                        </form>
                        <?php
                    } // End of else (not logged in)
                    ?>

                    <!-- Login Link -->
                    <div style="text-align: center; margin-top: 2rem; padding-top: 2rem; border-top: 1px solid #e5e7eb;">
                        <p style="color: #6b7280; margin-bottom: 1rem;">Already have an account?</p>
                        <a href="<?php echo esc_url(home_url('/login/')); ?>" 
                           style="color: #2563eb; font-weight: 500; text-decoration: none; display: inline-flex; align-items: center; transition: color 0.3s;" 
                           onmouseover="this.style.color='#1d4ed8'" onmouseout="this.style.color='#2563eb'">
                            <i class="fas fa-sign-in-alt" style="margin-right: 0.5rem;"></i>Sign In
                        </a>
                    </div>
                </div>

                <!-- Benefits -->
                <div style="margin-top: 3rem;">
                    <h3 style="text-align: center; font-size: 1.25rem; font-weight: bold; color: #111827; margin-bottom: 2rem;">Why Join Deal4u?</h3>
                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
                        
                        <div style="background: white; padding: 1.5rem; border-radius: 0.5rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">🎁</div>
                            <h4 style="font-weight: 600; color: #111827; margin-bottom: 0.5rem;">Exclusive Deals</h4>
                            <p style="color: #6b7280; font-size: 0.875rem;">Access member-only discounts and early sales</p>
                        </div>

                        <div style="background: white; padding: 1.5rem; border-radius: 0.5rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">❤️</div>
                            <h4 style="font-weight: 600; color: #111827; margin-bottom: 0.5rem;">Wishlist</h4>
                            <p style="color: #6b7280; font-size: 0.875rem;">Save your favorite products for later</p>
                        </div>

                        <div style="background: white; padding: 1.5rem; border-radius: 0.5rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); text-align: center;">
                            <div style="font-size: 2rem; margin-bottom: 0.5rem;">📦</div>
                            <h4 style="font-weight: 600; color: #111827; margin-bottom: 0.5rem;">Order Tracking</h4>
                            <p style="color: #6b7280; font-size: 0.875rem;">Track all your orders in one place</p>
                        </div>

                    </div>
                </div>

            </div>
        </div>
    </section>
</main>

<?php get_footer(); ?>
