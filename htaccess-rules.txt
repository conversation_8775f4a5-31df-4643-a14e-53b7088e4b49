# Add these rules to your .htaccess file to handle login/register redirects
# Place these BEFORE the WordPress rewrite rules

# Redirect /login/ to /my-account/
RewriteRule ^login/?$ /my-account/ [R=301,L]

# Redirect /register/ to /my-account/
RewriteRule ^register/?$ /my-account/ [R=301,L]

# Instructions:
# 1. Open your .htaccess file in the WordPress root directory
# 2. Add the above rules BEFORE the "# BEGIN WordPress" section
# 3. Save the file
# 4. Test the redirects

# Your .htaccess should look like this:
# 
# # Redirect /login/ to /my-account/
# RewriteRule ^login/?$ /my-account/ [R=301,L]
# 
# # Redirect /register/ to /my-account/
# RewriteRule ^register/?$ /my-account/ [R=301,L]
# 
# # BEGIN WordPress
# <IfModule mod_rewrite.c>
# RewriteEngine On
# RewriteBase /
# RewriteRule ^index\.php$ - [L]
# RewriteCond %{REQUEST_FILENAME} !-f
# RewriteCond %{REQUEST_FILENAME} !-d
# RewriteRule . /index.php [L]
# </IfModule>
# # END WordPress
