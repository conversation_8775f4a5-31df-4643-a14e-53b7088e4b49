/**
 * Deal4u Laravel Match Theme JavaScript
 * Simple and clean JavaScript for theme functionality
 */

document.addEventListener('DOMContentLoaded', function() {
    
    // Search dropdown toggle
    const searchToggle = document.querySelector('.search-toggle');
    const searchDropdown = document.querySelector('.search-dropdown');
    
    if (searchToggle && searchDropdown) {
        searchToggle.addEventListener('click', function(e) {
            e.preventDefault();
            searchDropdown.classList.toggle('active');
            
            // Focus on search input when opened
            if (searchDropdown.classList.contains('active')) {
                const searchInput = searchDropdown.querySelector('.search-input');
                if (searchInput) {
                    searchInput.focus();
                }
            }
        });
        
        // Close dropdown when clicking outside
        document.addEventListener('click', function(e) {
            if (!searchToggle.contains(e.target) && !searchDropdown.contains(e.target)) {
                searchDropdown.classList.remove('active');
            }
        });
    }
    
    // Mobile menu toggle
    const mobileToggle = document.querySelector('.mobile-menu-toggle');
    const mobileMenu = document.querySelector('.mobile-menu');
    
    if (mobileToggle && mobileMenu) {
        mobileToggle.addEventListener('click', function(e) {
            e.preventDefault();
            mobileMenu.classList.toggle('active');
            
            // Toggle icon
            const icon = mobileToggle.querySelector('i');
            if (icon) {
                if (mobileMenu.classList.contains('active')) {
                    icon.className = 'fas fa-times';
                } else {
                    icon.className = 'fas fa-bars';
                }
            }
        });
        
        // Close mobile menu when clicking on a link
        const mobileLinks = mobileMenu.querySelectorAll('.mobile-nav-link');
        mobileLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.remove('active');
                const icon = mobileToggle.querySelector('i');
                if (icon) {
                    icon.className = 'fas fa-bars';
                }
            });
        });
    }
    
    // Smooth scrolling for anchor links
    const anchorLinks = document.querySelectorAll('a[href^="#"]');
    anchorLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            const href = this.getAttribute('href');
            if (href === '#') return;
            
            const target = document.querySelector(href);
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });
    
    // Header scroll effect
    let lastScrollTop = 0;
    const header = document.querySelector('.site-header');
    
    if (header) {
        window.addEventListener('scroll', function() {
            const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
            
            if (scrollTop > 100) {
                header.classList.add('scrolled');
            } else {
                header.classList.remove('scrolled');
            }
            
            lastScrollTop = scrollTop;
        });
    }
    
    // Add loading state to buttons
    const buttons = document.querySelectorAll('.btn, .search-button');
    buttons.forEach(button => {
        button.addEventListener('click', function() {
            if (this.type === 'submit' || this.tagName === 'BUTTON') {
                const originalText = this.textContent;
                this.textContent = 'Loading...';
                this.disabled = true;
                
                // Reset after 3 seconds (adjust as needed)
                setTimeout(() => {
                    this.textContent = originalText;
                    this.disabled = false;
                }, 3000);
            }
        });
    });
    
    // Simple fade in animation for elements
    function fadeInElements() {
        const elements = document.querySelectorAll('.fade-in');
        elements.forEach(element => {
            const elementTop = element.offsetTop;
            const elementBottom = elementTop + element.offsetHeight;
            const viewportTop = window.pageYOffset;
            const viewportBottom = viewportTop + window.innerHeight;
            
            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                element.classList.add('visible');
            }
        });
    }
    
    // Run fade in on scroll and load
    window.addEventListener('scroll', fadeInElements);
    fadeInElements();
    
    // Simple notification system
    function showNotification(message, type = 'success') {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;
        notification.textContent = message;
        
        // Add styles
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'success' ? '#22c55e' : '#ef4444'};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 0.5rem;
            box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
            z-index: 1000;
            transform: translateX(100%);
            transition: transform 0.3s ease;
        `;
        
        document.body.appendChild(notification);
        
        // Show notification
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);
        
        // Hide notification after 3 seconds
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }
    
    // Example usage for forms
    const forms = document.querySelectorAll('form');
    forms.forEach(form => {
        form.addEventListener('submit', function(e) {
            // You can add form validation here
            // showNotification('Form submitted successfully!');
        });
    });
    
    // Scroll to top functionality
    window.addEventListener('scroll', function() {
        const scrollToTop = document.querySelector('.scroll-to-top');
        if (scrollToTop) {
            if (window.pageYOffset > 100) {
                scrollToTop.style.display = 'block';
            } else {
                scrollToTop.style.display = 'none';
            }
        }
    });
    
    const scrollToTopBtn = document.querySelector('.scroll-to-top');
    if (scrollToTopBtn) {
        scrollToTopBtn.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
    
    // Simple image lazy loading
    function lazyLoadImages() {
        const images = document.querySelectorAll('img[data-src]');
        images.forEach(img => {
            if (isElementInViewport(img)) {
                img.src = img.getAttribute('data-src');
                img.removeAttribute('data-src');
            }
        });
    }
    
    function isElementInViewport(el) {
        const rect = el.getBoundingClientRect();
        return (
            rect.top >= 0 &&
            rect.left >= 0 &&
            rect.bottom <= (window.innerHeight || document.documentElement.clientHeight) &&
            rect.right <= (window.innerWidth || document.documentElement.clientWidth)
        );
    }
    
    // Run lazy loading on scroll and load
    window.addEventListener('scroll', lazyLoadImages);
    window.addEventListener('load', lazyLoadImages);
    lazyLoadImages();

    // SIMPLIFIED: Let WooCommerce handle add to cart natively
    // Only handle shop page AJAX add to cart for simple products
    document.addEventListener('click', function(e) {
        const button = e.target.closest('a');
        if (button && button.classList.contains('ajax_add_to_cart') && !button.closest('.single-product')) {
            // Only intercept on shop/archive pages, not single product pages
            e.preventDefault();

            // Show loading state
            const originalText = button.innerHTML;
            button.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Adding...';
            button.style.opacity = '0.7';
            button.style.pointerEvents = 'none';

            // Use the button's href for add to cart
            fetch(button.href)
            .then(response => {
                if (response.ok) {
                    Deal4u.showNotification('✅ Product added to cart!', 'success');

                    // Update cart count
                    const cartCount = document.querySelector('.cart-count');
                    if (cartCount) {
                        const currentCount = parseInt(cartCount.textContent) || 0;
                        cartCount.textContent = currentCount + 1;
                    }
                } else {
                    Deal4u.showNotification('❌ Error adding to cart. Please try again.', 'error');
                }
            })
            .catch(error => {
                console.error('Add to cart error:', error);
                Deal4u.showNotification('❌ Error adding to cart. Please try again.', 'error');
            })
            .finally(() => {
                // Reset button state
                button.innerHTML = originalText;
                button.style.opacity = '1';
                button.style.pointerEvents = 'auto';
            });
            }
        }
    });

    // Quick View functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.quick-view-btn')) {
            e.preventDefault();
            const button = e.target.closest('.quick-view-btn');
            const productId = button.getAttribute('data-product-id');

            if (productId) {
                showQuickView(productId);
            }
        }
    });

    // Wishlist functionality
    document.addEventListener('click', function(e) {
        if (e.target.closest('.wishlist-btn')) {
            e.preventDefault();
            const button = e.target.closest('.wishlist-btn');
            const productId = button.getAttribute('data-product-id');

            if (productId) {
                toggleWishlist(productId, button);
            }
        }
    });

    // Quick View Modal
    function showQuickView(productId) {
        // Create modal overlay
        const overlay = document.createElement('div');
        overlay.className = 'quick-view-overlay';
        overlay.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 9999;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        `;

        // Create modal content
        const modal = document.createElement('div');
        modal.className = 'quick-view-modal';
        modal.style.cssText = `
            background: white;
            border-radius: 12px;
            max-width: 800px;
            width: 90%;
            max-height: 90%;
            overflow-y: auto;
            position: relative;
            transform: scale(0.8);
            transition: transform 0.3s ease;
        `;

        // Add loading content
        modal.innerHTML = `
            <div class="p-8 text-center">
                <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
                <p class="text-gray-600">Loading product details...</p>
            </div>
        `;

        // Close button
        const closeBtn = document.createElement('button');
        closeBtn.innerHTML = '×';
        closeBtn.style.cssText = `
            position: absolute;
            top: 15px;
            right: 20px;
            background: none;
            border: none;
            font-size: 30px;
            color: #666;
            cursor: pointer;
            z-index: 10;
        `;
        closeBtn.onclick = () => closeQuickView(overlay);

        modal.appendChild(closeBtn);
        overlay.appendChild(modal);
        document.body.appendChild(overlay);

        // Show modal with animation
        setTimeout(() => {
            overlay.style.opacity = '1';
            modal.style.transform = 'scale(1)';
        }, 10);

        // Load product data (simplified version)
        loadQuickViewContent(productId, modal);

        // Close on overlay click
        overlay.addEventListener('click', function(e) {
            if (e.target === overlay) {
                closeQuickView(overlay);
            }
        });
    }

    function loadQuickViewContent(productId, modal) {
        // In a real implementation, you would fetch product data via AJAX
        // For now, we'll create a basic product view
        setTimeout(() => {
            modal.innerHTML = `
                <button onclick="this.parentElement.parentElement.remove()" style="position: absolute; top: 15px; right: 20px; background: none; border: none; font-size: 30px; color: #666; cursor: pointer;">×</button>
                <div class="p-8">
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                        <div class="text-center">
                            <div class="bg-gray-200 rounded-lg h-64 flex items-center justify-center mb-4">
                                <span class="text-4xl">📦</span>
                            </div>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900 mb-4">Product Quick View</h2>
                            <p class="text-gray-600 mb-4">Product ID: ${productId}</p>
                            <div class="text-2xl font-bold text-blue-600 mb-6">$99.99</div>
                            <div class="space-y-3">
                                <button class="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300">
                                    Add to Cart
                                </button>
                                <a href="/product/${productId}" class="block w-full bg-gray-600 hover:bg-gray-700 text-white font-bold py-3 px-6 rounded-lg text-center transition-all duration-300">
                                    View Full Details
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }, 1000);
    }

    function closeQuickView(overlay) {
        overlay.style.opacity = '0';
        const modal = overlay.querySelector('.quick-view-modal');
        if (modal) {
            modal.style.transform = 'scale(0.8)';
        }
        setTimeout(() => {
            if (overlay.parentNode) {
                overlay.parentNode.removeChild(overlay);
            }
        }, 300);
    }

    // Wishlist functionality
    function toggleWishlist(productId, button) {
        // Get current wishlist from localStorage
        let wishlist = JSON.parse(localStorage.getItem('wishlist_items') || '[]');
        const isInWishlist = wishlist.includes(productId);

        if (isInWishlist) {
            // Remove from wishlist
            wishlist = wishlist.filter(id => id !== productId);
            button.innerHTML = '🤍';
            button.classList.remove('in-wishlist');
            Deal4u.showNotification('💔 Removed from wishlist', 'info');
        } else {
            // Add to wishlist
            wishlist.push(productId);
            button.innerHTML = '❤️';
            button.classList.add('in-wishlist');
            Deal4u.showNotification('💖 Added to wishlist!', 'success');
        }

        // Save to localStorage
        localStorage.setItem('wishlist_items', JSON.stringify(wishlist));

        // Update wishlist count in header
        updateWishlistCount();
    }

    function updateWishlistCount() {
        const wishlist = JSON.parse(localStorage.getItem('wishlist_items') || '[]');
        const wishlistCountElements = document.querySelectorAll('.wishlist-count');
        wishlistCountElements.forEach(element => {
            element.textContent = wishlist.length;
        });
    }

    // Initialize wishlist on page load
    updateWishlistCount();

    // Mark wishlist items as active on page load
    const wishlist = JSON.parse(localStorage.getItem('wishlist_items') || '[]');
    document.querySelectorAll('.wishlist-btn').forEach(button => {
        const productId = button.getAttribute('data-product-id');
        if (wishlist.includes(productId)) {
            button.innerHTML = '❤️';
            button.classList.add('in-wishlist');
        }
    });

    // PROFESSIONAL ENHANCEMENTS

    // Enhanced scroll animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe all animatable elements
    document.querySelectorAll('.fade-in, .product-card, .feature-card').forEach(el => {
        observer.observe(el);
    });

    // Enhanced loading states
    function showEnhancedLoading(element, text = 'Loading...') {
        if (element) {
            element.classList.add('loading-skeleton');
            element.setAttribute('aria-busy', 'true');
            element.setAttribute('aria-label', text);
        }
    }

    function hideEnhancedLoading(element) {
        if (element) {
            element.classList.remove('loading-skeleton');
            element.removeAttribute('aria-busy');
            element.removeAttribute('aria-label');
        }
    }

    // Professional form validation
    function validateForm(form) {
        const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
        let isValid = true;

        inputs.forEach(input => {
            const value = input.value.trim();
            const errorElement = input.parentNode.querySelector('.error-message');

            // Remove existing error
            if (errorElement) {
                errorElement.remove();
            }
            input.classList.remove('error');

            // Validate
            if (!value) {
                showFieldError(input, 'This field is required');
                isValid = false;
            } else if (input.type === 'email' && !Deal4u.validateEmail(value)) {
                showFieldError(input, 'Please enter a valid email address');
                isValid = false;
            }
        });

        return isValid;
    }

    function showFieldError(input, message) {
        input.classList.add('error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message text-red-500 text-sm mt-1';
        errorDiv.textContent = message;
        input.parentNode.appendChild(errorDiv);
    }

    // Enhanced form handling
    document.querySelectorAll('form').forEach(form => {
        form.addEventListener('submit', function(e) {
            if (!validateForm(this)) {
                e.preventDefault();
                Deal4u.showNotification('Please fix the errors below', 'error');
            }
        });
    });

    // Professional image lazy loading with fade-in
    function lazyLoadImagesEnhanced() {
        const images = document.querySelectorAll('img[data-src]');

        const imageObserver = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.style.opacity = '0';
                    img.src = img.getAttribute('data-src');
                    img.removeAttribute('data-src');

                    img.onload = () => {
                        img.style.transition = 'opacity 0.3s ease';
                        img.style.opacity = '1';
                    };

                    imageObserver.unobserve(img);
                }
            });
        });

        images.forEach(img => imageObserver.observe(img));
    }

    lazyLoadImagesEnhanced();

    // Flash Sale Countdown Timer
    function initFlashSaleTimer() {
        const hoursElement = document.getElementById('hours');
        const minutesElement = document.getElementById('minutes');
        const secondsElement = document.getElementById('seconds');

        if (!hoursElement || !minutesElement || !secondsElement) {
            return; // Timer elements not found on this page
        }

        // Set end time to 24 hours from now (you can customize this)
        const endTime = new Date().getTime() + (24 * 60 * 60 * 1000);

        function updateTimer() {
            const now = new Date().getTime();
            const timeLeft = endTime - now;

            if (timeLeft > 0) {
                const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

                hoursElement.textContent = hours.toString().padStart(2, '0');
                minutesElement.textContent = minutes.toString().padStart(2, '0');
                secondsElement.textContent = seconds.toString().padStart(2, '0');

                // Add pulse animation when time is running low
                if (timeLeft < 3600000) { // Less than 1 hour
                    hoursElement.parentElement.classList.add('animate-pulse');
                    minutesElement.parentElement.classList.add('animate-pulse');
                    secondsElement.parentElement.classList.add('animate-pulse');
                }
            } else {
                // Timer expired - reset to 24 hours
                const newEndTime = new Date().getTime() + (24 * 60 * 60 * 1000);
                localStorage.setItem('flashSaleEndTime', newEndTime);
                hoursElement.textContent = '24';
                minutesElement.textContent = '00';
                secondsElement.textContent = '00';
            }
        }

        // Update timer immediately and then every second
        updateTimer();
        setInterval(updateTimer, 1000);
    }

    // Initialize flash sale timer
    initFlashSaleTimer();

    // Enhanced cart count animation
    function animateCartCount() {
        const cartCount = document.querySelector('.cart-count');
        if (cartCount) {
            cartCount.style.transform = 'scale(1.3)';
            cartCount.style.transition = 'transform 0.2s ease';
            setTimeout(() => {
                cartCount.style.transform = 'scale(1)';
            }, 200);
        }
    }

    // Professional error handling
    window.addEventListener('error', function(e) {
        console.error('Theme Error:', e.error);
        // Don't show errors to users in production
        if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
            Deal4u.showNotification('A technical error occurred. Please refresh the page.', 'error');
        }
    });

    // Enhanced performance monitoring
    if ('performance' in window) {
        window.addEventListener('load', function() {
            setTimeout(() => {
                const perfData = performance.getEntriesByType('navigation')[0];
                if (perfData && perfData.loadEventEnd > 3000) {
                    console.warn('Page load time is slow:', perfData.loadEventEnd + 'ms');
                }
            }, 0);
        });
    }

});

// Simple utility functions
window.Deal4u = {
    
    // Show loading state
    showLoading: function(element) {
        if (element) {
            element.classList.add('loading');
            element.disabled = true;
        }
    },
    
    // Hide loading state
    hideLoading: function(element) {
        if (element) {
            element.classList.remove('loading');
            element.disabled = false;
        }
    },
    
    // Format price
    formatPrice: function(price) {
        return '$' + parseFloat(price).toFixed(2);
    },
    
    // Simple validation
    validateEmail: function(email) {
        const re = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return re.test(email);
    },
    
    // Enhanced notification system
    showNotification: function(message, type = 'success', duration = 3000) {
        const notification = document.createElement('div');
        notification.className = `notification notification-${type}`;

        // Enhanced notification with icon and close button
        const icon = type === 'success' ? '✅' : type === 'error' ? '❌' : type === 'info' ? 'ℹ️' : '⚠️';

        notification.innerHTML = `
            <div style="display: flex; align-items: center; gap: 0.75rem;">
                <span style="font-size: 1.25rem;">${icon}</span>
                <span style="flex: 1;">${message}</span>
                <button onclick="this.parentElement.parentElement.remove()" style="background: none; border: none; color: inherit; font-size: 1.25rem; cursor: pointer; opacity: 0.7; hover:opacity: 1;">×</button>
            </div>
        `;

        const colors = {
            success: { bg: '#22c55e', border: '#16a34a' },
            error: { bg: '#ef4444', border: '#dc2626' },
            warning: { bg: '#f59e0b', border: '#d97706' },
            info: { bg: '#3b82f6', border: '#2563eb' }
        };

        const color = colors[type] || colors.info;

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${color.bg};
            border-left: 4px solid ${color.border};
            color: white;
            padding: 1rem 1.5rem;
            border-radius: 0.75rem;
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
            z-index: 9999;
            transform: translateX(100%);
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            max-width: 400px;
            min-width: 300px;
            backdrop-filter: blur(10px);
        `;

        document.body.appendChild(notification);

        // Animate in
        setTimeout(() => {
            notification.style.transform = 'translateX(0)';
        }, 100);

        // Auto remove
        setTimeout(() => {
            notification.style.transform = 'translateX(100%)';
            notification.style.opacity = '0';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, duration);

        return notification;
    },

    // Enhanced loading states
    showEnhancedLoading: function(element, text = 'Loading...') {
        if (element) {
            element.classList.add('loading-skeleton');
            element.setAttribute('aria-busy', 'true');
            element.setAttribute('aria-label', text);

            // Add loading overlay for buttons
            if (element.tagName === 'BUTTON' || element.classList.contains('btn')) {
                const originalText = element.innerHTML;
                element.setAttribute('data-original-text', originalText);
                element.innerHTML = `
                    <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    ${text}
                `;
                element.disabled = true;
            }
        }
    },

    hideEnhancedLoading: function(element) {
        if (element) {
            element.classList.remove('loading-skeleton');
            element.removeAttribute('aria-busy');
            element.removeAttribute('aria-label');

            // Restore button text
            if (element.hasAttribute('data-original-text')) {
                element.innerHTML = element.getAttribute('data-original-text');
                element.removeAttribute('data-original-text');
                element.disabled = false;
            }
        }
    },

    // Professional form validation
    validateForm: function(form) {
        const inputs = form.querySelectorAll('input[required], textarea[required], select[required]');
        let isValid = true;
        const errors = [];

        inputs.forEach(input => {
            const value = input.value.trim();
            const label = input.getAttribute('placeholder') || input.getAttribute('name') || 'Field';

            // Remove existing error styling
            input.classList.remove('border-red-500', 'error');
            const existingError = input.parentNode.querySelector('.error-message');
            if (existingError) existingError.remove();

            // Validate
            if (!value) {
                this.showFieldError(input, `${label} is required`);
                errors.push(`${label} is required`);
                isValid = false;
            } else if (input.type === 'email' && !this.validateEmail(value)) {
                this.showFieldError(input, 'Please enter a valid email address');
                errors.push('Invalid email format');
                isValid = false;
            } else if (input.type === 'tel' && value.length < 10) {
                this.showFieldError(input, 'Please enter a valid phone number');
                errors.push('Invalid phone number');
                isValid = false;
            }
        });

        return { isValid, errors };
    },

    showFieldError: function(input, message) {
        input.classList.add('border-red-500', 'error');
        const errorDiv = document.createElement('div');
        errorDiv.className = 'error-message text-red-500 text-sm mt-1 flex items-center gap-1';
        errorDiv.innerHTML = `<span>⚠️</span><span>${message}</span>`;
        input.parentNode.appendChild(errorDiv);

        // Focus the first error field
        if (!document.querySelector('.error:focus')) {
            input.focus();
        }
    },

    // Enhanced email validation
    validateEmail: function(email) {
        const re = /^[a-zA-Z0-9.!#$%&'*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
        return re.test(email);
    },

    // Professional error logging
    logError: function(error, context = '') {
        const errorData = {
            message: error.message || error,
            stack: error.stack || '',
            context: context,
            url: window.location.href,
            userAgent: navigator.userAgent,
            timestamp: new Date().toISOString()
        };

        console.error('Deal4u Theme Error:', errorData);

        // In production, you could send this to an error tracking service
        // fetch('/api/log-error', { method: 'POST', body: JSON.stringify(errorData) });
    }
    
};
