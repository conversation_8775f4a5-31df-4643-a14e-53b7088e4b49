<?php
/**
 * Template Name: Wishlist Page
 */

get_header(); ?>

<main class="main-content">
    <!-- Wishlist Header -->
    <section style="padding: 3rem 0; background: linear-gradient(to right, #dc2626, #be185d); color: white;">
        <div class="container">
            <div style="text-align: center;">
                <h1 style="font-size: 3rem; font-weight: bold; margin-bottom: 1rem;">My Wishlist</h1>
                <p style="font-size: 1.25rem; color: #fecaca;">Save your favorite products for later</p>
            </div>
        </div>
    </section>

    <!-- Wishlist Content -->
    <section style="padding: 4rem 0; background: #f9fafb;">
        <div class="container">
            <div style="max-width: 80rem; margin: 0 auto;">
                
                <!-- Wishlist Items -->
                <div id="wishlist-items" style="margin-bottom: 3rem;">
                    <!-- Items will be loaded here via JavaScript -->
                </div>

                <!-- Empty Wishlist State -->
                <div id="empty-wishlist" style="text-align: center; padding: 4rem 2rem; background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); display: none;">
                    <div style="font-size: 5rem; margin-bottom: 1rem; opacity: 0.5;">💔</div>
                    <h2 style="font-size: 2rem; font-weight: bold; color: #111827; margin-bottom: 1rem;">Your wishlist is empty</h2>
                    <p style="color: #6b7280; margin-bottom: 2rem; font-size: 1.125rem;">Start adding products you love to your wishlist!</p>
                    <a href="<?php echo function_exists('wc_get_page_permalink') ? esc_url(wc_get_page_permalink('shop')) : esc_url(home_url('/shop/')); ?>" 
                       style="background: linear-gradient(to right, #dc2626, #be185d); color: white; font-weight: bold; padding: 1rem 2rem; border-radius: 0.5rem; text-decoration: none; display: inline-flex; align-items: center; transition: all 0.3s;">
                        <i class="fas fa-shopping-bag" style="margin-right: 0.5rem;"></i>Start Shopping
                    </a>
                </div>

                <!-- Wishlist Actions -->
                <div id="wishlist-actions" style="background: white; padding: 2rem; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); display: none;">
                    <div style="display: flex; justify-content: between; align-items: center; flex-wrap: wrap; gap: 1rem;">
                        <div>
                            <h3 style="font-size: 1.25rem; font-weight: bold; color: #111827; margin-bottom: 0.5rem;">Wishlist Actions</h3>
                            <p style="color: #6b7280;">Manage your saved items</p>
                        </div>
                        <div style="display: flex; gap: 1rem; flex-wrap: wrap;">
                            <button onclick="addAllToCart()" 
                                    style="background: #16a34a; color: white; font-weight: bold; padding: 0.75rem 1.5rem; border-radius: 0.5rem; border: none; cursor: pointer; transition: all 0.3s;">
                                <i class="fas fa-cart-plus" style="margin-right: 0.5rem;"></i>Add All to Cart
                            </button>
                            <button onclick="clearWishlist()" 
                                    style="background: #dc2626; color: white; font-weight: bold; padding: 0.75rem 1.5rem; border-radius: 0.5rem; border: none; cursor: pointer; transition: all 0.3s;">
                                <i class="fas fa-trash" style="margin-right: 0.5rem;"></i>Clear Wishlist
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Recommended Products -->
                <div style="margin-top: 4rem;">
                    <h2 style="font-size: 2rem; font-weight: bold; color: #111827; margin-bottom: 2rem; text-align: center;">You Might Also Like</h2>
                    
                    <?php
                    // Get featured products or recent products
                    $args = array(
                        'post_type' => 'product',
                        'posts_per_page' => 4,
                        'meta_query' => array(
                            array(
                                'key' => '_featured',
                                'value' => 'yes'
                            )
                        )
                    );
                    
                    $featured_products = new WP_Query($args);
                    
                    if ($featured_products->have_posts()) : ?>
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem;">
                            <?php while ($featured_products->have_posts()) : $featured_products->the_post(); 
                                global $product; ?>
                                <div style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); overflow: hidden; transition: transform 0.3s;" onmouseover="this.style.transform='translateY(-4px)'" onmouseout="this.style.transform='translateY(0)'">
                                    <div style="aspect-ratio: 1; background: #f3f4f6; position: relative;">
                                        <?php if (has_post_thumbnail()) : ?>
                                            <img src="<?php echo get_the_post_thumbnail_url(get_the_ID(), 'medium'); ?>" 
                                                 alt="<?php the_title(); ?>" 
                                                 style="width: 100%; height: 100%; object-fit: cover;">
                                        <?php else : ?>
                                            <div style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; font-size: 3rem; color: #9ca3af;">📦</div>
                                        <?php endif; ?>
                                    </div>
                                    <div style="padding: 1.5rem;">
                                        <h3 style="font-size: 1.125rem; font-weight: bold; color: #111827; margin-bottom: 0.5rem;"><?php the_title(); ?></h3>
                                        <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 1rem;"><?php echo wp_trim_words(get_the_excerpt(), 15); ?></p>
                                        <div style="display: flex; justify-content: between; align-items: center;">
                                            <span style="font-size: 1.25rem; font-weight: bold; color: #2563eb;"><?php echo $product->get_price_html(); ?></span>
                                            <button onclick="addToWishlist(<?php echo get_the_ID(); ?>)" 
                                                    style="background: #fecaca; color: #dc2626; padding: 0.5rem; border-radius: 0.5rem; border: none; cursor: pointer; transition: all 0.3s;">
                                                <i class="fas fa-heart"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            <?php endwhile; ?>
                        </div>
                    <?php endif; 
                    wp_reset_postdata(); ?>
                </div>
            </div>
        </div>
    </section>
</main>

<script>
// Wishlist functionality
document.addEventListener('DOMContentLoaded', function() {
    loadWishlist();
});

function loadWishlist() {
    const wishlist = JSON.parse(localStorage.getItem('wishlist_items') || '[]');
    const wishlistContainer = document.getElementById('wishlist-items');
    const emptyState = document.getElementById('empty-wishlist');
    const actionsContainer = document.getElementById('wishlist-actions');
    
    if (wishlist.length === 0) {
        emptyState.style.display = 'block';
        actionsContainer.style.display = 'none';
        wishlistContainer.innerHTML = '';
        return;
    }
    
    emptyState.style.display = 'none';
    actionsContainer.style.display = 'block';
    
    // Create wishlist items HTML
    let html = '<div style="display: grid; gap: 1rem;">';
    
    wishlist.forEach(item => {
        html += `
            <div style="background: white; padding: 1.5rem; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); display: flex; gap: 1rem; align-items: center;">
                <div style="width: 80px; height: 80px; background: #f3f4f6; border-radius: 0.5rem; display: flex; align-items: center; justify-content: center; font-size: 2rem;">
                    📦
                </div>
                <div style="flex: 1;">
                    <h3 style="font-size: 1.125rem; font-weight: bold; color: #111827; margin-bottom: 0.5rem;">${item.name}</h3>
                    <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 0.5rem;">${item.description || 'Product description'}</p>
                    <span style="font-size: 1.125rem; font-weight: bold; color: #2563eb;">$${item.price}</span>
                </div>
                <div style="display: flex; gap: 0.5rem;">
                    <button onclick="addToCart(${item.id})" 
                            style="background: #16a34a; color: white; padding: 0.5rem 1rem; border-radius: 0.5rem; border: none; cursor: pointer; font-size: 0.875rem;">
                        <i class="fas fa-cart-plus" style="margin-right: 0.25rem;"></i>Add to Cart
                    </button>
                    <button onclick="removeFromWishlist(${item.id})" 
                            style="background: #dc2626; color: white; padding: 0.5rem; border-radius: 0.5rem; border: none; cursor: pointer;">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            </div>
        `;
    });
    
    html += '</div>';
    wishlistContainer.innerHTML = html;
}

function addToWishlist(productId) {
    // This would typically make an AJAX call to get product details
    const wishlist = JSON.parse(localStorage.getItem('wishlist_items') || '[]');
    
    if (!wishlist.find(item => item.id === productId)) {
        // Add mock product data (in real implementation, fetch from server)
        wishlist.push({
            id: productId,
            name: 'Product Name',
            price: '99.99',
            description: 'Product description'
        });
        
        localStorage.setItem('wishlist_items', JSON.stringify(wishlist));
        loadWishlist();
        
        // Show success message
        alert('Product added to wishlist!');
    } else {
        alert('Product is already in your wishlist!');
    }
}

function removeFromWishlist(productId) {
    const wishlist = JSON.parse(localStorage.getItem('wishlist_items') || '[]');
    const updatedWishlist = wishlist.filter(item => item.id !== productId);
    
    localStorage.setItem('wishlist_items', JSON.stringify(updatedWishlist));
    loadWishlist();
}

function addToCart(productId) {
    // Add to cart functionality
    alert('Product added to cart!');
}

function addAllToCart() {
    const wishlist = JSON.parse(localStorage.getItem('wishlist_items') || '[]');
    if (wishlist.length > 0) {
        alert(`Added ${wishlist.length} items to cart!`);
    }
}

function clearWishlist() {
    if (confirm('Are you sure you want to clear your entire wishlist?')) {
        localStorage.removeItem('wishlist_items');
        loadWishlist();
    }
}
</script>

<?php get_footer(); ?>
