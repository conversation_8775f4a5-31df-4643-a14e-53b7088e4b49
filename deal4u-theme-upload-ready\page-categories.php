<?php
/**
 * Template Name: Categories Page
 */

get_header(); ?>

<main class="main-content">
    <!-- Professional Categories Header -->
    <section style="padding: 4rem 0; background: linear-gradient(135deg, #1e293b, #334155, #475569); color: white; position: relative; overflow: hidden;">
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0); background-size: 20px 20px; opacity: 0.3;"></div>
        <div class="container" style="position: relative; z-index: 1;">
            <div style="text-align: center; max-width: 800px; margin: 0 auto;">
                <div style="display: inline-flex; align-items: center; justify-content: center; width: 5rem; height: 5rem; background: rgba(255, 255, 255, 0.1); border-radius: 1rem; margin-bottom: 2rem; backdrop-filter: blur(10px);">
                    <i class="fas fa-store" style="font-size: 2rem;"></i>
                </div>
                <h1 style="font-size: 3.5rem; font-weight: bold; margin-bottom: 1.5rem; line-height: 1.1;">Product Categories</h1>
                <p style="font-size: 1.25rem; color: #cbd5e1; line-height: 1.6; margin-bottom: 0;">Discover our comprehensive range of premium products, carefully organized for your convenience</p>
            </div>
        </div>
    </section>

    <!-- Dynamic WooCommerce Categories Grid -->
    <section style="padding: 4rem 0; background: #f8fafc;">
        <div class="container">
            <div style="max-width: 1200px; margin: 0 auto; display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem;">

                <?php
                // Get WooCommerce product categories
                $categories = get_terms(array(
                    'taxonomy' => 'product_cat',
                    'hide_empty' => false,
                    'parent' => 0, // Only get parent categories
                    'orderby' => 'name',
                    'order' => 'ASC'
                ));

                // Define color themes for categories
                $color_themes = array(
                    array(
                        'bg_gradient' => 'linear-gradient(135deg, #f8fafc, #f1f5f9)',
                        'icon_bg' => 'linear-gradient(135deg, #dbeafe, #bfdbfe)',
                        'icon_color' => '#2563eb',
                        'button_bg' => 'linear-gradient(135deg, #2563eb, #1d4ed8)',
                        'button_shadow' => 'rgba(37, 99, 235, 0.25)',
                        'button_shadow_hover' => 'rgba(37, 99, 235, 0.35)',
                        'sub_bg' => '#f8fafc',
                        'sub_bg_hover' => '#f1f5f9',
                        'sub_icon_bg' => '#dbeafe'
                    ),
                    array(
                        'bg_gradient' => 'linear-gradient(135deg, #fef3c7, #fde68a)',
                        'icon_bg' => 'linear-gradient(135deg, #fef3c7, #fde68a)',
                        'icon_color' => '#d97706',
                        'button_bg' => 'linear-gradient(135deg, #f59e0b, #d97706)',
                        'button_shadow' => 'rgba(245, 158, 11, 0.25)',
                        'button_shadow_hover' => 'rgba(245, 158, 11, 0.35)',
                        'sub_bg' => '#fffbeb',
                        'sub_bg_hover' => '#fef3c7',
                        'sub_icon_bg' => '#fef3c7'
                    ),
                    array(
                        'bg_gradient' => 'linear-gradient(135deg, #f0fdf4, #dcfce7)',
                        'icon_bg' => 'linear-gradient(135deg, #dcfce7, #bbf7d0)',
                        'icon_color' => '#16a34a',
                        'button_bg' => 'linear-gradient(135deg, #22c55e, #16a34a)',
                        'button_shadow' => 'rgba(34, 197, 94, 0.25)',
                        'button_shadow_hover' => 'rgba(34, 197, 94, 0.35)',
                        'sub_bg' => '#f0fdf4',
                        'sub_bg_hover' => '#dcfce7',
                        'sub_icon_bg' => '#dcfce7'
                    ),
                    array(
                        'bg_gradient' => 'linear-gradient(135deg, #faf5ff, #f3e8ff)',
                        'icon_bg' => 'linear-gradient(135deg, #e9d5ff, #d8b4fe)',
                        'icon_color' => '#7c3aed',
                        'button_bg' => 'linear-gradient(135deg, #8b5cf6, #7c3aed)',
                        'button_shadow' => 'rgba(139, 92, 246, 0.25)',
                        'button_shadow_hover' => 'rgba(139, 92, 246, 0.35)',
                        'sub_bg' => '#faf5ff',
                        'sub_bg_hover' => '#e9d5ff',
                        'sub_icon_bg' => '#e9d5ff'
                    ),
                    array(
                        'bg_gradient' => 'linear-gradient(135deg, #fef2f2, #fee2e2)',
                        'icon_bg' => 'linear-gradient(135deg, #fee2e2, #fecaca)',
                        'icon_color' => '#dc2626',
                        'button_bg' => 'linear-gradient(135deg, #ef4444, #dc2626)',
                        'button_shadow' => 'rgba(239, 68, 68, 0.25)',
                        'button_shadow_hover' => 'rgba(239, 68, 68, 0.35)',
                        'sub_bg' => '#fef2f2',
                        'sub_bg_hover' => '#fee2e2',
                        'sub_icon_bg' => '#fee2e2'
                    )
                );

                // Define icons for common category names
                $category_icons = array(
                    'gaming' => 'fas fa-gamepad',
                    'games' => 'fas fa-gamepad',
                    'electronics' => 'fas fa-mobile-alt',
                    'phones' => 'fas fa-mobile-alt',
                    'computers' => 'fas fa-laptop',
                    'laptops' => 'fas fa-laptop',
                    'accessories' => 'fas fa-headphones',
                    'clothing' => 'fas fa-tshirt',
                    'fashion' => 'fas fa-tshirt',
                    'women' => 'fas fa-female',
                    'men' => 'fas fa-male',
                    'books' => 'fas fa-book',
                    'home' => 'fas fa-home',
                    'garden' => 'fas fa-leaf',
                    'sports' => 'fas fa-dumbbell',
                    'toys' => 'fas fa-puzzle-piece',
                    'beauty' => 'fas fa-heart',
                    'health' => 'fas fa-medkit',
                    'automotive' => 'fas fa-car',
                    'jewelry' => 'fas fa-gem',
                    'watches' => 'fas fa-clock',
                    'bags' => 'fas fa-shopping-bag',
                    'shoes' => 'fas fa-shoe-prints'
                );

                if ($categories && !is_wp_error($categories) && count($categories) > 0) {
                    $color_index = 0;
                    $displayed_categories = 0;

                    foreach ($categories as $category) {
                        // Skip "Uncategorized" if there are other categories
                        if ($category->slug === 'uncategorized' && count($categories) > 1) {
                            continue;
                        }

                        $theme = $color_themes[$color_index % count($color_themes)];

                        // Get category icon
                        $category_slug = strtolower($category->slug);
                        $icon = 'fas fa-tag'; // default icon
                        foreach ($category_icons as $key => $cat_icon) {
                            if (strpos($category_slug, $key) !== false) {
                                $icon = $cat_icon;
                                break;
                            }
                        }

                        // Get subcategories
                        $subcategories = get_terms(array(
                            'taxonomy' => 'product_cat',
                            'hide_empty' => false,
                            'parent' => $category->term_id,
                            'number' => 4 // Limit to 4 subcategories
                        ));

                        $category_url = get_term_link($category);
                        $displayed_categories++;
                        ?>

                        <!-- <?php echo esc_html($category->name); ?> Category -->
                        <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 16px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); transition: all 0.3s ease; position: relative;" onmouseover="this.style.transform='translateY(-4px)'; this.style.boxShadow='0 12px 24px rgba(0, 0, 0, 0.1)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 6px rgba(0, 0, 0, 0.05)'">
                            <div style="background: <?php echo $theme['bg_gradient']; ?>; padding: 2rem; border-bottom: 1px solid #e5e7eb; position: relative;">
                                <div style="display: flex; align-items: center; margin-bottom: 1rem;">
                                    <div style="width: 60px; height: 60px; background: <?php echo $theme['icon_bg']; ?>; border-radius: 12px; display: flex; align-items: center; justify-content: center; margin-right: 1rem; box-shadow: 0 4px 8px <?php echo $theme['button_shadow']; ?>;">
                                        <i class="<?php echo $icon; ?>" style="color: <?php echo $theme['icon_color']; ?>; font-size: 24px;"></i>
                                    </div>
                                    <div>
                                        <h3 style="font-size: 1.5rem; font-weight: 700; color: #111827; margin: 0; letter-spacing: -0.025em;"><?php echo esc_html($category->name); ?></h3>
                                        <p style="font-size: 0.95rem; color: #6b7280; margin: 0.25rem 0 0 0;"><?php echo $category->count; ?> products available</p>
                                    </div>
                                </div>
                            </div>
                            <div style="padding: 2rem;">
                                <?php if ($subcategories && !is_wp_error($subcategories) && count($subcategories) > 0) : ?>
                                <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem; margin-bottom: 2rem;">
                                    <?php foreach ($subcategories as $subcategory) :
                                        $sub_icon = 'fas fa-tag';
                                        $sub_slug = strtolower($subcategory->slug);
                                        foreach ($category_icons as $key => $cat_icon) {
                                            if (strpos($sub_slug, $key) !== false) {
                                                $sub_icon = $cat_icon;
                                                break;
                                            }
                                        }
                                    ?>
                                    <div style="text-align: center; padding: 1.25rem; background: <?php echo $theme['sub_bg']; ?>; border-radius: 12px; border: 1px solid <?php echo $theme['sub_icon_bg']; ?>; transition: all 0.2s;" onmouseover="this.style.backgroundColor='<?php echo $theme['sub_bg_hover']; ?>'" onmouseout="this.style.backgroundColor='<?php echo $theme['sub_bg']; ?>'">
                                        <div style="width: 40px; height: 40px; background: <?php echo $theme['sub_icon_bg']; ?>; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin: 0 auto 0.75rem auto;">
                                            <i class="<?php echo $sub_icon; ?>" style="color: <?php echo $theme['icon_color']; ?>; font-size: 16px;"></i>
                                        </div>
                                        <p style="font-size: 0.875rem; color: #374151; font-weight: 500; margin: 0;"><?php echo esc_html($subcategory->name); ?></p>
                                    </div>
                                    <?php endforeach; ?>
                                </div>
                                <?php endif; ?>
                                <a href="<?php echo esc_url($category_url); ?>"
                                   style="background: <?php echo $theme['button_bg']; ?>; color: white; font-weight: 600; padding: 1rem 1.5rem; border-radius: 12px; text-decoration: none; display: block; text-align: center; transition: all 0.3s; box-shadow: 0 4px 12px <?php echo $theme['button_shadow']; ?>;"
                                   onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 20px <?php echo $theme['button_shadow_hover']; ?>'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px <?php echo $theme['button_shadow']; ?>'">
                                    <i class="fas fa-arrow-right" style="margin-right: 0.5rem;"></i>Browse <?php echo esc_html($category->name); ?>
                                </a>
                            </div>
                        </div>

                        <?php
                        $color_index++;
                    }

                    // If no categories were displayed, show a message
                    if ($displayed_categories === 0) {
                        ?>
                        <div style="text-align: center; padding: 4rem 2rem; background: #ffffff; border-radius: 16px; border: 1px solid #e5e7eb;">
                            <div style="display: inline-flex; align-items: center; justify-content: center; width: 4rem; height: 4rem; background: #f3f4f6; border-radius: 12px; margin-bottom: 1.5rem;">
                                <i class="fas fa-folder-open" style="color: #6b7280; font-size: 1.5rem;"></i>
                            </div>
                            <h3 style="font-size: 1.5rem; font-weight: 600; color: #374151; margin-bottom: 1rem;">No Categories Available</h3>
                            <p style="color: #6b7280; font-size: 1rem; margin-bottom: 2rem; max-width: 500px; margin-left: auto; margin-right: auto;">
                                It looks like you haven't set up any product categories yet. Create some categories in your WooCommerce admin to organize your products.
                            </p>
                            <a href="<?php echo admin_url('edit-tags.php?taxonomy=product_cat&post_type=product'); ?>"
                               style="background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; font-weight: 600; padding: 1rem 2rem; border-radius: 12px; text-decoration: none; display: inline-flex; align-items: center; transition: all 0.3s;">
                                <i class="fas fa-plus" style="margin-right: 0.5rem;"></i>Add Categories
                            </a>
                        </div>
                        <?php
                    }
                } else {
                    ?>
                    <div style="text-align: center; padding: 4rem 2rem; background: #ffffff; border-radius: 16px; border: 1px solid #e5e7eb;">
                        <div style="display: inline-flex; align-items: center; justify-content: center; width: 4rem; height: 4rem; background: #fef2f2; border-radius: 12px; margin-bottom: 1.5rem;">
                            <i class="fas fa-exclamation-triangle" style="color: #dc2626; font-size: 1.5rem;"></i>
                        </div>
                        <h3 style="font-size: 1.5rem; font-weight: 600; color: #374151; margin-bottom: 1rem;">Categories Not Available</h3>
                        <p style="color: #6b7280; font-size: 1rem;">There was an issue loading the product categories. Please check your WooCommerce installation.</p>
                    </div>
                    <?php
                }
                ?>

            </div>

            <!-- Browse All Products -->
            <div style="text-align: center; margin-top: 4rem; padding: 3rem; background: #ffffff; border-radius: 16px; border: 1px solid #e5e7eb; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                <div style="margin-bottom: 2rem;">
                    <div style="display: inline-flex; align-items: center; justify-content: center; width: 4rem; height: 4rem; background: linear-gradient(135deg, #dbeafe, #bfdbfe); border-radius: 12px; margin-bottom: 1.5rem;">
                        <i class="fas fa-store" style="color: #2563eb; font-size: 1.5rem;"></i>
                    </div>
                    <h2 style="font-size: 2rem; font-weight: 700; color: #111827; margin-bottom: 1rem;">Explore Our Complete Collection</h2>
                    <p style="font-size: 1.125rem; color: #6b7280; max-width: 600px; margin: 0 auto;">Discover thousands of premium products across all categories with our comprehensive shopping experience.</p>
                </div>
                <a href="<?php echo esc_url(home_url('/shop/')); ?>"
                   style="background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; font-weight: 600; padding: 1.25rem 2.5rem; border-radius: 12px; text-decoration: none; display: inline-flex; align-items: center; font-size: 1.125rem; transition: all 0.3s; box-shadow: 0 4px 12px rgba(37, 99, 235, 0.25);"
                   onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 20px rgba(37, 99, 235, 0.35)'"
                   onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(37, 99, 235, 0.25)'">
                    <i class="fas fa-shopping-bag" style="margin-right: 0.75rem;"></i>Browse All Products
                </a>
            </div>
        </div>
    </section>
</main>

<?php get_footer(); ?>
