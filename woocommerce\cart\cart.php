<?php
/**
 * Cart Page Template
 * Deal4u Theme - WooCommerce Cart
 */

defined('ABSPATH') || exit;

get_header(); ?>

<div class="min-h-screen bg-gradient-to-br from-gray-50 to-blue-50 py-8">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        
        <!-- Breadcrumb -->
        <nav class="flex items-center space-x-2 text-sm text-gray-600 mb-8">
            <a href="<?php echo home_url(); ?>" class="hover:text-blue-600 transition-colors duration-200">Home</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <span class="text-gray-900 font-medium">Shopping Cart</span>
        </nav>

        <?php do_action('woocommerce_before_cart'); ?>

        <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
            <div class="p-6 lg:p-8">
                <div class="flex items-center justify-between mb-8">
                    <h1 class="text-2xl lg:text-3xl font-bold text-gray-900">Shopping Cart</h1>
                    <div class="text-sm text-gray-600">
                        <?php echo WC()->cart->get_cart_contents_count(); ?> item(s) in cart
                    </div>
                </div>

                <form class="woocommerce-cart-form" action="<?php echo esc_url(wc_get_cart_url()); ?>" method="post">
                    <?php do_action('woocommerce_before_cart_table'); ?>

                    <?php if (WC()->cart->is_empty()) : ?>
                        <!-- Empty Cart -->
                        <div class="text-center py-16">
                            <div class="mb-6">
                                <svg class="w-24 h-24 mx-auto text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15"></path>
                                </svg>
                            </div>
                            <h2 class="text-xl font-semibold text-gray-900 mb-2">Your cart is empty</h2>
                            <p class="text-gray-600 mb-8">Looks like you haven't added any items to your cart yet.</p>
                            <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>" 
                               class="inline-flex items-center gap-2 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-3 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02]">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                                </svg>
                                Continue Shopping
                            </a>
                        </div>
                    <?php else : ?>
                        <!-- Cart Items -->
                        <div class="overflow-x-auto">
                            <table class="w-full">
                                <thead>
                                    <tr class="border-b border-gray-200">
                                        <th class="text-left py-4 px-2 font-medium text-gray-900">Product</th>
                                        <th class="text-center py-4 px-2 font-medium text-gray-900">Price</th>
                                        <th class="text-center py-4 px-2 font-medium text-gray-900">Quantity</th>
                                        <th class="text-center py-4 px-2 font-medium text-gray-900">Subtotal</th>
                                        <th class="text-center py-4 px-2 font-medium text-gray-900">Remove</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php do_action('woocommerce_before_cart_contents'); ?>

                                    <?php
                                    foreach (WC()->cart->get_cart() as $cart_item_key => $cart_item) {
                                        $_product = apply_filters('woocommerce_cart_item_product', $cart_item['data'], $cart_item, $cart_item_key);
                                        $product_id = apply_filters('woocommerce_cart_item_product_id', $cart_item['product_id'], $cart_item, $cart_item_key);

                                        if ($_product && $_product->exists() && $cart_item['quantity'] > 0 && apply_filters('woocommerce_cart_item_visible', true, $cart_item, $cart_item_key)) {
                                            $product_permalink = apply_filters('woocommerce_cart_item_permalink', $_product->is_visible() ? $_product->get_permalink($cart_item) : '', $cart_item, $cart_item_key);
                                            ?>
                                            <tr class="border-b border-gray-100 hover:bg-gray-50 transition-colors duration-200">
                                                <!-- Product -->
                                                <td class="py-6 px-2">
                                                    <div class="flex items-center gap-4">
                                                        <div class="w-20 h-20 bg-gray-100 rounded-lg overflow-hidden flex-shrink-0">
                                                            <?php
                                                            $thumbnail = apply_filters('woocommerce_cart_item_thumbnail', $_product->get_image('thumbnail'), $cart_item, $cart_item_key);
                                                            if (!$product_permalink) {
                                                                echo $thumbnail;
                                                            } else {
                                                                printf('<a href="%s">%s</a>', esc_url($product_permalink), $thumbnail);
                                                            }
                                                            ?>
                                                        </div>
                                                        <div>
                                                            <h3 class="font-medium text-gray-900">
                                                                <?php
                                                                if (!$product_permalink) {
                                                                    echo wp_kses_post(apply_filters('woocommerce_cart_item_name', $_product->get_name(), $cart_item, $cart_item_key) . '&nbsp;');
                                                                } else {
                                                                    echo wp_kses_post(apply_filters('woocommerce_cart_item_name', sprintf('<a href="%s">%s</a>', esc_url($product_permalink), $_product->get_name()), $cart_item, $cart_item_key));
                                                                }
                                                                ?>
                                                            </h3>
                                                            <?php echo wc_get_formatted_cart_item_data($cart_item); ?>
                                                            <p class="text-sm text-gray-600 mt-1">SKU: <?php echo $_product->get_sku() ?: 'N/A'; ?></p>
                                                        </div>
                                                    </div>
                                                </td>

                                                <!-- Price -->
                                                <td class="py-6 px-2 text-center">
                                                    <span class="font-medium text-gray-900">
                                                        <?php echo apply_filters('woocommerce_cart_item_price', WC()->cart->get_product_price($_product), $cart_item, $cart_item_key); ?>
                                                    </span>
                                                </td>

                                                <!-- Quantity -->
                                                <td class="py-6 px-2 text-center">
                                                    <?php
                                                    if ($_product->is_sold_individually()) {
                                                        $product_quantity = sprintf('1 <input type="hidden" name="cart[%s][qty]" value="1" />', $cart_item_key);
                                                    } else {
                                                        $product_quantity = woocommerce_quantity_input(
                                                            array(
                                                                'input_name'   => "cart[{$cart_item_key}][qty]",
                                                                'input_value'  => $cart_item['quantity'],
                                                                'max_value'    => $_product->get_max_purchase_quantity(),
                                                                'min_value'    => '0',
                                                                'product_name' => $_product->get_name(),
                                                            ),
                                                            $_product,
                                                            false
                                                        );
                                                    }
                                                    echo apply_filters('woocommerce_cart_item_quantity', $product_quantity, $cart_item_key, $cart_item);
                                                    ?>
                                                </td>

                                                <!-- Subtotal -->
                                                <td class="py-6 px-2 text-center">
                                                    <span class="font-semibold text-gray-900">
                                                        <?php echo apply_filters('woocommerce_cart_item_subtotal', WC()->cart->get_product_subtotal($_product, $cart_item['quantity']), $cart_item, $cart_item_key); ?>
                                                    </span>
                                                </td>

                                                <!-- Remove -->
                                                <td class="py-6 px-2 text-center">
                                                    <?php
                                                    echo apply_filters(
                                                        'woocommerce_cart_item_remove_link',
                                                        sprintf(
                                                            '<a href="%s" class="text-red-600 hover:text-red-800 transition-colors duration-200" aria-label="%s" data-product_id="%s" data-product_sku="%s">
                                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                                                </svg>
                                                            </a>',
                                                            esc_url(wc_get_cart_remove_url($cart_item_key)),
                                                            esc_html__('Remove this item', 'woocommerce'),
                                                            esc_attr($product_id),
                                                            esc_attr($_product->get_sku())
                                                        ),
                                                        $cart_item_key
                                                    );
                                                    ?>
                                                </td>
                                            </tr>
                                            <?php
                                        }
                                    }
                                    ?>

                                    <?php do_action('woocommerce_cart_contents'); ?>

                                    <tr>
                                        <td colspan="5" class="py-6">
                                            <div class="flex flex-col sm:flex-row gap-4 justify-between items-center">
                                                <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>" 
                                                   class="inline-flex items-center gap-2 text-blue-600 hover:text-blue-800 font-medium transition-colors duration-200">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                                                    </svg>
                                                    Continue Shopping
                                                </a>
                                                
                                                <button type="submit" name="update_cart" value="<?php esc_attr_e('Update cart', 'woocommerce'); ?>" 
                                                        class="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-6 rounded-lg transition-colors duration-200">
                                                    Update Cart
                                                </button>
                                            </div>
                                        </td>
                                    </tr>

                                    <?php do_action('woocommerce_cart_actions'); ?>
                                    <?php wp_nonce_field('woocommerce-cart', 'woocommerce-cart-nonce'); ?>
                                </tbody>
                            </table>
                        </div>
                    <?php endif; ?>

                    <?php do_action('woocommerce_after_cart_table'); ?>
                </form>

                <?php if (!WC()->cart->is_empty()) : ?>
                    <!-- Cart Totals -->
                    <div class="mt-8 lg:mt-12">
                        <div class="max-w-md ml-auto">
                            <?php do_action('woocommerce_cart_collaterals'); ?>
                            
                            <div class="bg-gray-50 rounded-xl p-6">
                                <h3 class="text-lg font-semibold text-gray-900 mb-4">Cart Summary</h3>
                                
                                <div class="space-y-3">
                                    <div class="flex justify-between text-gray-600">
                                        <span>Subtotal:</span>
                                        <span><?php wc_cart_totals_subtotal_html(); ?></span>
                                    </div>
                                    
                                    <?php foreach (WC()->cart->get_coupons() as $code => $coupon) : ?>
                                        <div class="flex justify-between text-green-600">
                                            <span>Coupon: <?php echo esc_html($code); ?></span>
                                            <span>-<?php wc_cart_totals_coupon_html($coupon); ?></span>
                                        </div>
                                    <?php endforeach; ?>
                                    
                                    <?php if (WC()->cart->needs_shipping() && WC()->cart->show_shipping()) : ?>
                                        <div class="flex justify-between text-gray-600">
                                            <span>Shipping:</span>
                                            <span><?php wc_cart_totals_shipping_html(); ?></span>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <?php foreach (WC()->cart->get_fees() as $fee) : ?>
                                        <div class="flex justify-between text-gray-600">
                                            <span><?php echo esc_html($fee->name); ?>:</span>
                                            <span><?php wc_cart_totals_fee_html($fee); ?></span>
                                        </div>
                                    <?php endforeach; ?>
                                    
                                    <?php if (wc_tax_enabled() && !WC()->cart->display_prices_including_tax()) : ?>
                                        <?php if ('itemized' === get_option('woocommerce_tax_total_display')) : ?>
                                            <?php foreach (WC()->cart->get_tax_totals() as $code => $tax) : ?>
                                                <div class="flex justify-between text-gray-600">
                                                    <span><?php echo esc_html($tax->label); ?>:</span>
                                                    <span><?php echo wp_kses_post($tax->formatted_amount); ?></span>
                                                </div>
                                            <?php endforeach; ?>
                                        <?php else : ?>
                                            <div class="flex justify-between text-gray-600">
                                                <span><?php echo esc_html(WC()->countries->tax_or_vat()); ?>:</span>
                                                <span><?php wc_cart_totals_taxes_total_html(); ?></span>
                                            </div>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                    
                                    <div class="border-t border-gray-200 pt-3 mt-3">
                                        <div class="flex justify-between text-lg font-semibold text-gray-900">
                                            <span>Total:</span>
                                            <span><?php wc_cart_totals_order_total_html(); ?></span>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="mt-6">
                                    <a href="<?php echo esc_url(wc_get_checkout_url()); ?>" 
                                       class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-4 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] flex items-center justify-center gap-2">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z"></path>
                                        </svg>
                                        Proceed to Checkout
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>

                <?php do_action('woocommerce_after_cart'); ?>
            </div>
        </div>
    </div>
</div>

<?php get_footer(); ?>
