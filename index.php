<?php get_header(); ?>

<main class="flex-1">
    <!-- Professional Hero Section -->
    <section class="bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900 text-white relative overflow-hidden">

        <!-- Subtle Background Pattern -->
        <div class="absolute inset-0 opacity-5">
            <div class="absolute inset-0 bg-gradient-to-br from-white to-transparent"></div>
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 1px, transparent 1px); background-size: 50px 50px;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-20 md:py-28 relative z-10">
            <!-- Professional Dropshipping Badge -->
            <div class="absolute top-8 right-8 hidden md:block">
                <div class="relative bg-gradient-to-br from-blue-500 to-purple-600 text-white rounded-2xl px-6 py-4 shadow-2xl border border-blue-400/20">
                    <div class="text-center">
                        <p class="text-lg font-bold leading-none">Direct</p>
                        <p class="text-2xl font-black leading-none">From Source</p>
                        <p class="text-xs mt-1 font-medium opacity-90">No Middleman</p>
                    </div>
                    <div class="absolute -top-1 -right-1 w-4 h-4 bg-green-500 rounded-full animate-pulse"></div>
                </div>
            </div>

            <div class="flex flex-col md:flex-row items-center">
                <div class="md:w-2/3 text-center md:text-left">
                    <div class="inline-flex items-center px-8 py-4 bg-white/10 backdrop-blur-sm rounded-full mb-8 border border-white/20 shadow-xl">
                        <div class="w-2 h-2 bg-blue-400 rounded-full mr-4 shadow-lg"></div>
                        <span class="text-sm font-medium tracking-wide text-white">Direct From Global Suppliers - Premium Quality Guaranteed</span>
                        <div class="ml-4 px-3 py-1 bg-gradient-to-r from-blue-400 to-purple-400 text-white text-xs rounded-full font-medium">
                            Dropshipping
                        </div>
                    </div>

                    <h1 class="text-5xl md:text-7xl font-bold mb-8 leading-tight">
                        Shop Direct From
                        <span class="block text-transparent bg-clip-text bg-gradient-to-r from-blue-400 via-purple-400 to-indigo-400">
                            Global Suppliers
                        </span>
                    </h1>

                    <p class="text-lg md:text-xl mb-8 text-slate-300 max-w-2xl leading-relaxed">
                        Premium products shipped directly from manufacturers worldwide. Skip the middleman, enjoy wholesale prices, and get authentic quality delivered to your door with fast, reliable shipping.
                    </p>

                    <div class="flex flex-col sm:flex-row gap-4 mb-10">
                        <a href="<?php echo function_exists('wc_get_page_permalink') ? esc_url(wc_get_page_permalink('shop')) : esc_url(home_url('/shop/')); ?>"
                           class="group bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-semibold py-4 px-8 rounded-xl text-lg transition-all duration-300 shadow-xl hover:shadow-2xl flex items-center justify-center transform hover:-translate-y-1">
                            <svg class="mr-3 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            Shop Direct
                            <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                            </svg>
                        </a>
                        <a href="<?php echo esc_url(home_url('/categories/')); ?>"
                           class="group bg-white/10 hover:bg-white/20 border-2 border-white/20 hover:border-white/40 text-white font-semibold py-4 px-8 rounded-xl text-lg transition-all duration-300 shadow-xl hover:shadow-2xl flex items-center justify-center backdrop-blur-sm transform hover:-translate-y-1">
                            <svg class="mr-3 w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                            </svg>
                            Browse Global Products
                            <svg class="ml-2 w-5 h-5 group-hover:translate-x-1 transition-transform" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                            </svg>
                        </a>
                    </div>

                    <!-- Dropshipping Trust Badges -->
                    <div class="flex flex-wrap items-center justify-center gap-6 mb-8">
                        <div class="flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
                            <svg class="w-4 h-4 text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-sm font-medium text-white">Secure Payments</span>
                        </div>
                        <div class="flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
                            <svg class="w-4 h-4 text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"></path>
                            </svg>
                            <span class="text-sm font-medium text-white">Direct Shipping</span>
                        </div>
                        <div class="flex items-center gap-2 px-4 py-2 bg-white/10 backdrop-blur-sm rounded-lg border border-white/20">
                            <svg class="w-4 h-4 text-blue-300" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-sm font-medium text-white">Authentic Products</span>
                        </div>
                    </div>

                    <!-- Professional Payment Security Section -->
                    <div class="flex flex-col items-center justify-center gap-3 mb-8">
                        <div class="flex items-center gap-2">
                            <svg class="w-4 h-4 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path>
                            </svg>
                            <span class="text-sm text-slate-300 font-medium">256-bit SSL Secured Payments</span>
                        </div>
                        <div class="flex items-center gap-3">
                            <!-- Visa -->
                            <div class="w-16 h-10 bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg shadow-lg border border-blue-200 flex items-center justify-center group hover:shadow-xl transition-all duration-300 hover:scale-105 relative overflow-hidden">
                                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-700"></div>
                                <span class="text-white font-black text-sm tracking-wider relative z-10">VISA</span>
                            </div>

                            <!-- Mastercard -->
                            <div class="w-16 h-10 bg-gradient-to-br from-red-500 to-orange-500 rounded-lg shadow-lg border border-red-200 flex items-center justify-center group hover:shadow-xl transition-all duration-300 hover:scale-105 relative overflow-hidden">
                                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-700"></div>
                                <div class="flex items-center gap-[-2px] relative z-10">
                                    <div class="w-4 h-4 bg-red-600 rounded-full"></div>
                                    <div class="w-4 h-4 bg-orange-400 rounded-full -ml-2"></div>
                                </div>
                            </div>

                            <!-- American Express -->
                            <div class="w-16 h-10 bg-gradient-to-br from-blue-700 to-blue-900 rounded-lg shadow-lg border border-blue-300 flex items-center justify-center group hover:shadow-xl transition-all duration-300 hover:scale-105 relative overflow-hidden">
                                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-700"></div>
                                <span class="text-white font-black text-xs tracking-wider relative z-10">AMEX</span>
                            </div>

                            <!-- PayPal -->
                            <div class="w-16 h-10 bg-gradient-to-br from-blue-600 to-blue-800 rounded-lg shadow-lg border border-blue-200 flex items-center justify-center group hover:shadow-xl transition-all duration-300 hover:scale-105 relative overflow-hidden">
                                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent -skew-x-12 translate-x-[-100%] group-hover:translate-x-[200%] transition-transform duration-700"></div>
                                <div class="flex items-center gap-0.5 relative z-10">
                                    <span class="text-white font-black text-xs">Pay</span>
                                    <span class="text-blue-300 font-black text-xs">Pal</span>
                                </div>
                            </div>

                            <!-- Apple Pay -->
                            <div class="w-16 h-10 bg-gradient-to-br from-gray-800 to-black rounded-lg shadow-lg border border-gray-300 flex items-center justify-center group hover:shadow-xl transition-all duration-300 hover:scale-105 relative overflow-hidden">
                                <svg class="w-8 h-5" viewBox="0 0 40 24" fill="none">
                                    <rect width="40" height="24" rx="4" fill="white"/>
                                    <path d="M16.5 8.5c-.3-.4-.8-.7-1.3-.6-.1 1 .3 1.6.7 2.1.3.4.8.7 1.3.6.1-1-.2-1.7-.7-2.1zm.7 2.3c-.7 0-1.3.4-1.6.4s-1-.4-1.7-.4c-.9 0-1.7.5-2.1 1.3-.9 1.6-.2 4 .6 5.3.4.6.9 1.3 1.5 1.3s.8-.4 1.5-.4.9.4 1.5.4 1-.6 1.4-1.2c.3-.4.4-.6.6-1-.6-.2-1-.8-1-1.5s.4-1.2.9-1.5c-.4-.6-1.1-1-1.8-1l.2-.2z" fill="#000"/>
                                    <text x="26" y="14" text-anchor="middle" fill="#000" font-family="Arial, sans-serif" font-size="4" font-weight="500">Pay</text>
                                </svg>
                            </div>

                            <!-- Google Pay -->
                            <div class="w-12 h-8 bg-white rounded-md shadow-sm border border-gray-200 flex items-center justify-center group hover:shadow-md transition-shadow">
                                <svg class="w-8 h-5" viewBox="0 0 40 24" fill="none">
                                    <rect width="40" height="24" rx="4" fill="white"/>
                                    <path d="M20 9v2.5h4.2c-.2 1.1-.8 2-1.7 2.6v2.1h2.7c1.6-1.5 2.5-3.7 2.5-6.3 0-.6-.1-1.2-.2-1.7H20v-.2z" fill="#4285F4"/>
                                    <path d="M15.8 14.8c-.4-.3-.9-.5-1.4-.5s-1 .2-1.4.5c-.8.6-1.3 1.5-1.3 2.5s.5 1.9 1.3 2.5c.4.3.9.5 1.4.5s1-.2 1.4-.5c.8-.6 1.3-1.5 1.3-2.5s-.5-1.9-1.3-2.5z" fill="#34A853"/>
                                    <path d="M20 16.5c0 .8-.1 1.5-.3 2.2-.5 1.5-1.7 2.6-3.2 2.8-.5.1-1 .1-1.5 0-1.5-.2-2.7-1.3-3.2-2.8-.2-.7-.3-1.4-.3-2.2s.1-1.5.3-2.2c.5-1.5 1.7-2.6 3.2-2.8.5-.1 1-.1 1.5 0 1.5.2 2.7 1.3 3.2 2.8.2.7.3 1.4.3 2.2z" fill="#FBBC05"/>
                                    <path d="M11.8 12.5c.4-.3.9-.5 1.4-.5s1 .2 1.4.5c.8.6 1.3 1.5 1.3 2.5H20c0-2.6-.9-4.8-2.5-6.3h-2.7v2.1c.9.6 1.5 1.5 1.7 2.6H11.8v-.9z" fill="#EA4335"/>
                                </svg>
                            </div>
                        </div>
                        <div class="flex items-center gap-4 text-xs text-slate-400">
                            <div class="flex items-center gap-1">
                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z" clip-rule="evenodd"></path>
                                </svg>
                                <span>PCI Compliant</span>
                            </div>
                            <span>•</span>
                            <div class="flex items-center gap-1">
                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                </svg>
                                <span>Bank-Level Security</span>
                            </div>
                            <span>•</span>
                            <div class="flex items-center gap-1">
                                <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                </svg>
                                <span>Money Back Guarantee</span>
                            </div>
                        </div>
                    </div>

                    <!-- Dropshipping Trust Indicators -->
                    <div class="flex flex-wrap items-center justify-center md:justify-start gap-8 text-sm text-slate-300">
                        <div class="flex items-center bg-gradient-to-r from-blue-500/20 to-purple-500/20 px-4 py-2 rounded-lg backdrop-blur-sm border border-blue-400/30">
                            <svg class="w-5 h-5 mr-2 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                            </svg>
                            <span class="font-bold">4.9/5 Trusted Supplier</span>
                        </div>
                        <div class="flex items-center bg-gradient-to-r from-green-500/20 to-emerald-500/20 px-4 py-2 rounded-lg backdrop-blur-sm border border-green-400/30">
                            <svg class="w-5 h-5 mr-2 text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                            </svg>
                            <span class="font-bold">50,000+ Global Orders</span>
                        </div>
                        <div class="flex items-center bg-gradient-to-r from-purple-500/20 to-indigo-500/20 px-4 py-2 rounded-lg backdrop-blur-sm border border-purple-400/30">
                            <svg class="w-5 h-5 mr-2 text-purple-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                            </svg>
                            <span class="font-bold">Direct From Source</span>
                        </div>
                    </div>
                </div>

                <!-- Professional Business Dashboard -->
                <div class="md:w-1/3 mt-12 md:mt-0 relative">
                    <div class="relative w-full max-w-md mx-auto">
                        <!-- Main Dashboard Container -->
                        <div class="relative w-80 h-80 bg-white rounded-3xl shadow-2xl border border-gray-200 overflow-hidden">
                            <!-- Header -->
                            <div class="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4">
                                <div class="flex items-center justify-between">
                                    <div>
                                        <h3 class="font-bold text-lg">Global Network</h3>
                                        <p class="text-sm opacity-90">Live Operations</p>
                                    </div>
                                    <div class="w-3 h-3 bg-green-400 rounded-full animate-pulse"></div>
                                </div>
                            </div>

                            <!-- Stats Grid -->
                            <div class="p-6 space-y-4">
                                <!-- Suppliers Row -->
                                <div class="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Verified Suppliers</div>
                                            <div class="text-sm text-gray-600">Active Partners</div>
                                        </div>
                                    </div>
                                    <div class="text-2xl font-bold text-blue-600">500+</div>
                                </div>

                                <!-- Orders Row -->
                                <div class="flex items-center justify-between p-3 bg-purple-50 rounded-lg">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-purple-500 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 11V7a4 4 0 00-8 0v4M5 9h14l1 12H4L5 9z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Global Orders</div>
                                            <div class="text-sm text-gray-600">Successfully Delivered</div>
                                        </div>
                                    </div>
                                    <div class="text-2xl font-bold text-purple-600">50K+</div>
                                </div>

                                <!-- Shipping Row -->
                                <div class="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Avg. Delivery</div>
                                            <div class="text-sm text-gray-600">Direct Shipping</div>
                                        </div>
                                    </div>
                                    <div class="text-2xl font-bold text-green-600">7-14d</div>
                                </div>

                                <!-- Quality Row -->
                                <div class="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                                    <div class="flex items-center gap-3">
                                        <div class="w-10 h-10 bg-yellow-500 rounded-lg flex items-center justify-center">
                                            <svg class="w-5 h-5 text-white" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                            </svg>
                                        </div>
                                        <div>
                                            <div class="font-semibold text-gray-900">Quality Rating</div>
                                            <div class="text-sm text-gray-600">Customer Satisfaction</div>
                                        </div>
                                    </div>
                                    <div class="text-2xl font-bold text-yellow-600">4.9/5</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Flash Sale Timer Section -->
    <section class="py-16 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 relative overflow-hidden">
        <!-- Animated Background -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0 bg-gradient-to-r from-white/20 via-transparent to-white/20 animate-pulse"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <!-- Flash Sale Header -->
            <div class="text-center mb-12">
                <div class="inline-flex items-center gap-3 bg-white/20 backdrop-blur-sm rounded-full px-8 py-4 mb-6 border border-white/30">
                    <div class="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                    <span class="text-white font-bold text-lg">DIRECT SOURCING</span>
                    <div class="w-3 h-3 bg-yellow-400 rounded-full animate-pulse"></div>
                </div>

                <h2 class="text-4xl md:text-5xl font-black text-white mb-4">
                    Direct From Suppliers
                </h2>
                <p class="text-xl text-white/90 mb-8">Premium products shipped directly to you - No middleman markup!</p>

                <!-- Countdown Timer -->
                <div class="flex justify-center items-center gap-2 mb-8">
                    <div class="text-center">
                        <div class="bg-white/20 backdrop-blur-sm rounded-lg p-2 border border-white/30 min-w-[60px]">
                            <div id="hours" class="text-xl font-black text-white">23</div>
                            <div class="text-xs text-white/80 font-medium">Hours</div>
                        </div>
                    </div>
                    <div class="text-white text-lg font-bold animate-pulse">:</div>
                    <div class="text-center">
                        <div class="bg-white/20 backdrop-blur-sm rounded-lg p-2 border border-white/30 min-w-[60px]">
                            <div id="minutes" class="text-xl font-black text-white">59</div>
                            <div class="text-xs text-white/80 font-medium">Minutes</div>
                        </div>
                    </div>
                    <div class="text-white text-lg font-bold animate-pulse">:</div>
                    <div class="text-center">
                        <div class="bg-white/20 backdrop-blur-sm rounded-lg p-2 border border-white/30 min-w-[60px]">
                            <div id="seconds" class="text-xl font-black text-white">45</div>
                            <div class="text-xs text-white/80 font-medium">Seconds</div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Flash Sale Products -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <?php
                if (function_exists('wc_get_products')) {
                    // First try to get products on sale
                    $flash_sale_products = wc_get_products(array(
                        'limit' => 4,
                        'meta_query' => array(
                            array(
                                'key' => '_sale_price',
                                'value' => '',
                                'compare' => '!='
                            )
                        ),
                        'status' => 'publish'
                    ));

                    // If no sale products, get recent products
                    if (empty($flash_sale_products)) {
                        $flash_sale_products = wc_get_products(array(
                            'limit' => 4,
                            'status' => 'publish',
                            'orderby' => 'date',
                            'order' => 'DESC'
                        ));
                    }

                    // If still empty, get any published products
                    if (empty($flash_sale_products)) {
                        $flash_sale_products = wc_get_products(array(
                            'limit' => 4,
                            'status' => 'publish'
                        ));
                    }

                    foreach ($flash_sale_products as $product) {
                        $image_url = wp_get_attachment_image_url($product->get_image_id(), 'medium');
                        if (!$image_url) {
                            $image_url = wc_placeholder_img_src('medium');
                        }

                        $regular_price = $product->get_regular_price();
                        $sale_price = $product->get_sale_price();
                        $discount_percentage = 0;

                        if ($regular_price && $sale_price) {
                            $discount_percentage = round((($regular_price - $sale_price) / $regular_price) * 100);
                        }
                        ?>
                        <div class="group bg-white rounded-2xl shadow-2xl overflow-hidden hover:shadow-3xl transition-all duration-500 transform hover:-translate-y-2 border border-white/20 flex flex-col h-full">
                            <div class="relative overflow-hidden h-48">
                                <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($product->get_name()); ?>" class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">

                                <!-- Dropshipping Badge -->
                                <div class="absolute top-3 left-3 bg-gradient-to-r from-blue-500 to-purple-500 text-white px-3 py-1 rounded-full text-sm font-black shadow-lg">
                                    <?php echo $discount_percentage > 0 ? "-{$discount_percentage}%" : "DIRECT"; ?>
                                </div>

                                <!-- Fast Shipping Indicator -->
                                <div class="absolute top-3 right-3 bg-gradient-to-r from-green-500 to-emerald-500 text-white px-2 py-1 rounded-full text-xs font-bold animate-pulse">
                                    Fast Ship
                                </div>
                            </div>

                            <div class="p-6 flex flex-col flex-grow">
                                <h3 class="text-lg font-bold text-gray-900 mb-3 line-clamp-2">
                                    <?php echo esc_html($product->get_name()); ?>
                                </h3>

                                <!-- Price Section -->
                                <div class="mb-4 flex-grow">
                                    <div class="flex items-center gap-2">
                                        <?php
                                        // Use WooCommerce's built-in price HTML which handles tax and currency automatically
                                        $price_html = $product->get_price_html();
                                        if (!empty($price_html)) : ?>
                                            <span class="text-2xl font-black <?php echo $product->is_on_sale() ? 'text-red-600' : 'text-gray-900'; ?>">
                                                <?php echo $price_html; ?>
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                    <?php if ($discount_percentage > 0) : ?>
                                        <div class="text-sm text-blue-600 font-bold mt-1">
                                            <?php
                                            // Calculate savings using WooCommerce price functions
                                            $regular_price_amount = $product->get_regular_price();
                                            $sale_price_amount = $product->get_sale_price();
                                            if ($regular_price_amount && $sale_price_amount) {
                                                $savings = $regular_price_amount - $sale_price_amount;
                                                echo 'Direct Price: Save ' . wc_price($savings);
                                            }
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Dropshipping CTA -->
                                <a href="<?php echo esc_url(get_permalink($product->get_id())); ?>"
                                   class="w-full bg-gradient-to-r from-blue-500 to-purple-500 hover:from-blue-600 hover:to-purple-600 text-white font-bold py-3 px-6 rounded-xl transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-105 mt-auto">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                    </svg>
                                    <span>Order Direct!</span>
                                </a>
                            </div>
                        </div>
                        <?php
                    }
                } else {
                    echo '<p class="text-center text-white col-span-full">WooCommerce is required to display flash sale products.</p>';
                }
                ?>
            </div>

            <!-- Dropshipping CTA -->
            <div class="text-center mt-12">
                <a href="<?php echo function_exists('wc_get_page_permalink') ? esc_url(wc_get_page_permalink('shop')) : esc_url(home_url('/shop/')); ?>"
                   class="inline-flex items-center gap-3 bg-white text-blue-600 font-black py-4 px-8 rounded-2xl text-xl transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-3xl">
                    <span>Shop Direct From Source</span>
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>
            </div>
        </div>
    </section>

    <!-- Professional Features Section -->
    <section class="py-20 bg-gradient-to-br from-gray-50 to-white">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="text-center mb-16">
                <h2 class="text-3xl md:text-4xl font-bold text-gray-900 mb-4">Why Shop With Deal4u?</h2>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto">When you shop with us, you get premium products, unbeatable prices, and an exceptional online shopping experience designed for your convenience</p>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                <!-- Feature 1 -->
                <div class="group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                    <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-2xl flex items-center justify-center mx-auto mb-6 text-white shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Free Worldwide Shipping</h3>
                    <p class="text-gray-600 leading-relaxed">Get free delivery on orders over $50. We ship worldwide with fast, reliable service and real-time tracking for your peace of mind.</p>
                </div>

                <!-- Feature 2 -->
                <div class="group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                    <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-2xl flex items-center justify-center mx-auto mb-6 text-white shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Authentic Products Only</h3>
                    <p class="text-gray-600 leading-relaxed">Shop with confidence knowing every product is 100% authentic and brand new, backed by our comprehensive 2-year warranty.</p>
                </div>

                <!-- Feature 3 -->
                <div class="group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                    <div class="w-16 h-16 bg-gradient-to-br from-orange-500 to-orange-600 rounded-2xl flex items-center justify-center mx-auto mb-6 text-white shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">Safe & Secure Checkout</h3>
                    <p class="text-gray-600 leading-relaxed">Shop safely with our secure payment system. Your personal and payment information is always protected with bank-level encryption.</p>
                </div>

                <!-- Feature 4 -->
                <div class="group text-center p-8 bg-white rounded-2xl shadow-lg hover:shadow-2xl transition-all duration-300 transform hover:-translate-y-2 border border-gray-100">
                    <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-6 text-white shadow-lg group-hover:scale-110 transition-transform duration-300">
                        <svg class="w-8 h-8" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 5.636l-3.536 3.536m0 5.656l3.536 3.536M9.172 9.172L5.636 5.636m3.536 9.192L5.636 18.364M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-5 0a4 4 0 11-8 0 4 4 0 018 0z"></path>
                        </svg>
                    </div>
                    <h3 class="text-xl font-bold text-gray-900 mb-3">24/7 Customer Support</h3>
                    <p class="text-gray-600 leading-relaxed">Get help anytime you need it. Our friendly customer support team is available 24/7 to assist with orders, returns, and any questions.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Professional Featured Products Section -->
    <section class="py-24 bg-white relative">
        <!-- Subtle Background Pattern -->
        <div class="absolute inset-0 opacity-3">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #e2e8f0 1px, transparent 1px); background-size: 40px 40px;"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
            <div class="text-center mb-20">
                <div class="inline-block mb-4">
                    <span class="inline-flex items-center px-8 py-4 bg-gradient-to-r from-red-500 via-pink-500 to-purple-500 text-white text-lg font-bold rounded-full shadow-2xl animate-pulse border-2 border-yellow-400">
                        <svg class="w-6 h-6 mr-3 animate-bounce" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M12.395 2.553a1 1 0 00-1.45-.385c-.345.23-.614.558-.822.88-.214.33-.403.713-.57 1.116-.334.804-.614 1.768-.84 2.734a31.365 31.365 0 00-.613 3.58 2.64 2.64 0 01-.945-1.067c-.328-.68-.398-1.534-.398-2.654A1 1 0 005.05 6.05 6.981 6.981 0 003 11a7 7 0 1011.95-4.95c-.592-.591-.98-.985-1.348-1.467-.363-.476-.724-1.063-1.207-2.03zM12.12 15.12A3 3 0 017 13s.879.5 2.5.5c0-1 .5-4 1.25-4.5.5 1 .786 1.293 1.371 1.879A2.99 2.99 0 0113 13a2.99 2.99 0 01-.879 2.121z" clip-rule="evenodd"></path>
                        </svg>
                        🔥 MEGA HOT DEALS
                        <div class="ml-3 px-3 py-1 bg-yellow-400 text-black text-sm font-black rounded-full animate-bounce">
                            LIMITED TIME!
                        </div>
                    </span>
                </div>
                <h2 class="text-5xl md:text-6xl font-black text-transparent bg-clip-text bg-gradient-to-r from-purple-600 via-pink-600 to-blue-600 mb-8 leading-tight">
                    🛍️ Shop Best Sellers
                </h2>
                <div class="w-32 h-2 bg-gradient-to-r from-purple-500 via-pink-500 to-blue-500 mx-auto rounded-full mb-8 shadow-lg"></div>
                <p class="text-xl text-gray-600 max-w-3xl mx-auto leading-relaxed">
                    Browse our most popular products across all categories. These customer favorites offer the best value, quality, and style at unbeatable prices.
                </p>
            </div>

            <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-12">
                <?php
                if (function_exists('wc_get_products')) {
                    $featured_products = wc_get_products(array(
                        'limit' => 8,
                        'featured' => true,
                        'status' => 'publish'
                    ));
                    
                    if (empty($featured_products)) {
                        $featured_products = wc_get_products(array(
                            'limit' => 8,
                            'status' => 'publish',
                            'orderby' => 'popularity'
                        ));
                    }
                    
                    foreach ($featured_products as $product) {
                        $image_url = wp_get_attachment_image_url($product->get_image_id(), 'medium');
                        if (!$image_url) {
                            $image_url = wc_placeholder_img_src('medium');
                        }
                        ?>
                        <div class="group bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-[1.02] border border-gray-100 backdrop-blur-sm">
                            <div class="relative overflow-hidden h-64">
                                <img src="<?php echo esc_url($image_url); ?>" alt="<?php echo esc_attr($product->get_name()); ?>" class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">

                                <!-- Gradient Overlay on Hover -->
                                <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                                <!-- Sale Badge -->
                                <?php if ($product->is_on_sale()) : ?>
                                    <div class="absolute top-4 left-4 bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-2 rounded-full text-sm font-bold shadow-lg animate-pulse">
                                        <span class="flex items-center gap-1">
                                            🔥 SALE
                                        </span>
                                    </div>
                                <?php endif; ?>

                                <!-- Quick View Button -->
                                <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0">
                                    <button class="bg-white/90 backdrop-blur-sm text-gray-800 p-2 rounded-full shadow-lg hover:bg-white transition-colors duration-200">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                            
                            <div class="p-6 flex flex-col h-full">
                                <h3 class="text-xl font-bold text-gray-900 mb-3 line-clamp-2 leading-tight">
                                    <a href="<?php echo get_permalink($product->get_id()); ?>" class="hover:text-transparent hover:bg-gradient-to-r hover:from-blue-600 hover:to-purple-600 hover:bg-clip-text transition-all duration-300">
                                        <?php echo esc_html($product->get_name()); ?>
                                    </a>
                                </h3>

                                <!-- Price Section -->
                                <div class="mb-4">
                                    <?php
                                    // Use WooCommerce's built-in price HTML which handles tax and currency automatically
                                    $price_html = $product->get_price_html();
                                    if (!empty($price_html)) : ?>
                                        <div class="text-2xl font-extrabold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                                            <?php echo $price_html; ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if ($product->is_on_sale()) : ?>
                                        <div class="text-sm text-green-600 font-semibold mt-1">
                                            <?php
                                            // Calculate savings using WooCommerce functions
                                            $regular_price = $product->get_regular_price();
                                            $sale_price = $product->get_sale_price();
                                            if ($regular_price && $sale_price) {
                                                $savings = $regular_price - $sale_price;
                                                echo '💰 Save ' . wc_price($savings);
                                            }
                                            ?>
                                        </div>
                                    <?php endif; ?>
                                </div>

                                <!-- Rating Section -->
                                <div class="flex items-center gap-2 mb-4">
                                    <div class="flex text-yellow-400">
                                        <?php
                                        $rating = $product->get_average_rating();
                                        for ($i = 1; $i <= 5; $i++) {
                                            if ($i <= $rating) {
                                                echo '<svg class="w-4 h-4 fill-current" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>';
                                            } else {
                                                echo '<svg class="w-4 h-4 fill-current text-gray-300" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>';
                                            }
                                        }
                                        ?>
                                    </div>
                                    <span class="text-gray-600 text-sm font-medium">(<?php echo $product->get_review_count(); ?> reviews)</span>
                                </div>
                                
                                <!-- Action Buttons -->
                                <div class="mt-auto pt-4">
                                    <?php if ($product->is_purchasable() && $product->is_in_stock()): ?>
                                        <?php if ($product->is_type('simple')): ?>
                                            <!-- Simple Product: Enhanced Add to Cart + Quick View -->
                                            <div class="space-y-3">
                                                <!-- Primary Add to Cart Button -->
                                                <a href="<?php echo esc_url(home_url('/?add-to-cart=' . $product->get_id())); ?>"
                                                   data-quantity="1"
                                                   data-product_id="<?php echo esc_attr($product->get_id()); ?>"
                                                   data-product_sku="<?php echo esc_attr($product->get_sku()); ?>"
                                                   class="group/btn w-full bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-[1.02] button product_type_simple add_to_cart_button ajax_add_to_cart relative overflow-hidden"
                                                   aria-label="<?php echo esc_attr($product->add_to_cart_description()); ?>"
                                                   rel="nofollow">
                                                    <!-- Button Shine Effect -->
                                                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700"></div>

                                                    <svg class="w-5 h-5 transition-transform group-hover/btn:scale-110 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
                                                    </svg>
                                                    <span class="text-lg relative z-10">Add to Cart</span>
                                                </a>

                                                <!-- Secondary Quick View Button -->
                                                <a href="<?php echo esc_url(get_permalink($product->get_id())); ?>"
                                                   class="w-full bg-white border-2 border-gray-200 hover:border-blue-300 text-gray-700 hover:text-blue-600 font-semibold py-3 px-4 rounded-xl transition-all duration-300 flex items-center justify-center gap-2 hover:shadow-md"
                                                   title="View Product Details">
                                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                    </svg>
                                                    <span>Quick View</span>
                                                </a>
                                            </div>
                                        <?php elseif ($product->is_type('variable')): ?>
                                            <!-- Variable Product: Enhanced Select Options -->
                                            <a href="<?php echo esc_url(get_permalink($product->get_id())); ?>"
                                               class="group/btn w-full bg-gradient-to-r from-purple-500 via-pink-500 to-indigo-500 hover:from-purple-600 hover:via-pink-600 hover:to-indigo-600 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-[1.02] relative overflow-hidden">
                                                <!-- Button Shine Effect -->
                                                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700"></div>

                                                <svg class="w-5 h-5 transition-transform group-hover/btn:scale-110 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                </svg>
                                                <span class="text-lg relative z-10">Select Options</span>
                                            </a>
                                        <?php else: ?>
                                            <!-- Other Product Types: Enhanced View Product -->
                                            <a href="<?php echo esc_url(get_permalink($product->get_id())); ?>"
                                               class="group/btn w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-[1.02] relative overflow-hidden">
                                                <!-- Button Shine Effect -->
                                                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700"></div>

                                                <svg class="w-5 h-5 transition-transform group-hover/btn:scale-110 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                </svg>
                                                <span class="text-lg relative z-10">View Product</span>
                                            </a>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        <!-- Out of Stock or Not Purchasable -->
                                        <button class="w-full bg-gradient-to-r from-gray-400 to-gray-500 text-white font-bold py-4 px-6 rounded-xl cursor-not-allowed opacity-75 flex items-center justify-center gap-2">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                            </svg>
                                            <span class="text-lg"><?php echo $product->is_in_stock() ? 'Not Available' : 'Out of Stock'; ?></span>
                                        </button>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                        <?php
                    }
                } else {
                    echo '<p class="text-center text-gray-600 col-span-full">WooCommerce is required to display products.</p>';
                }
                ?>
            </div>
            
            <!-- Enhanced View All Products Button -->
            <div class="text-center mt-16">
                <a href="<?php echo function_exists('wc_get_page_permalink') ? esc_url(wc_get_page_permalink('shop')) : esc_url(home_url('/shop/')); ?>"
                   class="group/cta inline-flex items-center gap-3 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white font-bold py-5 px-10 rounded-2xl text-xl transition-all duration-300 transform hover:scale-105 shadow-2xl hover:shadow-3xl relative overflow-hidden">
                    <!-- Button Shine Effect -->
                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover/cta:translate-x-full transition-transform duration-700"></div>

                    <svg class="w-6 h-6 transition-transform group-hover/cta:scale-110 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                    <span class="relative z-10">Explore All Products</span>
                    <svg class="w-5 h-5 transition-transform group-hover/cta:translate-x-1 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                    </svg>
                </a>

                <!-- Decorative Elements -->
                <div class="mt-8 flex justify-center space-x-2">
                    <div class="w-2 h-2 bg-blue-400 rounded-full animate-pulse"></div>
                    <div class="w-2 h-2 bg-purple-400 rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
                    <div class="w-2 h-2 bg-indigo-400 rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
                </div>
            </div>
        </div>
    </section>
</main>

<script>
// Flash Sale Countdown Timer - Inline to ensure it loads
document.addEventListener('DOMContentLoaded', function() {
    function initFlashSaleTimer() {
        const hoursElement = document.getElementById('hours');
        const minutesElement = document.getElementById('minutes');
        const secondsElement = document.getElementById('seconds');

        if (!hoursElement || !minutesElement || !secondsElement) {
            console.log('Timer elements not found');
            return;
        }

        console.log('Timer elements found, starting countdown...');

        // Set end time to 24 hours from now
        const endTime = new Date().getTime() + (24 * 60 * 60 * 1000);

        function updateTimer() {
            const now = new Date().getTime();
            const timeLeft = endTime - now;

            if (timeLeft > 0) {
                const hours = Math.floor((timeLeft % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((timeLeft % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeLeft % (1000 * 60)) / 1000);

                hoursElement.textContent = hours.toString().padStart(2, '0');
                minutesElement.textContent = minutes.toString().padStart(2, '0');
                secondsElement.textContent = seconds.toString().padStart(2, '0');

                // Add pulse animation when time is running low
                if (timeLeft < 3600000) { // Less than 1 hour
                    hoursElement.parentElement.classList.add('animate-pulse');
                    minutesElement.parentElement.classList.add('animate-pulse');
                    secondsElement.parentElement.classList.add('animate-pulse');
                }
            } else {
                // Timer expired - reset to 24 hours
                hoursElement.textContent = '24';
                minutesElement.textContent = '00';
                secondsElement.textContent = '00';

                // Restart timer
                setTimeout(() => {
                    initFlashSaleTimer();
                }, 1000);
            }
        }

        // Update timer immediately and then every second
        updateTimer();
        setInterval(updateTimer, 1000);
    }

    // Initialize timer
    initFlashSaleTimer();
});
</script>

<?php get_footer(); ?>
