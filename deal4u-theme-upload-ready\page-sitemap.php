<?php
/**
 * Template Name: Sitemap Page
 */

get_header(); ?>

<main class="main-content">
    <div class="container mx-auto px-4 py-8">
        <!-- Page Header -->
        <div class="text-center mb-12">
            <h1 class="text-4xl font-bold text-gray-900 mb-4">Sitemap</h1>
            <p class="text-lg text-gray-600">Find all the pages and sections of our website</p>
        </div>

        <!-- Sitemap Content -->
        <div class="max-w-6xl mx-auto">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                
                <!-- Main Pages -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                        <svg class="w-6 h-6 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2H5a2 2 0 00-2-2z"></path>
                        </svg>
                        Main Pages
                    </h2>
                    <ul class="space-y-2">
                        <li><a href="<?php echo home_url('/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">Home</a></li>
                        <li><a href="<?php echo home_url('/shop/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">Shop</a></li>
                        <li><a href="<?php echo home_url('/categories/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">Categories</a></li>
                        <li><a href="<?php echo home_url('/about/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">About Us</a></li>
                        <li><a href="<?php echo home_url('/contact/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">Contact</a></li>
                        <li><a href="<?php echo home_url('/faq/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">FAQ</a></li>
                    </ul>
                </div>

                <!-- Account Pages -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                        <svg class="w-6 h-6 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                        </svg>
                        Account
                    </h2>
                    <ul class="space-y-2">
                        <li><a href="<?php echo home_url('/login/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">Login</a></li>
                        <li><a href="<?php echo home_url('/register/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">Register</a></li>
                        <li><a href="<?php echo home_url('/my-account/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">My Account</a></li>
                        <li><a href="<?php echo home_url('/track-order/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">Track Order</a></li>
                    </ul>
                </div>

                <!-- Shopping -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                        <svg class="w-6 h-6 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-2.5 5M7 13l2.5 5m6-5v6a2 2 0 01-2 2H9a2 2 0 01-2-2v-6m8 0V9a2 2 0 00-2-2H9a2 2 0 00-2 2v4.01"></path>
                        </svg>
                        Shopping
                    </h2>
                    <ul class="space-y-2">
                        <li><a href="<?php echo home_url('/cart/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">Shopping Cart</a></li>
                        <li><a href="<?php echo home_url('/checkout/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">Checkout</a></li>
                        <li><a href="<?php echo home_url('/wishlist/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">Wishlist</a></li>
                    </ul>
                </div>

                <!-- Product Categories -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                        <svg class="w-6 h-6 mr-2 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                        Categories
                    </h2>
                    <ul class="space-y-2">
                        <?php
                        // Get WooCommerce product categories
                        if (function_exists('get_terms')) {
                            $product_categories = get_terms(array(
                                'taxonomy' => 'product_cat',
                                'hide_empty' => false,
                                'number' => 10
                            ));
                            
                            if (!empty($product_categories) && !is_wp_error($product_categories)) {
                                foreach ($product_categories as $category) {
                                    echo '<li><a href="' . esc_url(get_term_link($category)) . '" class="text-blue-600 hover:text-blue-800 hover:underline">' . esc_html($category->name) . '</a></li>';
                                }
                            } else {
                                // Fallback categories
                                echo '<li><a href="' . home_url('/product-category/gaming/') . '" class="text-blue-600 hover:text-blue-800 hover:underline">Gaming</a></li>';
                                echo '<li><a href="' . home_url('/product-category/electronics/') . '" class="text-blue-600 hover:text-blue-800 hover:underline">Electronics</a></li>';
                                echo '<li><a href="' . home_url('/product-category/accessories/') . '" class="text-blue-600 hover:text-blue-800 hover:underline">Accessories</a></li>';
                            }
                        }
                        ?>
                    </ul>
                </div>

                <!-- Legal Pages -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                        <svg class="w-6 h-6 mr-2 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                        </svg>
                        Legal
                    </h2>
                    <ul class="space-y-2">
                        <li><a href="<?php echo home_url('/terms-of-service/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">Terms of Service</a></li>
                        <li><a href="<?php echo home_url('/privacy-policy/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">Privacy Policy</a></li>
                        <li><a href="<?php echo home_url('/shipping-info/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">Shipping Information</a></li>
                        <li><a href="<?php echo home_url('/sitemap/'); ?>" class="text-blue-600 hover:text-blue-800 hover:underline">Sitemap</a></li>
                    </ul>
                </div>

                <!-- Recent Products -->
                <div class="bg-white rounded-lg shadow-lg p-6">
                    <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                        <svg class="w-6 h-6 mr-2 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                        </svg>
                        Recent Products
                    </h2>
                    <ul class="space-y-2">
                        <?php
                        // Get recent products
                        if (function_exists('wc_get_products')) {
                            $recent_products = wc_get_products(array(
                                'limit' => 5,
                                'orderby' => 'date',
                                'order' => 'DESC',
                                'status' => 'publish'
                            ));
                            
                            if (!empty($recent_products)) {
                                foreach ($recent_products as $product) {
                                    echo '<li><a href="' . esc_url(get_permalink($product->get_id())) . '" class="text-blue-600 hover:text-blue-800 hover:underline">' . esc_html($product->get_name()) . '</a></li>';
                                }
                            } else {
                                echo '<li class="text-gray-500">No products found</li>';
                            }
                        }
                        ?>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Search Section -->
        <div class="mt-12 text-center">
            <div class="bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-8">
                <h3 class="text-2xl font-bold text-gray-900 mb-4">Can't Find What You're Looking For?</h3>
                <p class="text-gray-600 mb-6">Use our search feature to find specific products or pages.</p>
                <div class="max-w-md mx-auto">
                    <?php get_search_form(); ?>
                </div>
            </div>
        </div>
    </div>
</main>

<?php get_footer(); ?>
