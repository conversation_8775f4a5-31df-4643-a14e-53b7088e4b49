<?php
/**
 * The Template for displaying all single products
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product.php.
 */

defined( 'ABSPATH' ) || exit;

get_header(); ?>

<div class="min-h-screen bg-gray-50">
    <!-- Breadcrumb Navigation -->
    <nav class="bg-white border-b border-gray-200 py-4">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center space-x-2 text-sm text-gray-600">
                <a href="<?php echo esc_url(home_url('/')); ?>" class="hover:text-blue-600 transition-colors duration-200">Home</a>
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>" class="hover:text-blue-600 transition-colors duration-200">Shop</a>
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <span class="text-gray-900 font-medium"><?php the_title(); ?></span>
            </div>
        </div>
    </nav>

    <!-- Main Product Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <?php while ( have_posts() ) : ?>
            <?php the_post(); ?>
            <?php global $product; ?>

            <div class="bg-white rounded-3xl shadow-xl overflow-hidden">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 p-8 lg:p-12">
                    <!-- Product Images -->
                    <div class="space-y-6">
                        <!-- Main Product Image -->
                        <div class="aspect-square bg-gray-100 rounded-2xl overflow-hidden">
                            <?php
                            $image_id = $product->get_image_id();
                            if ($image_id) {
                                $image_url = wp_get_attachment_image_url($image_id, 'large');
                                $image_alt = get_post_meta($image_id, '_wp_attachment_image_alt', true);
                            } else {
                                $image_url = wc_placeholder_img_src('large');
                                $image_alt = 'Product placeholder';
                            }
                            ?>
                            <img src="<?php echo esc_url($image_url); ?>" 
                                 alt="<?php echo esc_attr($image_alt ?: $product->get_name()); ?>" 
                                 class="w-full h-full object-cover hover:scale-105 transition-transform duration-500">
                        </div>

                        <!-- Sale Badge -->
                        <?php if ($product->is_on_sale()) : ?>
                            <div class="absolute top-6 left-6 bg-gradient-to-r from-red-500 to-pink-500 text-white px-4 py-2 rounded-full text-sm font-bold shadow-lg animate-pulse">
                                SALE - Save <?php echo esc_html(round((($product->get_regular_price() - $product->get_sale_price()) / $product->get_regular_price()) * 100)); ?>%
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Product Details -->
                    <div class="space-y-8">
                        <!-- Product Title -->
                        <div>
                            <h1 class="text-4xl lg:text-5xl font-black text-gray-900 mb-4 leading-tight">
                                <?php the_title(); ?>
                            </h1>
                            
                            <!-- Product Rating -->
                            <div class="flex items-center gap-4 mb-6">
                                <div class="flex items-center">
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <svg class="w-5 h-5 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                    <?php endfor; ?>
                                </div>
                                <span class="text-gray-600">(4.8/5 - 127 reviews)</span>
                            </div>
                        </div>

                        <!-- Price -->
                        <div class="space-y-2">
                            <div class="flex items-baseline gap-4">
                                <?php if ($product->is_on_sale()) : ?>
                                    <span class="text-4xl font-black text-blue-600">
                                        <?php echo wc_price($product->get_sale_price()); ?>
                                    </span>
                                    <span class="text-2xl text-gray-500 line-through">
                                        <?php echo wc_price($product->get_regular_price()); ?>
                                    </span>
                                <?php else : ?>
                                    <span class="text-4xl font-black text-gray-900">
                                        <?php echo wc_price($product->get_price()); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            <p class="text-gray-600">Free shipping on orders over $50</p>
                        </div>

                        <!-- Stock Status -->
                        <div class="flex items-center gap-3">
                            <?php if ($product->is_in_stock()) : ?>
                                <div class="flex items-center gap-2 px-4 py-2 bg-green-100 text-green-800 rounded-full text-sm font-semibold">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    In Stock - Ready to Ship
                                </div>
                            <?php else : ?>
                                <div class="flex items-center gap-2 px-4 py-2 bg-red-100 text-red-800 rounded-full text-sm font-semibold">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    Out of Stock
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Product Description -->
                        <div class="prose prose-lg max-w-none">
                            <h3 class="text-xl font-bold text-gray-900 mb-4">Product Description</h3>
                            <div class="text-gray-700 leading-relaxed">
                                <?php echo $product->get_description() ?: $product->get_short_description() ?: 'Premium quality product with exceptional features and performance.'; ?>
                            </div>
                        </div>

                        <!-- Add to Cart Form -->
                        <div class="space-y-6">
                            <?php if ($product->is_purchasable() && $product->is_in_stock()) : ?>
                                <form class="cart" action="<?php echo esc_url(apply_filters('woocommerce_add_to_cart_form_action', $product->get_permalink())); ?>" method="post" enctype='multipart/form-data'>
                                    
                                    <!-- Quantity Selector -->
                                    <div class="flex items-center gap-4 mb-6">
                                        <label class="text-lg font-semibold text-gray-900">Quantity:</label>
                                        <div class="flex items-center border-2 border-gray-200 rounded-xl overflow-hidden">
                                            <button type="button" class="px-4 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-bold transition-colors duration-200" onclick="decreaseQuantity()">-</button>
                                            <input type="number" id="quantity" name="quantity" value="1" min="1" class="w-20 px-4 py-3 text-center border-0 focus:ring-0 font-semibold text-lg">
                                            <button type="button" class="px-4 py-3 bg-gray-100 hover:bg-gray-200 text-gray-700 font-bold transition-colors duration-200" onclick="increaseQuantity()">+</button>
                                        </div>
                                    </div>

                                    <!-- Add to Cart Button -->
                                    <button type="submit" name="add-to-cart" value="<?php echo esc_attr($product->get_id()); ?>" 
                                            class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-6 px-8 rounded-2xl transition-all duration-300 text-xl shadow-lg hover:shadow-xl transform hover:scale-[1.02] flex items-center justify-center gap-3">
                                        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
                                        </svg>
                                        Add to Cart - <?php echo wc_price($product->get_price()); ?>
                                    </button>
                                </form>
                            <?php else : ?>
                                <button disabled class="w-full bg-gray-400 text-white font-bold py-6 px-8 rounded-2xl text-xl cursor-not-allowed">
                                    Currently Unavailable
                                </button>
                            <?php endif; ?>

                            <!-- Additional Actions -->
                            <div class="flex gap-4">
                                <button class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center gap-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                    </svg>
                                    Add to Wishlist
                                </button>
                                <button class="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-800 font-semibold py-4 px-6 rounded-xl transition-all duration-200 flex items-center justify-center gap-2">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                    </svg>
                                    Share Product
                                </button>
                            </div>
                        </div>

                        <!-- Product Meta -->
                        <div class="border-t border-gray-200 pt-6 space-y-3">
                            <div class="flex items-center gap-3 text-gray-600">
                                <span class="font-semibold">SKU:</span>
                                <span><?php echo $product->get_sku() ?: 'N/A'; ?></span>
                            </div>
                            <div class="flex items-center gap-3 text-gray-600">
                                <span class="font-semibold">Category:</span>
                                <span><?php echo wc_get_product_category_list($product->get_id(), ', ') ?: 'Uncategorized'; ?></span>
                            </div>
                            <div class="flex items-center gap-3 text-gray-600">
                                <span class="font-semibold">Tags:</span>
                                <span><?php echo wc_get_product_tag_list($product->get_id(), ', ') ?: 'None'; ?></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <?php endwhile; ?>
    </main>
</div>

<!-- Quantity Control JavaScript -->
<script>
function increaseQuantity() {
    const input = document.getElementById('quantity');
    input.value = parseInt(input.value) + 1;
}

function decreaseQuantity() {
    const input = document.getElementById('quantity');
    if (parseInt(input.value) > 1) {
        input.value = parseInt(input.value) - 1;
    }
}

// Add to cart success notification
document.querySelector('form.cart')?.addEventListener('submit', function(e) {
    const button = this.querySelector('button[type="submit"]');
    button.innerHTML = `
        <svg class="w-6 h-6 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
        </svg>
        Adding to Cart...
    `;
    button.disabled = true;
});
</script>

<?php get_footer(); ?>
