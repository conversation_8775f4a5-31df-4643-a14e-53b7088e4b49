{
  "name": "deal4u-theme-testing",
  "version": "1.0.0",
  "description": "Playwright testing suite for Deal4u WordPress theme production verification",
  "main": "production-theme-test.js",
  "scripts": {
    "test": "npx playwright test production-theme-test.js",
    "test:headed": "npx playwright test production-theme-test.js --headed",
    "test:debug": "npx playwright test production-theme-test.js --debug",
    "test:report": "npx playwright show-report",
    "install-browsers": "npx playwright install"
  },
  "keywords": [
    "wordpress",
    "theme",
    "testing",
    "playwright",
    "woocommerce",
    "deal4u"
  ],
  "author": "Deal4u Theme",
  "license": "MIT",
  "devDependencies": {
    "@playwright/test": "^1.40.0"
  },
  "playwright": {
    "testDir": "./",
    "timeout": 30000,
    "expect": {
      "timeout": 5000
    },
    "fullyParallel": false,
    "forbidOnly": true,
    "retries": 2,
    "workers": 1,
    "reporter": [
      ["html"],
      ["list"]
    ],
    "use": {
      "baseURL": "https://yourdomain.com",
      "trace": "on-first-retry",
      "screenshot": "only-on-failure",
      "video": "retain-on-failure"
    },
    "projects": [
      {
        "name": "chromium",
        "use": {
          "...devices['Desktop Chrome']"
        }
      },
      {
        "name": "firefox",
        "use": {
          "...devices['Desktop Firefox']"
        }
      },
      {
        "name": "webkit",
        "use": {
          "...devices['Desktop Safari']"
        }
      },
      {
        "name": "mobile-chrome",
        "use": {
          "...devices['Pixel 5']"
        }
      },
      {
        "name": "mobile-safari",
        "use": {
          "...devices['iPhone 12']"
        }
      }
    ]
  }
}
