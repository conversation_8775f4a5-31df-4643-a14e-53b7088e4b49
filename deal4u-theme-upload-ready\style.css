/*
Theme Name: Deal4u Laravel Match
Description: WordPress theme that exactly matches the Laravel Deal4u project
Version: 1.0.1
Author: Deal4u Team
Author URI: https://deal4u.co
Text Domain: deal4u-laravel
Requires at least: 5.0
Tested up to: 6.4
Requires PHP: 7.4
License: GPL v2 or later
License URI: https://www.gnu.org/licenses/gpl-2.0.html
*/

/* CSS Custom Properties - FIXED: Define missing variables */
:root {
    /* Colors - Tailwind CSS compatible */
    --gray-50: #f9fafb;
    --gray-100: #f3f4f6;
    --gray-200: #e5e7eb;
    --gray-300: #d1d5db;
    --gray-400: #9ca3af;
    --gray-500: #6b7280;
    --gray-600: #4b5563;
    --gray-700: #374151;
    --gray-800: #1f2937;
    --gray-900: #111827;

    --blue-50: #eff6ff;
    --blue-100: #dbeafe;
    --blue-200: #bfdbfe;
    --blue-300: #93c5fd;
    --blue-400: #60a5fa;
    --blue-500: #3b82f6;
    --blue-600: #2563eb;
    --blue-700: #1d4ed8;
    --blue-800: #1e40af;
    --blue-900: #1e3a8a;

    --purple-50: #faf5ff;
    --purple-100: #f3e8ff;
    --purple-200: #e9d5ff;
    --purple-300: #d8b4fe;
    --purple-400: #c084fc;
    --purple-500: #a855f7;
    --purple-600: #9333ea;
    --purple-700: #7c3aed;
    --purple-800: #6b21a8;
    --purple-900: #581c87;

    --yellow-50: #fefce8;
    --yellow-100: #fef3c7;
    --yellow-200: #fde68a;
    --yellow-300: #fcd34d;
    --yellow-400: #fbbf24;
    --yellow-500: #f59e0b;
    --yellow-600: #d97706;
    --yellow-700: #b45309;
    --yellow-800: #92400e;
    --yellow-900: #78350f;

    --green-50: #f0fdf4;
    --green-100: #dcfce7;
    --green-200: #bbf7d0;
    --green-300: #86efac;
    --green-400: #4ade80;
    --green-500: #22c55e;
    --green-600: #16a34a;
    --green-700: #15803d;
    --green-800: #166534;
    --green-900: #14532d;

    --red-50: #fef2f2;
    --red-100: #fee2e2;
    --red-200: #fecaca;
    --red-300: #fca5a5;
    --red-400: #f87171;
    --red-500: #ef4444;
    --red-600: #dc2626;
    --red-700: #b91c1c;
    --red-800: #991b1b;
    --red-900: #7f1d1d;

    --orange-50: #fff7ed;
    --orange-100: #ffedd5;
    --orange-200: #fed7aa;
    --orange-300: #fdba74;
    --orange-400: #fb923c;
    --orange-500: #f97316;
    --orange-600: #ea580c;
    --orange-700: #c2410c;
    --orange-800: #9a3412;
    --orange-900: #7c2d12;
}

/* ADDED: Line clamp utilities for product descriptions */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* ADDED: WooCommerce cart form styling */
.cart {
    display: inline-block;
}

.cart button[type="submit"] {
    cursor: pointer;
    transition: all 0.3s ease;
}

.cart button[type="submit"]:hover {
    transform: scale(1.05);
}

/* PROFESSIONAL ENHANCEMENTS */

/* Enhanced Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes fadeInLeft {
    from {
        opacity: 0;
        transform: translateX(-30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes fadeInRight {
    from {
        opacity: 0;
        transform: translateX(30px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes shimmer {
    0% {
        background-position: -200px 0;
    }
    100% {
        background-position: calc(200px + 100%) 0;
    }
}

@keyframes bounce-slow {
    0%, 100% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Professional Loading States */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200px 100%;
    animation: shimmer 1.5s infinite;
}

.animate-bounce-slow {
    animation: bounce-slow 3s ease-in-out infinite;
}

.fade-in-up {
    animation: fadeInUp 0.8s ease-out;
}

.fade-in-left {
    animation: fadeInLeft 0.8s ease-out;
}

.fade-in-right {
    animation: fadeInRight 0.8s ease-out;
}

/* Enhanced Button Hover Effects */
.btn-hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-hover-lift:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 24px rgba(59, 130, 246, 0.4);
}

/* Professional Focus States */
.focus-ring:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    border-color: #3b82f6;
}

/* Enhanced Product Card Animations */
.product-card-enhanced {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.product-card-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(59, 130, 246, 0.05) 0%, rgba(139, 92, 246, 0.05) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card-enhanced:hover::before {
    opacity: 1;
}

.product-card-enhanced:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

/* Accessibility Improvements */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* Enhanced Wishlist Button Styling */
.wishlist-btn {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.wishlist-btn.active {
    background: linear-gradient(135deg, #ec4899, #be185d) !important;
    color: white !important;
    border-color: #be185d !important;
}

.wishlist-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(236, 72, 153, 0.3);
}

/* Enhanced Quick View Modal */
#quick-view-modal {
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out;
}

#quick-view-modal .bg-white {
    animation: slideUp 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Product Card Hover Effects */
.product:hover .wishlist-btn {
    transform: scale(1.05);
}

.product:hover .quick-view-btn {
    transform: scale(1.05);
}

/* WooCommerce Variations Dropdown Styling */
.variations select,
.form-select {
    appearance: none;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    min-height: 2.75rem;
    font-size: 1rem;
    transition: all 0.2s ease;
}

.variations select:focus,
.form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Single Product Page Enhancements */
.single-product .variations_form {
    margin-bottom: 2rem;
}

.single-product .single_variation_wrap {
    background: #f8fafc;
    border: 1px solid #e2e8f0;
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-top: 1rem;
}

.single-product .woocommerce-variation-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #059669;
    margin-bottom: 1rem;
}

.single-product .reset_variations {
    color: #6366f1;
    text-decoration: none;
    font-size: 0.875rem;
    margin-left: 0.5rem;
}

.single-product .reset_variations:hover {
    color: #4f46e5;
    text-decoration: underline;
}

/* Professional Button Gradients */
.bg-gradient-to-r.from-blue-500.to-purple-500 {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
}

.bg-gradient-to-r.from-blue-500.to-purple-500:hover {
    background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
    box-shadow: 0 8px 20px 0 rgba(59, 130, 246, 0.4);
    transform: translateY(-1px);
}

.bg-gradient-to-r.from-purple-500.to-indigo-500 {
    background: linear-gradient(135deg, #8b5cf6 0%, #6366f1 100%);
    box-shadow: 0 4px 14px 0 rgba(139, 92, 246, 0.3);
}

.bg-gradient-to-r.from-purple-500.to-indigo-500:hover {
    background: linear-gradient(135deg, #7c3aed 0%, #4f46e5 100%);
    box-shadow: 0 8px 20px 0 rgba(139, 92, 246, 0.4);
    transform: translateY(-1px);
}

/* Print Styles */
@media print {
    .site-header,
    .site-footer,
    .floating-whatsapp,
    .btn,
    #quick-view-modal {
        display: none !important;
    }

    body {
        background: white !important;
        color: black !important;
    }
}

/* Reset and Base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    scroll-behavior: smooth;
    font-family: 'Figtree', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

body {
    line-height: 1.6;
    color: #111827;
    background-color: #f9fafb;
    font-family: 'Figtree', sans-serif;
}

.container {
    max-width: 1280px;
    margin: 0 auto;
    padding: 0 1rem;
}

/* Header Styles */
.site-header {
    position: sticky;
    top: 0;
    z-index: 50;
    background: white;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
}

.top-banner {
    background: linear-gradient(to right, #2563eb, #9333ea);
    color: white;
    padding: 0.5rem 1rem;
    text-align: center;
    font-size: 0.875rem;
}

.top-banner .container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.fire-icon {
    animation: pulse 2s infinite;
}

.banner-text {
    font-weight: 600;
}

.main-header {
    background: white;
    border-bottom: 1px solid #e5e7eb;
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 4rem;
}

.logo {
    background: linear-gradient(to right, #2563eb, #9333ea);
    color: white;
    padding: 0.5rem 0.75rem;
    border-radius: 0.5rem;
    font-weight: bold;
    font-size: 1.25rem;
    text-decoration: none;
    transition: all 0.3s;
}

.logo:hover {
    background: linear-gradient(to right, #1d4ed8, #7c3aed);
}

.main-nav {
    display: none;
    gap: 2rem;
}

.nav-link {
    color: #374151;
    text-decoration: none;
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.3s;
}

.nav-link:hover,
.nav-link.active {
    color: #2563eb;
    border-bottom: 2px solid #2563eb;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.search-container {
    position: relative;
}

.search-toggle {
    background: none;
    border: none;
    padding: 0.5rem;
    color: #4b5563;
    cursor: pointer;
    transition: color 0.3s;
}

.search-toggle:hover {
    color: #2563eb;
}

.search-dropdown {
    display: none;
    position: absolute;
    right: 0;
    top: 100%;
    margin-top: 0.5rem;
    width: 20rem;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    padding: 1rem;
    z-index: 50;
}

.search-dropdown.active {
    display: block;
}

.search-input-container {
    position: relative;
    margin-bottom: 0.75rem;
}

.search-input {
    width: 100%;
    padding: 0.5rem 0.75rem 0.5rem 2.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 0.875rem;
}

.search-input:focus {
    outline: none;
    border-color: #2563eb;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.5);
}

.search-icon {
    position: absolute;
    left: 0.75rem;
    top: 50%;
    transform: translateY(-50%);
    color: #9ca3af;
}

.search-button {
    width: 100%;
    background: #2563eb;
    color: white;
    border: none;
    padding: 0.5rem;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: background 0.3s;
}

.search-button:hover {
    background: #1d4ed8;
}

.header-icon {
    position: relative;
    padding: 0.5rem;
    color: #4b5563;
    text-decoration: none;
    transition: color 0.3s;
}

.header-icon:hover {
    color: #2563eb;
}

.wishlist-icon:hover {
    color: #ef4444;
}

.icon-badge {
    position: absolute;
    top: -0.25rem;
    right: -0.25rem;
    background: #2563eb;
    color: white;
    font-size: 0.75rem;
    border-radius: 9999px;
    width: 1.25rem;
    height: 1.25rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
}

.wishlist-icon .icon-badge {
    background: #ef4444;
}

.user-menu {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.user-link {
    color: #4b5563;
    text-decoration: none;
    font-size: 0.875rem;
    font-weight: 500;
    transition: color 0.3s;
}

.user-link:hover {
    color: #2563eb;
}

.separator {
    color: #9ca3af;
}

.mobile-menu-toggle {
    display: block;
    background: none;
    border: none;
    padding: 0.5rem;
    color: #4b5563;
    cursor: pointer;
    transition: color 0.3s;
}

.mobile-menu-toggle:hover {
    color: #2563eb;
}

.mobile-menu {
    display: none;
    background: white;
    border-top: 1px solid #e5e7eb;
}

.mobile-menu.active {
    display: block;
}

.mobile-menu-content {
    padding: 0.5rem 1rem;
}

.mobile-nav-link {
    display: block;
    padding: 0.5rem 0.75rem;
    color: #374151;
    text-decoration: none;
    border-radius: 0.375rem;
    transition: all 0.3s;
}

.mobile-nav-link:hover,
.mobile-nav-link.active {
    color: #2563eb;
    background: #eff6ff;
}

/* Hero Section Styles */
.hero-section {
    background: linear-gradient(to right, #2563eb, #9333ea, #1e40af);
    color: white;
    position: relative;
    overflow: hidden;
}

.sale-banner {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    background: #fbbf24;
    color: #581c87;
    padding: 0.5rem 1rem;
    text-align: center;
    font-weight: bold;
    transform: skewY(-2deg);
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    z-index: 30;
}

.sale-banner .container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
}

.sale-text {
    font-size: 1.125rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.hero-content {
    padding: 4rem 0 6rem;
    position: relative;
    z-index: 10;
}

.hero-inner {
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
}

.floating-badge {
    position: absolute;
    top: 1rem;
    right: 1rem;
    animation: bounce-slow 3s infinite;
}

.badge-content {
    position: relative;
    width: 6rem;
    height: 6rem;
    background: #fbbf24;
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transform: rotate(12deg);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    text-align: center;
    padding: 0.5rem;
}

.badge-percent {
    font-size: 1.25rem;
    font-weight: 900;
    color: #581c87;
    line-height: 1;
}

.badge-off {
    font-size: 0.875rem;
    font-weight: bold;
    color: #581c87;
    line-height: 1;
}

.badge-code {
    font-size: 0.75rem;
    margin-top: 0.25rem;
    font-weight: 600;
    color: #6b21a8;
}

.hero-text {
    text-align: center;
    max-width: 42rem;
}

.hero-tag {
    display: inline-block;
    padding: 0.25rem 0.75rem;
    background: rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(4px);
    border-radius: 9999px;
    margin-bottom: 1.5rem;
    animation: pulse 2s infinite;
}

.hero-tag span {
    font-size: 0.75rem;
    font-weight: 600;
    letter-spacing: 0.05em;
}

.hero-title {
    font-size: 2.25rem;
    font-weight: bold;
    margin-bottom: 1.5rem;
    line-height: 1.2;
}

.gradient-text {
    background: linear-gradient(to right, #fbbf24, #f97316);
    -webkit-background-clip: text;
    background-clip: text;
    color: transparent;
}

.hero-description {
    font-size: 1.125rem;
    margin-bottom: 2rem;
    color: #dbeafe;
    max-width: 42rem;
}

.hero-buttons {
    display: flex;
    flex-direction: column;
    gap: 1rem;
    margin-bottom: 2rem;
}

.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    font-size: 1.125rem;
    font-weight: bold;
    text-decoration: none;
    transition: all 0.3s;
    transform: scale(1);
}

.btn:hover {
    transform: scale(1.05);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.btn-primary {
    background: #fbbf24;
    color: #581c87;
}

.btn-primary:hover {
    background: #fcd34d;
}

.btn-secondary {
    border: 2px solid white;
    color: white;
}

.btn-secondary:hover {
    background: white;
    color: #7c3aed;
}

.btn-arrow {
    width: 1.25rem;
    height: 1.25rem;
    margin-left: 0.5rem;
    transition: transform 0.3s;
}

.btn:hover .btn-arrow {
    transform: translateX(0.25rem);
}

.trust-indicators {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    gap: 1.5rem;
    font-size: 0.875rem;
    color: #bfdbfe;
}

.trust-item {
    display: flex;
    align-items: center;
}

.trust-icon {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.5rem;
}

.trust-icon.star {
    color: #fbbf24;
}

.trust-icon.check {
    color: #4ade80;
}

.trust-icon.shipping {
    color: #60a5fa;
}

.hero-visual {
    margin-top: 2rem;
    position: relative;
    width: 100%;
    max-width: 28rem;
    margin-left: auto;
    margin-right: auto;
}

.floating-card {
    position: absolute;
    background: white;
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
}

.card-1 {
    top: -1rem;
    left: -1rem;
    width: 6rem;
    height: 8rem;
    transform: rotate(12deg);
    animation: float 3s ease-in-out infinite;
}

.card-2 {
    top: -0.5rem;
    right: -1.5rem;
    width: 7rem;
    height: 9rem;
    transform: rotate(-6deg);
    animation: float-delayed 3s ease-in-out infinite 1.5s;
}

.card-image {
    width: 100%;
    height: 4rem;
    background: #e5e7eb;
    border-radius: 0.25rem;
    margin-bottom: 0.5rem;
}

.card-image.large {
    height: 5rem;
}

.card-line {
    height: 0.5rem;
    background: #e5e7eb;
    border-radius: 0.25rem;
    margin-bottom: 0.25rem;
}

.card-line.short {
    width: 66.666667%;
}

.card-line.medium {
    width: 75%;
}

.main-visual {
    position: relative;
    z-index: 10;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(4px);
    border-radius: 1rem;
    padding: 2rem;
    text-align: center;
}

.visual-icon {
    font-size: 3.75rem;
    margin-bottom: 1rem;
}

.visual-title {
    font-size: 1.25rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.visual-subtitle {
    color: #bfdbfe;
    font-size: 0.875rem;
}

/* WhatsApp Button */
.whatsapp-button {
    position: fixed;
    bottom: 1.5rem;
    right: 1.5rem;
    z-index: 50;
}

.whatsapp-link {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 3.5rem;
    height: 3.5rem;
    background: #22c55e;
    color: white;
    border-radius: 50%;
    box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    text-decoration: none;
    transition: all 0.3s;
    transform: scale(1);
}

.whatsapp-link:hover {
    background: #16a34a;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
}

.whatsapp-icon {
    width: 2rem;
    height: 2rem;
}

/* Animations */
@keyframes pulse {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes bounce-slow {
    0%, 100% {
        transform: translateY(-25%);
        animation-timing-function: cubic-bezier(0.8, 0, 1, 1);
    }
    50% {
        transform: translateY(0);
        animation-timing-function: cubic-bezier(0, 0, 0.2, 1);
    }
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(12deg); }
    50% { transform: translateY(-10px) rotate(12deg); }
}

@keyframes float-delayed {
    0%, 100% { transform: translateY(0px) rotate(-6deg); }
    50% { transform: translateY(-15px) rotate(-6deg); }
}

/* Product Single Page Styles */
.product-single {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    margin: 2rem 0;
}

.product-gallery {
    position: relative;
}

.product-images {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.main-image {
    position: relative;
    overflow: hidden;
    border-radius: 1rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    background: #f8fafc;
    aspect-ratio: 1;
}

.main-image img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform 0.3s ease;
    padding: 1rem;
}

.main-image:hover img {
    transform: scale(1.05);
}

.image-thumbnails {
    display: flex;
    gap: 0.75rem;
    flex-wrap: wrap;
    justify-content: center;
}

.thumbnail {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 0.5rem;
    cursor: pointer;
    border: 2px solid transparent;
    transition: all 0.3s ease;
    opacity: 0.7;
}

.thumbnail:hover,
.thumbnail.active {
    border-color: #3b82f6;
    opacity: 1;
    transform: scale(1.1);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.product-info {
    padding: 1rem 0;
}

.product-title {
    font-size: 2rem;
    font-weight: bold;
    color: #1f2937;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.product-price {
    font-size: 1.5rem;
    font-weight: bold;
    color: #059669;
    margin-bottom: 1rem;
}

.product-price del {
    color: #6b7280;
    font-weight: normal;
    margin-right: 0.5rem;
}

.product-description {
    color: #4b5563;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.cart-form {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 1rem;
    border: 1px solid #e5e7eb;
}

.quantity-selector {
    margin-bottom: 1rem;
}

.quantity-selector label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.quantity-selector input {
    width: 80px;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    text-align: center;
    font-weight: 600;
}

.cart-actions {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

.cart-actions .btn {
    flex: 1;
    min-width: 150px;
}

/* Product badges */
.product-badge {
    position: absolute;
    top: 1rem;
    left: 1rem;
    background: #ef4444;
    color: white;
    padding: 0.25rem 0.75rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: bold;
    text-transform: uppercase;
    z-index: 10;
    animation: pulse 2s infinite;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.product-badge.sale {
    background: linear-gradient(135deg, #ef4444, #dc2626);
}

.product-badge.new {
    background: linear-gradient(135deg, #10b981, #059669);
}

.product-badge.featured {
    background: linear-gradient(135deg, #8b5cf6, #7c3aed);
}

/* Pulse animation for badges */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Image loading animation */
.main-image img {
    animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: scale(0.95);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* Enhanced hover effects */
.main-image::before {
    content: '🔍 Click to zoom';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    opacity: 0;
    transition: opacity 0.3s ease;
    z-index: 5;
    pointer-events: none;
}

.main-image:hover::before {
    opacity: 1;
}

/* Product info enhancements */
.product-info h1 {
    background: linear-gradient(135deg, #1f2937, #374151);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Stock status indicator */
.stock-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.stock-status.in-stock {
    background: #dcfce7;
    color: #166534;
}

.stock-status.out-of-stock {
    background: #fee2e2;
    color: #991b1b;
}

.stock-status::before {
    content: '';
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: currentColor;
}

/* Product Cards Enhancement */
.woocommerce ul.products li.product,
.woocommerce-page ul.products li.product {
    background: white;
    border-radius: 1rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
    border: 1px solid #f3f4f6;
}

.woocommerce ul.products li.product:hover,
.woocommerce-page ul.products li.product:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

.woocommerce ul.products li.product img,
.woocommerce-page ul.products li.product img {
    width: 100% !important;
    height: 350px !important;
    object-fit: cover !important;
    transition: transform 0.3s ease !important;
    border-radius: 1rem !important;
    margin-bottom: 2rem !important;
    flex-shrink: 0 !important;
}

.woocommerce ul.products li.product:hover img,
.woocommerce-page ul.products li.product:hover img {
    transform: scale(1.05);
}

.woocommerce ul.products li.product .woocommerce-loop-product__title,
.woocommerce-page ul.products li.product .woocommerce-loop-product__title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 1rem 0 0.5rem 0;
    padding: 0 1rem;
}

.woocommerce ul.products li.product .price,
.woocommerce-page ul.products li.product .price {
    font-size: 1.25rem;
    font-weight: bold;
    color: #059669;
    padding: 0 1rem 1rem 1rem;
}

.woocommerce ul.products li.product .price del,
.woocommerce-page ul.products li.product .price del {
    color: #6b7280;
    font-weight: normal;
}

/* Enhanced Add to cart button styling - REMOVED CONFLICTING STYLES */
.woocommerce ul.products li.product .button,
.woocommerce-page ul.products li.product .button {
    /* Let the template handle colors and gradients */
    border: none !important;
    font-weight: 600 !important;
    transition: all 0.3s ease !important;
    margin: 0 !important;
    text-decoration: none !important;
    position: relative !important;
    overflow: hidden !important;
}

.woocommerce ul.products li.product .button:hover,
.woocommerce-page ul.products li.product .button:hover {
    /* Let the template handle hover colors */
    text-decoration: none !important;
}

.woocommerce ul.products li.product .button:before,
.woocommerce-page ul.products li.product .button:before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transition: left 0.5s;
}

.woocommerce ul.products li.product .button:hover:before,
.woocommerce-page ul.products li.product .button:hover:before {
    left: 100%;
}

/* Enhanced Product Title Styling - LARGER */
.woocommerce ul.products li.product h2,
.woocommerce ul.products li.product .woocommerce-loop-product__title,
.woocommerce-page ul.products li.product h2,
.woocommerce-page ul.products li.product .woocommerce-loop-product__title {
    font-size: 1.4rem !important;
    font-weight: 700 !important;
    color: #1f2937 !important;
    line-height: 1.4 !important;
    margin-bottom: 1rem !important;
    transition: color 0.3s ease !important;
    padding: 0 !important;
}

.woocommerce ul.products li.product:hover h2,
.woocommerce ul.products li.product:hover .woocommerce-loop-product__title,
.woocommerce-page ul.products li.product:hover h2,
.woocommerce-page ul.products li.product:hover .woocommerce-loop-product__title {
    color: #3b82f6;
}

/* Enhanced Price Styling - LARGER */
.woocommerce ul.products li.product .price,
.woocommerce-page ul.products li.product .price {
    font-size: 1.5rem !important;
    font-weight: 800 !important;
    color: #059669 !important;
    margin-bottom: 1.5rem !important;
    padding: 0 !important;
}

.woocommerce ul.products li.product .price del,
.woocommerce-page ul.products li.product .price del {
    color: #9ca3af;
    font-weight: 400;
    text-decoration: line-through;
    margin-right: 0.5rem;
}

.woocommerce ul.products li.product .price ins,
.woocommerce-page ul.products li.product .price ins {
    text-decoration: none;
    color: #dc2626;
    font-weight: 700;
}

/* Star Rating Enhancement */
.woocommerce .star-rating,
.woocommerce-page .star-rating {
    color: #fbbf24;
    font-size: 1rem;
}

/* Wishlist Button Styling */
.wishlist-btn {
    backdrop-filter: blur(10px);
}

/* Professional Pagination Styling */
.page-numbers {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.75rem 1rem;
    margin: 0 0.25rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    background-color: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    text-decoration: none;
    transition: all 0.3s ease;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.page-numbers:hover {
    background-color: #f3f4f6;
    border-color: #9ca3af;
    color: #111827;
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.page-numbers.current {
    background-color: #2563eb;
    border-color: #2563eb;
    color: #ffffff;
    font-weight: 600;
}

.page-numbers.current:hover {
    background-color: #1d4ed8;
    border-color: #1d4ed8;
    transform: translateY(-1px);
}

.page-numbers.prev,
.page-numbers.next {
    padding: 0.75rem 1.25rem;
    font-weight: 600;
}

.page-numbers.dots {
    border: none;
    background: none;
    color: #9ca3af;
    cursor: default;
}

.page-numbers.dots:hover {
    background: none;
    border: none;
    transform: none;
    box-shadow: none;
}

/* Pagination Container */
.woocommerce-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 2rem 0;
    padding: 1rem;
}

.woocommerce-pagination ul {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    list-style: none;
    margin: 0;
    padding: 0;
}

.woocommerce-pagination ul li {
    margin: 0;
}

/* Navigation Pagination (for other pages) */
.navigation.pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 2rem 0;
    padding: 1rem;
}

.navigation.pagination .nav-links {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

/* Hide unwanted sidebars and menus in main content */
.woocommerce-sidebar,
.shop-sidebar,
.widget-area,
.sidebar {
    display: none !important;
}

/* Ensure main content takes full width when sidebar is hidden */
.woocommerce .content-area,
.woocommerce-page .content-area {
    width: 100% !important;
    float: none !important;
}

/* Hide any WordPress default navigation menus that appear in content */
.wp-block-navigation,
.wp-block-navigation__container {
    display: none !important;
}

/* Hide category lists that might appear as menus */
.product_cat-list,
.category-list {
    display: none !important;
}

/* MEGA Eye-Catching Add to Cart Button - FORCE LARGE SIZE */
.add_to_cart_button {
    position: relative !important;
    overflow: hidden !important;
    min-height: 60px !important;
    height: 60px !important;
    font-size: 16px !important;
    font-weight: 900 !important;
    letter-spacing: 1px !important;
    text-transform: uppercase !important;
    box-shadow: 0 8px 25px rgba(255, 107, 0, 0.6) !important;
    border: 3px solid rgba(255, 255, 255, 0.3) !important;
    padding: 1rem 1.5rem !important;
    line-height: 1.2 !important;
}

.add_to_cart_button:hover {
    box-shadow: 0 8px 25px rgba(255, 107, 0, 0.6);
    border-color: rgba(255, 255, 255, 0.3);
}

.add_to_cart_button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s;
}

.add_to_cart_button:hover::before {
    left: 100%;
}

/* Pulse Animation for Add to Cart */
@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 4px 15px rgba(255, 107, 0, 0.4);
    }
    50% {
        box-shadow: 0 4px 20px rgba(255, 107, 0, 0.8);
    }
}

.add_to_cart_button.animate-pulse {
    animation: pulse-glow 2s infinite;
}

/* Enhanced Button Sizing for Product Cards */
.woocommerce ul.products li.product .space-y-2 > a,
.woocommerce ul.products li.product .space-y-2 > button,
.woocommerce-page ul.products li.product .space-y-2 > a,
.woocommerce-page ul.products li.product .space-y-2 > button {
    min-height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 700;
}

/* Quick Actions Buttons */
.quick-view-btn,
.wishlist-btn {
    min-height: 40px;
    transition: all 0.3s ease;
}

.quick-view-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.wishlist-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(236, 72, 153, 0.3);
}

.wishlist-btn.active {
    background-color: #ec4899 !important;
    color: white !important;
    border-color: #ec4899 !important;
}

/* MEGA TALL Product Card Enhanced Styling */
.woocommerce ul.products li.product,
.woocommerce-page ul.products li.product {
    transition: all 0.3s ease !important;
    border-radius: 1.5rem !important;
    overflow: hidden !important;
    background: white !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
    min-height: 600px !important;
    height: 600px !important;
    padding: 2rem !important;
    margin-bottom: 2rem !important;
    display: flex !important;
    flex-direction: column !important;
}

.woocommerce ul.products li.product:hover,
.woocommerce-page ul.products li.product:hover {
    transform: translateY(-8px) !important;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2) !important;
}

/* Ensure proper spacing in tall cards */
.woocommerce ul.products li.product .mt-auto,
.woocommerce-page ul.products li.product .mt-auto {
    margin-top: auto !important;
    padding-top: 1rem !important;
}

/* Product content area spacing */
.woocommerce ul.products li.product .p-4,
.woocommerce-page ul.products li.product .p-4 {
    padding: 1.5rem !important;
    height: 100% !important;
    display: flex !important;
    flex-direction: column !important;
}

/* Style for products without prices */
.woocommerce .no-price,
.woocommerce-page .no-price {
    color: #6b7280 !important;
    font-style: italic !important;
    font-size: 1rem !important;
}

/* Ensure price display is consistent */
.woocommerce ul.products li.product .price,
.woocommerce-page ul.products li.product .price {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* Ensure cart count is always visible and properly styled */
.cart-count {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    visibility: visible !important;
    opacity: 1 !important;
    position: absolute !important;
    top: -4px !important;
    right: -4px !important;
    background-color: #3b82f6 !important;
    color: white !important;
    font-size: 0.75rem !important;
    border-radius: 50% !important;
    height: 20px !important;
    width: 20px !important;
    font-weight: 600 !important;
    z-index: 10 !important;
}

/* Hide cart count when it's 0 */
.cart-count:empty,
.cart-count[data-count="0"] {
    display: none !important;
}

/* Product Variations Styling */
.product-variations {
    background: #f8fafc;
    padding: 1.5rem;
    border-radius: 1rem;
    border: 1px solid #e5e7eb;
    margin-bottom: 2rem;
}

.product-variations h4 {
    margin: 0 0 1.5rem 0;
    color: #1f2937;
    font-size: 1.125rem;
    font-weight: 600;
}

.variation-option {
    margin-bottom: 1.5rem;
}

.variation-label {
    display: block;
    margin-bottom: 0.75rem;
    font-weight: 600;
    color: #374151;
    font-size: 0.95rem;
}

.variation-choices {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
}

.variation-choice {
    padding: 0.75rem 1.25rem;
    border: 2px solid #d1d5db;
    background: white;
    border-radius: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;
    color: #374151;
    min-width: 60px;
    text-align: center;
}

.variation-choice:hover {
    border-color: #3b82f6;
    background: #eff6ff;
    color: #1d4ed8;
    transform: translateY(-1px);
}

.variation-choice.selected {
    border-color: #3b82f6;
    background: #3b82f6;
    color: white;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
}

.variation-choice:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f3f4f6;
    color: #9ca3af;
}

.selected-variation-info {
    margin-top: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
}

.variation-price {
    font-size: 1.25rem;
    font-weight: 700;
    color: #059669;
    margin-bottom: 0.5rem;
}

.variation-stock {
    font-size: 0.875rem;
    color: #6b7280;
}

.variation-stock.in-stock {
    color: #059669;
}

.variation-stock.out-of-stock {
    color: #dc2626;
}

/* Responsive variations */
@media (max-width: 640px) {
    .variation-choices {
        gap: 0.5rem;
    }

    .variation-choice {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
        min-width: 50px;
    }
}

/* Ensure proper product grid sizing - LARGER PRODUCTS */
.woocommerce ul.products,
.woocommerce-page ul.products {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)) !important;
    gap: 2rem !important;
    max-width: 100% !important;
}

@media (max-width: 639px) {
    .woocommerce ul.products,
    .woocommerce-page ul.products {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }
}

@media (min-width: 640px) {
    .woocommerce ul.products,
    .woocommerce-page ul.products {
        grid-template-columns: repeat(auto-fit, minmax(380px, 1fr)) !important;
        gap: 2rem !important;
    }
}

@media (min-width: 1024px) {
    .woocommerce ul.products,
    .woocommerce-page ul.products {
        grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
        max-width: 1400px !important;
        gap: 2.5rem !important;
    }
}

/* Force larger product cards */
.woocommerce ul.products li.product,
.woocommerce-page ul.products li.product {
    min-height: 500px !important;
    width: 100% !important;
}

/* Ensure buttons are always visible and large */
.woocommerce ul.products li.product .add_to_cart_button,
.woocommerce ul.products li.product a[href*="add-to-cart"],
.woocommerce ul.products li.product a[href*="/product/"] {
    min-height: 60px !important;
    font-size: 1.125rem !important;
    padding: 1.5rem 2rem !important;
    font-weight: 700 !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

/* Product card image sizing */
.woocommerce ul.products li.product img {
    height: 16rem !important;
    object-fit: cover !important;
    width: 100% !important;
}

/* Product card content padding */
.woocommerce ul.products li.product .p-6,
.woocommerce ul.products li.product .p-4 {
    padding: 1.5rem !important;
}

/* Product title sizing */
.woocommerce ul.products li.product h3,
.woocommerce ul.products li.product .woocommerce-loop-product__title {
    font-size: 1.25rem !important;
    line-height: 1.4 !important;
    margin-bottom: 1rem !important;
}

/* Price display */
.woocommerce ul.products li.product .price {
    font-size: 1.25rem !important;
    font-weight: 700 !important;
    margin-bottom: 1.5rem !important;
}

/* Single Product Page Styling */
.single-product .product-add-to-cart-section {
    margin: 2rem 0;
    padding: 2rem;
    background: #f9fafb;
    border-radius: 1rem;
    border: 1px solid #e5e7eb;
}

/* WooCommerce native form styling */
.woocommerce form.cart {
    margin: 1.5rem 0;
}

.woocommerce form.cart .quantity {
    margin-bottom: 1rem;
}

.woocommerce form.cart .quantity input {
    width: 80px;
    padding: 0.75rem;
    border: 2px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
}

/* Single add to cart button styling */
.woocommerce .single_add_to_cart_button {
    background: linear-gradient(135deg, #f59e0b, #d97706) !important;
    color: white !important;
    padding: 1rem 2rem !important;
    font-size: 1.125rem !important;
    font-weight: 700 !important;
    border: none !important;
    border-radius: 0.75rem !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
    min-height: 60px !important;
    width: 100% !important;
}

.woocommerce .single_add_to_cart_button:hover {
    background: linear-gradient(135deg, #d97706, #b45309) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.2) !important;
}

.woocommerce .single_add_to_cart_button:disabled {
    background: #9ca3af !important;
    cursor: not-allowed !important;
    transform: none !important;
}

/* Variable product variations */
.woocommerce .variations {
    margin-bottom: 1.5rem;
}

.woocommerce .variations td {
    padding: 0.5rem 0;
    border: none;
}

.woocommerce .variations select {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid #d1d5db;
    border-radius: 0.5rem;
    font-size: 1rem;
    background: white;
}

/* Price display on single product */
.woocommerce .price {
    font-size: 1.5rem !important;
    font-weight: 700 !important;
    color: #059669 !important;
    margin-bottom: 1rem !important;
}

.woocommerce .price del {
    color: #9ca3af !important;
    text-decoration: line-through !important;
}

.woocommerce .price ins {
    text-decoration: none !important;
    color: #dc2626 !important;
}

/* CHECKOUT PAGE FIXES */
.woocommerce-checkout {
    overflow: visible !important;
    width: 100% !important;
    max-width: none !important;
}

.woocommerce-checkout .col2-set {
    width: 100% !important;
    display: block !important;
    overflow: visible !important;
}

.woocommerce-checkout .col-1,
.woocommerce-checkout .col-2 {
    width: 100% !important;
    float: none !important;
    margin-bottom: 2rem;
}

/* Ensure checkout form fields are visible */
.woocommerce-checkout .form-row {
    width: 100% !important;
    margin-bottom: 1.5rem !important;
    clear: both !important;
}

.woocommerce-checkout input,
.woocommerce-checkout select,
.woocommerce-checkout textarea {
    width: 100% !important;
    box-sizing: border-box !important;
    padding: 0.875rem !important;
    border: 2px solid #e5e7eb !important;
    border-radius: 0.5rem !important;
    font-size: 1rem !important;
}

/* Fix checkout layout container */
.checkout-container {
    width: 100% !important;
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 0 1rem !important;
}

/* Responsive checkout layout */
@media (min-width: 1024px) {
    .checkout-container {
        display: flex !important;
        gap: 3rem !important;
        align-items: start !important;
    }

    .checkout-main {
        flex: 1 !important;
        min-width: 0 !important;
    }

    .checkout-sidebar {
        flex: 0 0 400px !important;
        min-width: 300px !important;
    }
}

@media (max-width: 1023px) {
    .checkout-container {
        display: block !important;
    }

    .checkout-main,
    .checkout-sidebar {
        width: 100% !important;
        margin-bottom: 2rem !important;
    }
}

/* ENHANCED FEATURED PRODUCTS SECTION STYLES */
.featured-products-section {
    background: linear-gradient(135deg, #f8fafc 0%, #e0f2fe 50%, #e8eaf6 100%);
}

/* Enhanced Product Card Animations */
.product-card-enhanced {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
}

.product-card-enhanced:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.15);
}

/* Button Shine Animation */
@keyframes shine {
    0% { transform: translateX(-100%) skewX(-12deg); }
    100% { transform: translateX(200%) skewX(-12deg); }
}

.btn-shine::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transform: skewX(-12deg);
    transition: left 0.7s;
}

.btn-shine:hover::before {
    left: 100%;
}

/* Enhanced Gradient Text */
.gradient-text {
    background: linear-gradient(135deg, #2563eb, #7c3aed, #4f46e5);
    -webkit-background-clip: text;
    background-clip: text;
    -webkit-text-fill-color: transparent;
}

/* Pulse Animation for Sale Badges */
@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 5px rgba(239, 68, 68, 0.5);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 20px rgba(239, 68, 68, 0.8);
        transform: scale(1.05);
    }
}

.sale-badge {
    animation: pulse-glow 2s infinite;
}

/* Enhanced Shadow Effects */
.shadow-3xl {
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* FORCE CONSISTENT BUTTON COLORS - OVERRIDE ALL CONFLICTS */
.woocommerce ul.products li.product a[href*="add-to-cart"],
.woocommerce ul.products li.product .add_to_cart_button,
.woocommerce ul.products li.product .ajax_add_to_cart {
    background: linear-gradient(135deg, #2563eb, #7c3aed, #4f46e5) !important;
    color: white !important;
}

.woocommerce ul.products li.product a[href*="add-to-cart"]:hover,
.woocommerce ul.products li.product .add_to_cart_button:hover,
.woocommerce ul.products li.product .ajax_add_to_cart:hover {
    background: linear-gradient(135deg, #1d4ed8, #6d28d9, #4338ca) !important;
}

/* ENHANCED SHOP PAGE STYLES */

/* Shop Header Enhancements */
.shop-header-pattern {
    background-image: radial-gradient(circle at 20% 80%, rgba(255,255,255,0.1) 1px, transparent 1px),
                      radial-gradient(circle at 80% 20%, rgba(255,255,255,0.1) 1px, transparent 1px);
    background-size: 40px 40px;
}

/* Enhanced Filter Styles */
.shop-filters {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.8);
}

.shop-filters select,
.shop-filters input {
    transition: all 0.3s ease;
    border: 2px solid #e5e7eb;
}

.shop-filters select:focus,
.shop-filters input:focus {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

/* Product Card Enhancements for Shop Page */
.product-card {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.95);
    transition: all 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-card:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Enhanced Sale Badge Animation */
.sale-badge {
    animation: pulse-glow 2s infinite;
    background: linear-gradient(135deg, #ef4444, #ec4899);
}

@keyframes pulse-glow {
    0%, 100% {
        box-shadow: 0 0 10px rgba(239, 68, 68, 0.5);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 25px rgba(239, 68, 68, 0.8);
        transform: scale(1.05);
    }
}

/* Quick Action Buttons */
.quick-view-btn,
.wishlist-btn {
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.quick-view-btn:hover,
.wishlist-btn:hover {
    transform: scale(1.1);
    box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.2);
}

/* Enhanced Pagination Styles */
.pagination-container {
    backdrop-filter: blur(10px);
    background: rgba(255, 255, 255, 0.8);
}

.pagination-link {
    transition: all 0.3s ease;
}

.pagination-link:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Active Filter Tags */
.filter-tag {
    background: linear-gradient(135deg, #dbeafe, #e0e7ff);
    color: #1e40af;
    transition: all 0.3s ease;
}

.filter-tag:hover {
    transform: scale(1.05);
    box-shadow: 0 2px 8px -2px rgba(0, 0, 0, 0.1);
}

/* Loading States */
.loading-shimmer {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Enhanced Button Shine Effects */
.btn-shine {
    position: relative;
    overflow: hidden;
}

.btn-shine::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transform: skewX(-25deg);
    transition: left 0.7s;
}

.btn-shine:hover::before {
    left: 100%;
}

/* Floating Elements Animation */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.floating-element {
    animation: float 3s ease-in-out infinite;
}

.floating-element:nth-child(2) {
    animation-delay: 1s;
}

.floating-element:nth-child(3) {
    animation-delay: 2s;
}

/* Enhanced Responsive Design */
@media (max-width: 768px) {
    .product-card {
        margin-bottom: 2rem;
    }

    .shop-filters {
        flex-direction: column;
        gap: 1rem;
    }

    .shop-filters select,
    .shop-filters input {
        width: 100%;
    }

    .pagination-container {
        padding: 1rem;
    }
}

/* ULTRA-PROFESSIONAL SHOP ENHANCEMENTS */

/* Advanced Floating Animations */
@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-10px) rotate(1deg); }
    66% { transform: translateY(-5px) rotate(-1deg); }
}

@keyframes float-delayed {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    33% { transform: translateY(-15px) rotate(-1deg); }
    66% { transform: translateY(-8px) rotate(1deg); }
}

@keyframes float-slow {
    0%, 100% { transform: translateY(0px) rotate(0deg); }
    50% { transform: translateY(-12px) rotate(0.5deg); }
}

.animate-float {
    animation: float 6s ease-in-out infinite;
}

.animate-float-delayed {
    animation: float-delayed 8s ease-in-out infinite;
    animation-delay: 2s;
}

.animate-float-slow {
    animation: float-slow 10s ease-in-out infinite;
    animation-delay: 4s;
}

/* Professional Glass Morphism Effects */
.glass-morphism {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.glass-morphism-dark {
    background: rgba(15, 23, 42, 0.95);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
}

/* Advanced Hover Effects */
.hover-lift {
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.3);
}

/* Professional Button Styles */
.btn-professional {
    position: relative;
    overflow: hidden;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 16px;
    padding: 16px 32px;
    font-weight: 700;
    font-size: 16px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 25px -5px rgba(102, 126, 234, 0.4);
}

.btn-professional::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.3), transparent);
    transition: left 0.7s;
}

.btn-professional:hover::before {
    left: 100%;
}

.btn-professional:hover {
    transform: translateY(-2px);
    box-shadow: 0 20px 40px -5px rgba(102, 126, 234, 0.6);
}

/* Enhanced Filter Styles */
.filter-professional {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 16px;
    padding: 16px 20px;
    font-size: 16px;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.filter-professional:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1);
    transform: translateY(-2px);
}

.filter-professional:hover {
    border-color: #9ca3af;
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
}

/* Professional Product Cards */
.product-card-professional {
    background: white;
    border-radius: 24px;
    overflow: hidden;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(0, 0, 0, 0.05);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

.product-card-professional::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.product-card-professional:hover::before {
    opacity: 1;
}

.product-card-professional:hover {
    transform: translateY(-12px) scale(1.03);
    box-shadow: 0 35px 60px -12px rgba(0, 0, 0, 0.25);
}

/* Professional Typography */
.text-professional {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 600;
    letter-spacing: -0.025em;
}

.text-professional-light {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-weight: 400;
    letter-spacing: -0.01em;
}

/* Advanced Loading States */
.loading-professional {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer-professional 2s infinite;
}

@keyframes shimmer-professional {
    0% { background-position: -200% 0; }
    100% { background-position: 200% 0; }
}

/* Professional Tooltips */
.tooltip-professional {
    position: relative;
    cursor: help;
}

.tooltip-professional::after {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 14px;
    white-space: nowrap;
    opacity: 0;
    pointer-events: none;
    transition: opacity 0.3s ease;
    z-index: 1000;
}

.tooltip-professional:hover::after {
    opacity: 1;
}

/* Professional Status Indicators */
.status-indicator {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
}

.status-in-stock {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.status-low-stock {
    background: rgba(251, 191, 36, 0.1);
    color: #d97706;
    border: 1px solid rgba(251, 191, 36, 0.2);
}

.status-out-of-stock {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Professional Pagination */
.pagination-professional {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: center;
    margin-top: 48px;
}

.pagination-professional a,
.pagination-professional span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 48px;
    height: 48px;
    border-radius: 12px;
    font-weight: 600;
    text-decoration: none;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.pagination-professional a {
    background: white;
    color: #6b7280;
    border-color: #e5e7eb;
}

.pagination-professional a:hover {
    background: #3b82f6;
    color: white;
    border-color: #3b82f6;
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(59, 130, 246, 0.3);
}

.pagination-professional .current {
    background: linear-gradient(135deg, #3b82f6, #8b5cf6);
    color: white;
    border-color: #3b82f6;
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .product-card-professional {
        background: rgba(31, 41, 55, 0.95);
        border-color: rgba(75, 85, 99, 0.3);
        color: white;
    }

    .filter-professional {
        background: rgba(31, 41, 55, 0.8);
        border-color: rgba(75, 85, 99, 0.5);
        color: white;
    }

    .glass-morphism {
        background: rgba(31, 41, 55, 0.95);
        border-color: rgba(75, 85, 99, 0.3);
    }
}

/* Clean Product Card Styling */
.woocommerce ul.products li.product {
    position: relative;
    cursor: pointer;
    transition: all 0.3s ease;
}

.woocommerce ul.products li.product:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px -10px rgba(0, 0, 0, 0.15);
}

.woocommerce ul.products li.product::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 3px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6, #ec4899);
    opacity: 0;
    transition: opacity 0.3s ease;
    border-radius: 12px 12px 0 0;
}

.woocommerce ul.products li.product:hover::before {
    opacity: 1;
}

/* Professional View Details Button */
.view-details-btn {
    background: linear-gradient(135deg, #1e293b, #0f172a);
    border: none;
    color: white;
    font-weight: 600;
    padding: 16px 24px;
    border-radius: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px -3px rgba(30, 41, 59, 0.4);
}

.view-details-btn:hover {
    background: linear-gradient(135deg, #0f172a, #020617);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px -5px rgba(30, 41, 59, 0.6);
}

/* Clean Product Grid */
.woocommerce ul.products {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
    gap: 2rem;
    list-style: none;
    padding: 0;
    margin: 0;
}

.woocommerce ul.products li.product {
    background: white;
    border-radius: 16px;
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
    display: flex;
    flex-direction: column;
    min-height: 400px;
}

/* Remove default WooCommerce styling */
.woocommerce ul.products li.product .button {
    margin: 0;
    width: 100%;
}

.woocommerce ul.products li.product .price {
    margin-bottom: 1rem;
}

/* Professional Stock Status */
.stock-status {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border-radius: 12px;
    font-size: 0.875rem;
    font-weight: 600;
}

.stock-status.in-stock {
    background: rgba(34, 197, 94, 0.1);
    color: #16a34a;
    border: 1px solid rgba(34, 197, 94, 0.2);
}

.stock-status.out-of-stock {
    background: rgba(239, 68, 68, 0.1);
    color: #dc2626;
    border: 1px solid rgba(239, 68, 68, 0.2);
}

/* Responsive Enhancements */
@media (max-width: 768px) {
    .product-card-professional {
        margin-bottom: 24px;
    }

    .filter-professional {
        width: 100%;
        margin-bottom: 16px;
    }

    .btn-professional {
        width: 100%;
        padding: 20px;
        font-size: 18px;
    }

    .woocommerce ul.products {
        grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
        gap: 1.5rem;
    }
}

/* Variable product buttons */
.woocommerce ul.products li.product a[href*="/product/"]:not([href*="add-to-cart"]) {
    background: linear-gradient(135deg, #8b5cf6, #6366f1) !important;
    color: white !important;
}

.woocommerce ul.products li.product a[href*="/product/"]:not([href*="add-to-cart"]):hover {
    background: linear-gradient(135deg, #7c3aed, #4f46e5) !important;
}

/* Related Products Section Styling */
.related-products-section {
    margin-top: 3rem;
    padding: 2rem 0;
}

.related-products-section h3 {
    font-size: 1.875rem;
    font-weight: 700;
    margin-bottom: 1.5rem;
    text-align: center;
}

.related-products-section .bg-gray-50 {
    background-color: #f9fafb;
    border-radius: 1rem;
    padding: 2rem;
}

.related-products-section .grid {
    display: grid;
    gap: 1.5rem;
}

.related-products-section .product-card {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    transition: all 0.3s ease;
}

.related-products-section .product-card:hover {
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    transform: translateY(-2px);
}

.related-products-section .product-card img {
    width: 100%;
    height: 12rem;
    object-fit: cover;
    transition: transform 0.3s ease;
}

.related-products-section .product-card:hover img {
    transform: scale(1.05);
}

.related-products-section .product-card h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.5rem;
    line-height: 1.4;
}

.related-products-section .product-card h4 a {
    text-decoration: none;
    color: inherit;
    transition: color 0.3s ease;
}

.related-products-section .product-card h4 a:hover {
    color: #2563eb;
}

.related-products-section .product-card .price {
    font-size: 1.125rem;
    font-weight: 700;
    color: #2563eb;
    margin-bottom: 1rem;
}

/* Line clamp utility for product titles */
.line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Responsive adjustments for related products */
@media (max-width: 640px) {
    .related-products-section .bg-gray-50 {
        padding: 1.5rem;
    }

    .related-products-section h3 {
        font-size: 1.5rem;
    }
}

/* Make buttons more clickable on mobile - COMPACT VERSION */
@media (max-width: 768px) {
    .add_to_cart_button {
        min-height: 40px !important;
        font-size: 0.875rem !important;
        padding: 0.625rem 0.875rem !important;
    }

    .quick-view-btn,
    .wishlist-btn {
        min-height: 36px !important;
        font-size: 0.75rem !important;
        padding: 0.5rem 0.75rem !important;
    }

    /* Make mobile text smaller */
    .hero-title {
        font-size: 2rem !important;
    }

    .hero-description {
        font-size: 0.875rem !important;
    }

    /* Make mobile feature boxes more compact */
    .group.text-center.p-8 {
        padding: 1rem !important;
    }

    .group.text-center.p-8 h3 {
        font-size: 0.875rem !important;
    }

    .group.text-center.p-8 p {
        font-size: 0.75rem !important;
    }

    /* Make mobile product cards more compact */
    .woocommerce ul.products li.product {
        min-height: 360px !important;
        height: 360px !important;
        padding: 1rem !important;
    }

    .woocommerce ul.products li.product img {
        height: 10rem !important;
    }

    .woocommerce ul.products li.product h2,
    .woocommerce ul.products li.product .woocommerce-loop-product__title {
        font-size: 0.875rem !important;
    }

    .woocommerce ul.products li.product .price {
        font-size: 0.875rem !important;
    }
}

/* Mega Eye-Catching Buttons for Single Product Page */
.mega-cart-btn {
    background: linear-gradient(45deg, #ff6b35, #f7931e, #ff6b35) !important;
    background-size: 200% 200% !important;
    animation: gradient-shift 3s ease infinite !important;
    color: white !important;
    font-size: 18px !important;
    font-weight: 900 !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    padding: 1rem 2rem !important;
    border-radius: 1rem !important;
    border: 3px solid #fff !important;
    box-shadow: 0 8px 25px rgba(255, 107, 53, 0.5), 0 0 0 1px rgba(255, 107, 53, 0.3) !important;
    position: relative !important;
    overflow: hidden !important;
    min-height: 60px !important;
    transform: perspective(1000px) rotateX(0deg) !important;
    transition: all 0.3s ease !important;
}

.mega-cart-btn:hover {
    transform: perspective(1000px) rotateX(-5deg) translateY(-5px) !important;
    box-shadow: 0 15px 35px rgba(255, 107, 53, 0.7), 0 0 0 2px rgba(255, 107, 53, 0.5) !important;
    animation: mega-pulse 1s ease infinite !important;
}

.mega-cart-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s;
}

.mega-cart-btn:hover::before {
    left: 100%;
}

.mega-wishlist-btn {
    background: linear-gradient(45deg, #ec4899, #be185d, #ec4899) !important;
    background-size: 200% 200% !important;
    animation: gradient-shift 3s ease infinite !important;
    color: white !important;
    font-size: 16px !important;
    font-weight: 700 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.5px !important;
    padding: 1rem 1.5rem !important;
    border-radius: 1rem !important;
    border: 2px solid #fff !important;
    box-shadow: 0 6px 20px rgba(236, 72, 153, 0.4) !important;
    min-height: 60px !important;
    transition: all 0.3s ease !important;
}

.mega-wishlist-btn:hover {
    transform: translateY(-3px) !important;
    box-shadow: 0 10px 25px rgba(236, 72, 153, 0.6) !important;
}

/* Animations */
@keyframes gradient-shift {
    0%, 100% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
}

@keyframes mega-pulse {
    0%, 100% {
        box-shadow: 0 15px 35px rgba(255, 107, 53, 0.7), 0 0 0 2px rgba(255, 107, 53, 0.5);
    }
    50% {
        box-shadow: 0 15px 35px rgba(255, 107, 53, 0.9), 0 0 0 4px rgba(255, 107, 53, 0.7);
    }
}

/* Enhanced Cart Actions Layout */
.cart-actions {
    display: flex !important;
    gap: 1rem !important;
    flex-wrap: wrap !important;
    margin-top: 1.5rem !important;
}

.cart-actions .btn {
    flex: 1 !important;
    min-width: 200px !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    gap: 0.5rem !important;
}

.wishlist-btn.active {
    background: #ef4444 !important;
    color: white !important;
}

/* Quick View Button */
.quick-view-btn {
    backdrop-filter: blur(10px);
}

/* Shop Filters Enhancement */
.shop-filters select,
.shop-filters input {
    transition: all 0.3s ease;
}

.shop-filters select:focus,
.shop-filters input:focus {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

/* Loading Animation */
@keyframes shimmer {
    0% {
        background-position: -468px 0;
    }
    100% {
        background-position: 468px 0;
    }
}

.loading-shimmer {
    animation: shimmer 1.2s ease-in-out infinite;
    background: linear-gradient(to right, #f6f7f8 8%, #edeef1 18%, #f6f7f8 33%);
    background-size: 800px 104px;
}

/* Mobile Responsiveness */
@media (max-width: 768px) {
    .woocommerce ul.products li.product,
    .woocommerce-page ul.products li.product {
        margin-bottom: 1.5rem;
    }

    .shop-filters {
        flex-direction: column;
        gap: 1rem;
    }

    .shop-filters select,
    .shop-filters input {
        width: 100%;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .product-single {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .main-image {
        aspect-ratio: 4/3;
    }

    .cart-actions {
        flex-direction: column;
    }

    .cart-actions .btn {
        min-width: auto;
    }
}

@media (min-width: 640px) {
    .hero-buttons {
        flex-direction: row;
        justify-content: center;
    }

    .trust-indicators {
        justify-content: center;
    }
}

@media (min-width: 768px) {
    .main-nav {
        display: flex;
    }

    .mobile-menu-toggle {
        display: none;
    }

    .hero-inner {
        flex-direction: row;
        align-items: center;
    }

    .hero-text {
        width: 66.666667%;
        text-align: left;
    }

    .hero-visual {
        width: 33.333333%;
        margin-top: 0;
    }

    .hero-title {
        font-size: 2.75rem !important;
    }

    .hero-description {
        font-size: 1rem !important;
    }

    .floating-badge {
        top: 2rem;
        right: 2rem;
    }

    .badge-content {
        width: 8rem;
        height: 8rem;
    }

    .badge-percent {
        font-size: 1.5rem;
    }

    .badge-off {
        font-size: 1rem;
    }

    .trust-indicators {
        justify-content: flex-start;
    }
}

@media (min-width: 1024px) {
    .container {
        padding: 0 2rem;
    }
}

/* COMPACT DESIGN OVERRIDES - Make text and buttons smaller */

/* Reduce feature box text sizes */
.group.text-center.p-8 h3 {
    font-size: 1rem !important;
    margin-bottom: 0.75rem !important;
}

.group.text-center.p-8 p {
    font-size: 0.875rem !important;
    line-height: 1.4 !important;
}

.group.text-center.p-8 {
    padding: 1.5rem !important;
}

/* Reduce trust indicators text size */
.trust-indicators {
    font-size: 0.75rem !important;
    gap: 1rem !important;
}

.trust-indicators .font-bold {
    font-size: 0.8rem !important;
}

/* Make product card titles smaller */
.woocommerce ul.products li.product h2,
.woocommerce ul.products li.product .woocommerce-loop-product__title,
.woocommerce-page ul.products li.product h2,
.woocommerce-page ul.products li.product .woocommerce-loop-product__title {
    font-size: 1rem !important;
    line-height: 1.3 !important;
    margin-bottom: 0.75rem !important;
}

/* Make product prices smaller */
.woocommerce ul.products li.product .price {
    font-size: 1rem !important;
    margin-bottom: 1rem !important;
}

/* Make View Details buttons smaller */
.view-details-btn {
    padding: 12px 18px !important;
    font-size: 0.875rem !important;
    border-radius: 12px !important;
}

/* FORCE COMPACT PRODUCT CARDS - OVERRIDE ALL EXISTING STYLES */
.woocommerce ul.products li.product,
.woocommerce-page ul.products li.product {
    min-height: 320px !important;
    height: auto !important;
    max-height: 380px !important;
    padding: 0 !important;
}

/* Force compact product card images */
.woocommerce ul.products li.product img,
.woocommerce-page ul.products li.product img {
    height: 10rem !important;
    max-height: 10rem !important;
    object-fit: cover !important;
}

/* Force compact product titles */
.woocommerce ul.products li.product h2,
.woocommerce ul.products li.product h3,
.woocommerce ul.products li.product .woocommerce-loop-product__title,
.woocommerce-page ul.products li.product h2,
.woocommerce-page ul.products li.product h3,
.woocommerce-page ul.products li.product .woocommerce-loop-product__title {
    font-size: 0.875rem !important;
    line-height: 1.2 !important;
    margin-bottom: 0.5rem !important;
    font-weight: 600 !important;
}

/* Force compact product prices */
.woocommerce ul.products li.product .price,
.woocommerce-page ul.products li.product .price {
    font-size: 0.875rem !important;
    margin-bottom: 0.75rem !important;
    font-weight: 700 !important;
}

/* Force compact View Details buttons */
.woocommerce ul.products li.product a[href*="/product/"],
.woocommerce-page ul.products li.product a[href*="/product/"] {
    padding: 0.5rem 1rem !important;
    font-size: 0.75rem !important;
    min-height: auto !important;
    border-radius: 0.5rem !important;
    font-weight: 500 !important;
}

/* Force compact product card content padding */
.woocommerce ul.products li.product .p-6,
.woocommerce ul.products li.product .p-4,
.woocommerce-page ul.products li.product .p-6,
.woocommerce-page ul.products li.product .p-4 {
    padding: 1rem !important;
}

/* Make Add to Cart buttons smaller */
.add_to_cart_button {
    min-height: 44px !important;
    height: 44px !important;
    font-size: 0.875rem !important;
    padding: 0.75rem 1rem !important;
    font-weight: 600 !important;
}

/* Make product card buttons smaller */
.woocommerce ul.products li.product .space-y-2 > a,
.woocommerce ul.products li.product .space-y-2 > button,
.woocommerce-page ul.products li.product .space-y-2 > a,
.woocommerce-page ul.products li.product .space-y-2 > button {
    min-height: 40px !important;
    font-size: 0.875rem !important;
    padding: 0.75rem 1rem !important;
}

/* Make general buttons smaller */
.woocommerce ul.products li.product .add_to_cart_button,
.woocommerce ul.products li.product a[href*="add-to-cart"],
.woocommerce ul.products li.product a[href*="/product/"] {
    min-height: 44px !important;
    font-size: 0.875rem !important;
    padding: 0.75rem 1rem !important;
    border-radius: 0.5rem !important;
}

/* Make quick action buttons smaller */
.quick-view-btn,
.wishlist-btn {
    min-height: 36px !important;
    font-size: 0.8rem !important;
    padding: 0.5rem 0.75rem !important;
}

/* Reduce hero section text sizes */
.hero-title {
    font-size: 2.5rem !important;
}

.hero-description {
    font-size: 1rem !important;
}

/* Make section headings smaller */
h2.text-3xl,
h2.text-4xl {
    font-size: 1.875rem !important;
}

h3.text-xl {
    font-size: 1.125rem !important;
}

/* Make trust badges smaller */
.trust-badge {
    padding: 6px 12px !important;
    font-size: 0.75rem !important;
}

/* Make payment method cards smaller */
.payment-method {
    width: 32px !important;
    height: 20px !important;
    font-size: 0.625rem !important;
}

/* Reduce product card padding */
.woocommerce ul.products li.product {
    padding: 1.25rem !important;
    min-height: 420px !important;
    height: 420px !important;
}

/* Make product images smaller */
.woocommerce ul.products li.product img {
    height: 12rem !important;
    margin-bottom: 1rem !important;
}

/* Make dropshipping trust badges smaller */
.flex.items-center.gap-2.px-4.py-2 {
    padding: 0.5rem 0.75rem !important;
    font-size: 0.75rem !important;
}

.flex.items-center.gap-2.px-4.py-2 span {
    font-size: 0.75rem !important;
}

/* Make trust indicators in hero section smaller */
.flex.items-center.bg-gradient-to-r span.font-bold {
    font-size: 0.75rem !important;
}

.flex.items-center.bg-gradient-to-r {
    padding: 0.5rem 0.75rem !important;
}

/* Make payment method cards in hero smaller */
.w-16.h-10 {
    width: 3rem !important;
    height: 2rem !important;
}

.w-16.h-10 span {
    font-size: 0.625rem !important;
}

/* Make section titles smaller */
.text-4xl.md\\:text-5xl {
    font-size: 2rem !important;
}

.text-3xl.md\\:text-4xl {
    font-size: 1.75rem !important;
}

/* Make feature section icons smaller */
.w-16.h-16 {
    width: 3rem !important;
    height: 3rem !important;
}

.w-16.h-16 svg {
    width: 1.5rem !important;
    height: 1.5rem !important;
}

/* TARGET THE SPECIFIC BLUE TRUST INDICATOR BOXES */
/* These are the "1000+ Premium Products", "24/7 Expert Support", etc. boxes */
.grid.grid-cols-2.md\\:grid-cols-4.gap-6 .bg-white\\/10 {
    padding: 1rem !important;
}

.grid.grid-cols-2.md\\:grid-cols-4.gap-6 .text-4xl {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
}

.grid.grid-cols-2.md\\:grid-cols-4.gap-6 .text-blue-200 {
    font-size: 0.75rem !important;
}

/* Alternative targeting for the trust indicator boxes */
.bg-white\\/10.backdrop-blur-sm.rounded-2xl {
    padding: 1rem !important;
}

.bg-white\\/10.backdrop-blur-sm.rounded-2xl .text-4xl {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
}

.bg-white\\/10.backdrop-blur-sm.rounded-2xl .text-blue-200 {
    font-size: 0.75rem !important;
}

/* More specific targeting for the trust boxes */
div[class*="bg-white/10"][class*="backdrop-blur-sm"][class*="rounded-2xl"] {
    padding: 1rem !important;
}

div[class*="bg-white/10"][class*="backdrop-blur-sm"][class*="rounded-2xl"] div[class*="text-4xl"] {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
}

div[class*="bg-white/10"][class*="backdrop-blur-sm"][class*="rounded-2xl"] div[class*="text-blue-200"] {
    font-size: 0.75rem !important;
}

/* DIRECT CSS TARGETING FOR TRUST INDICATOR BOXES */
/* Target by content and structure */
.grid .text-center:has(.text-4xl) {
    padding: 1rem !important;
}

.grid .text-center .text-4xl.font-black {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
}

.grid .text-center .text-blue-200.font-medium {
    font-size: 0.75rem !important;
}

/* Target the specific grid container */
.grid.grid-cols-2 > div,
.grid.md\\:grid-cols-4 > div {
    padding: 1rem !important;
}

.grid.grid-cols-2 > div .text-4xl,
.grid.md\\:grid-cols-4 > div .text-4xl {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
}

.grid.grid-cols-2 > div .text-blue-200,
.grid.md\\:grid-cols-4 > div .text-blue-200 {
    font-size: 0.75rem !important;
}

/* Universal targeting for any large text in trust boxes */
.backdrop-blur-sm .text-4xl {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
}

.backdrop-blur-sm .text-blue-200 {
    font-size: 0.75rem !important;
}

/* Target by text content if possible */
div:contains("1000+"),
div:contains("24/7"),
div:contains("Free"),
div:contains("2Y") {
    font-size: 1.5rem !important;
}

/* More aggressive targeting */
[class*="text-4xl"][class*="font-black"] {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
}

[class*="text-blue-200"][class*="font-medium"] {
    font-size: 0.75rem !important;
}

/* MOST AGGRESSIVE TARGETING FOR TRUST INDICATOR BOXES */
/* Target all large text elements in the shop page hero section */
section div div:nth-child(1) {
    font-size: 1.5rem !important;
}

section div div:nth-child(2) {
    font-size: 0.75rem !important;
}

/* Target by position in grid */
.grid > div:nth-child(1) div:first-child,
.grid > div:nth-child(2) div:first-child,
.grid > div:nth-child(3) div:first-child,
.grid > div:nth-child(4) div:first-child {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
}

.grid > div:nth-child(1) div:last-child,
.grid > div:nth-child(2) div:last-child,
.grid > div:nth-child(3) div:last-child,
.grid > div:nth-child(4) div:last-child {
    font-size: 0.75rem !important;
}

/* Target by text content using CSS attribute selectors */
div[class*="text-4xl"]:contains("1000+"),
div[class*="text-4xl"]:contains("24/7"),
div[class*="text-4xl"]:contains("Free"),
div[class*="text-4xl"]:contains("2Y") {
    font-size: 1.5rem !important;
}

/* Force override all text-4xl classes */
.text-4xl {
    font-size: 1.5rem !important;
}

/* Force override all font-black classes in grid context */
.grid .font-black {
    font-size: 1.5rem !important;
}

/* Force override all text-blue-200 classes */
.text-blue-200 {
    font-size: 0.75rem !important;
}

/* Target the specific container structure */
.max-w-5xl .grid > div {
    padding: 1rem !important;
}

.max-w-5xl .grid > div > div:first-child {
    font-size: 1.5rem !important;
    margin-bottom: 0.5rem !important;
}

.max-w-5xl .grid > div > div:last-child {
    font-size: 0.75rem !important;
}

/* Footer Styles */
.site-footer {
    background: #1f2937;
    color: white;
    padding: 2rem 0;
    text-align: center;
}

.footer-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.footer-section {
    margin-bottom: 1rem;
}

.footer-section h3,
.footer-section h4 {
    margin-bottom: 0.5rem;
    color: white;
}

.social-links {
    display: flex;
    gap: 1rem;
    justify-content: center;
    margin-top: 0.5rem;
}

.social-link {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s;
}

.social-link:hover {
    color: white;
}

.footer-menu {
    list-style: none;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.footer-menu a {
    color: #9ca3af;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-menu a:hover {
    color: white;
}

/* WordPress specific */
.wp-admin-bar {
    display: none !important;
}

/* Utility classes for WordPress */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}

.alignleft {
    float: left;
    margin-right: 1rem;
}

.alignright {
    float: right;
    margin-left: 1rem;
}

.aligncenter {
    display: block;
    margin: 0 auto;
}

.wp-caption {
    max-width: 100%;
}

.wp-caption-text {
    font-size: 0.875rem;
    color: #6b7280;
    text-align: center;
    margin-top: 0.5rem;
}

.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.items-center { align-items: center; }
.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.gap-2 { gap: 0.5rem; }
.gap-4 { gap: 1rem; }
.gap-6 { gap: 1.5rem; }
.gap-8 { gap: 2rem; }

.w-5 { width: 1.25rem; }
.w-8 { width: 2rem; }
.w-14 { width: 3.5rem; }
.w-16 { width: 4rem; }
.w-20 { width: 5rem; }
.w-24 { width: 6rem; }
.w-32 { width: 8rem; }
.w-48 { width: 12rem; }
.w-80 { width: 20rem; }
.w-full { width: 100%; }
.h-5 { height: 1.25rem; }
.h-8 { height: 2rem; }
.h-14 { height: 3.5rem; }
.h-16 { height: 4rem; }
.h-20 { height: 5rem; }
.h-24 { height: 6rem; }
.h-32 { height: 8rem; }
.h-48 { height: 12rem; }

.text-xs { font-size: 0.75rem; line-height: 1rem; }
.text-sm { font-size: 0.875rem; line-height: 1.25rem; }
.text-base { font-size: 1rem; line-height: 1.5rem; }
.text-lg { font-size: 1.125rem; line-height: 1.75rem; }
.text-xl { font-size: 1.25rem; line-height: 1.75rem; }
.text-2xl { font-size: 1.5rem; line-height: 2rem; }
.text-3xl { font-size: 1.875rem; line-height: 2.25rem; }
.text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
.text-6xl { font-size: 3.75rem; line-height: 1; }

.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-black { font-weight: 900; }
.uppercase { text-transform: uppercase; }
.tracking-wider { letter-spacing: 0.05em; }
.leading-none { line-height: 1; }
.leading-relaxed { line-height: 1.625; }
.text-center { text-align: center; }
.text-left { text-align: left; }

.text-white { color: #ffffff; }
.text-purple-900 { color: var(--purple-900); }
.text-purple-800 { color: var(--purple-800); }
.text-blue-100 { color: var(--blue-100); }
.text-blue-200 { color: var(--blue-200); }
.text-blue-600 { color: var(--blue-600); }
.text-yellow-400 { color: var(--yellow-400); }
.text-green-400 { color: var(--green-400); }
.text-blue-400 { color: var(--blue-400); }
.text-gray-300 { color: var(--gray-300); }
.text-gray-600 { color: var(--gray-600); }
.text-gray-700 { color: var(--gray-700); }
.text-gray-900 { color: var(--gray-900); }

.bg-yellow-400 { background-color: var(--yellow-400); }
.bg-yellow-300 { background-color: var(--yellow-300); }
.bg-white { background-color: #ffffff; }
.bg-gray-50 { background-color: var(--gray-50); }
.bg-gray-200 { background-color: var(--gray-200); }
.bg-gray-900 { background-color: var(--gray-900); }
.bg-blue-600 { background-color: var(--blue-600); }
.bg-green-500 { background-color: var(--green-500); }
.bg-green-600 { background-color: var(--green-600); }

.bg-gradient-to-r {
    background-image: linear-gradient(to right, var(--tw-gradient-stops));
}
.bg-gradient-to-br {
    background-image: linear-gradient(to bottom right, var(--tw-gradient-stops));
}
.from-blue-600 {
    --tw-gradient-from: var(--blue-600);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(37, 99, 235, 0));
}
.via-purple-600 {
    --tw-gradient-stops: var(--tw-gradient-from), var(--purple-600), var(--tw-gradient-to, rgba(147, 51, 234, 0));
}
.to-blue-800 {
    --tw-gradient-to: var(--blue-800);
}
.to-purple-600 {
    --tw-gradient-to: var(--purple-600);
}
.to-purple-700 {
    --tw-gradient-to: var(--purple-700);
}
.from-yellow-400 {
    --tw-gradient-from: var(--yellow-400);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(251, 191, 36, 0));
}
.to-orange-500 {
    --tw-gradient-to: var(--orange-500);
}
.from-blue-500 {
    --tw-gradient-from: var(--blue-500);
    --tw-gradient-stops: var(--tw-gradient-from), var(--tw-gradient-to, rgba(59, 130, 246, 0));
}

.text-transparent { color: transparent; }
.bg-clip-text {
    -webkit-background-clip: text;
    background-clip: text;
}

.rounded-lg { border-radius: 0.5rem; }
.rounded-xl { border-radius: 0.75rem; }
.rounded-2xl { border-radius: 1rem; }
.rounded-full { border-radius: 9999px; }

.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); }
.shadow-xl { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }

.overflow-hidden { overflow: hidden; }
.opacity-10 { opacity: 0.1; }

.transform { transform: translateX(var(--tw-translate-x, 0)) translateY(var(--tw-translate-y, 0)) rotate(var(--tw-rotate, 0)) skewX(var(--tw-skew-x, 0)) skewY(var(--tw-skew-y, 0)) scaleX(var(--tw-scale-x, 1)) scaleY(var(--tw-scale-y, 1)); }
.rotate-12 { --tw-rotate: 12deg; }
.-rotate-6 { --tw-rotate: -6deg; }
.-skew-y-2 { --tw-skew-y: -2deg; }
.scale-105 { --tw-scale-x: 1.05; --tw-scale-y: 1.05; }
.scale-110 { --tw-scale-x: 1.1; --tw-scale-y: 1.1; }

.transition-all { transition-property: all; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-colors { transition-property: color, background-color, border-color, text-decoration-color, fill, stroke; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.transition-transform { transition-property: transform; transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1); transition-duration: 150ms; }
.duration-300 { transition-duration: 300ms; }

.hover\:bg-yellow-300:hover { background-color: var(--yellow-300); }
.hover\:bg-white:hover { background-color: #ffffff; }
.hover\:bg-green-600:hover { background-color: var(--green-600); }
.hover\:text-purple-900:hover { color: var(--purple-900); }
.hover\:text-blue-600:hover { color: var(--blue-600); }
.hover\:text-red-600:hover { color: var(--red-500); }
.hover\:text-white:hover { color: #ffffff; }
.hover\:scale-105:hover { --tw-scale-x: 1.05; --tw-scale-y: 1.05; }
.hover\:scale-110:hover { --tw-scale-x: 1.1; --tw-scale-y: 1.1; }
.hover\:shadow-xl:hover { box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04); }
.group:hover .group-hover\:translate-x-1 { --tw-translate-x: 0.25rem; }
.group:hover .group-hover\:scale-110 { --tw-scale-x: 1.1; --tw-scale-y: 1.1; }

.grid-cols-1 { grid-template-columns: repeat(1, minmax(0, 1fr)); }
.grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
.grid-cols-3 { grid-template-columns: repeat(3, minmax(0, 1fr)); }
.grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

.border { border-width: 1px; }
.border-2 { border-width: 2px; }
.border-b { border-bottom-width: 1px; }
.border-b-2 { border-bottom-width: 2px; }
.border-t { border-top-width: 1px; }
.border-white { border-color: #ffffff; }
.border-gray-200 { border-color: var(--gray-200); }
.border-gray-300 { border-color: var(--gray-300); }
.border-blue-600 { border-color: var(--blue-600); }

.backdrop-blur-sm { backdrop-filter: blur(4px); }
.bg-opacity-10 { background-color: rgba(255, 255, 255, 0.1); }
.bg-opacity-20 { background-color: rgba(255, 255, 255, 0.2); }

.space-x-2 > * + * { margin-left: 0.5rem; }
.space-x-4 > * + * { margin-left: 1rem; }
.space-x-8 > * + * { margin-left: 2rem; }
.space-y-1 > * + * { margin-top: 0.25rem; }
.space-y-2 > * + * { margin-top: 0.5rem; }
.space-y-3 > * + * { margin-top: 0.75rem; }
.space-y-4 > * + * { margin-top: 1rem; }

.flex-shrink-0 { flex-shrink: 0; }
.max-w-2xl { max-width: 42rem; }
.max-w-3xl { max-width: 48rem; }
.max-w-md { max-width: 28rem; }

.-top-1 { top: -0.25rem; }
.-right-1 { right: -0.25rem; }
.top-4 { top: 1rem; }
.right-4 { right: 1rem; }
.left-3 { left: 0.75rem; }
.top-2\.5 { top: 0.625rem; }

.p-2 { padding: 0.5rem; }
.p-4 { padding: 1rem; }
.p-8 { padding: 2rem; }
.px-3 { padding-left: 0.75rem; padding-right: 0.75rem; }
.pl-10 { padding-left: 2.5rem; }
.pr-4 { padding-right: 1rem; }

.focus\:ring-2:focus { box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }
.focus\:ring-blue-500:focus { box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5); }
.focus\:border-transparent:focus { border-color: transparent; }

/* REMOVED: Duplicate animations (already defined above) */

@keyframes fade-in-up {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
.animate-fade-in-up { animation: fade-in-up 0.8s ease-out forwards; }

/* Custom scrollbar - Laravel match */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(45deg, #3b82f6, #8b5cf6);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(45deg, #2563eb, #7c3aed);
}

/* Responsive */
@media (min-width: 640px) {
    .sm\:px-6 { padding-left: 1.5rem; padding-right: 1.5rem; }
    .sm\:flex-row { flex-direction: row; }
}

@media (min-width: 768px) {
    .md\:w-1\/3 { width: 33.333333%; }
    .md\:w-2\/3 { width: 66.666667%; }
    .md\:flex { display: flex; }
    .md\:hidden { display: none; }
    .md\:block { display: block; }
    .md\:flex-row { flex-direction: row; }
    .md\:text-left { text-align: left; }
    .md\:text-2xl { font-size: 1.5rem; line-height: 2rem; }
    .md\:text-4xl { font-size: 2.25rem; line-height: 2.5rem; }
    .md\:text-6xl { font-size: 3.75rem; line-height: 1; }
    .md\:text-xl { font-size: 1.25rem; line-height: 1.75rem; }
    .md\:py-24 { padding-top: 6rem; padding-bottom: 6rem; }
    .md\:mt-0 { margin-top: 0; }
    .md\:top-8 { top: 2rem; }
    .md\:right-8 { right: 2rem; }
    .md\:w-32 { width: 8rem; }
    .md\:h-32 { height: 8rem; }
    .md\:text-base { font-size: 1rem; line-height: 1.5rem; }
    .md\:text-xs { font-size: 0.75rem; line-height: 1rem; }
    .md\:grid-cols-2 { grid-template-columns: repeat(2, minmax(0, 1fr)); }
    .md\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }
}

@media (min-width: 1024px) {
    .lg\:px-8 { padding-left: 2rem; padding-right: 2rem; }
    .lg\:grid-cols-4 { grid-template-columns: repeat(4, minmax(0, 1fr)); }

    /* FIXED: Properly structured responsive styles */
    .main-menu {
        flex-direction: row;
        gap: 2rem;
    }

    .products-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 2rem;
    }
}
