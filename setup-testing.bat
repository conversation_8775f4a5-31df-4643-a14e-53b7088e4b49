@echo off
echo ========================================
echo   Deal4u Theme Production Testing Setup
echo ========================================
echo.

echo 🔧 Installing Node.js dependencies...
call npm install

echo.
echo 🌐 Installing Playwright browsers...
call npx playwright install

echo.
echo ✅ Setup complete!
echo.
echo 📋 Next steps:
echo 1. Edit production-theme-test.js
echo 2. Update DOMAIN, ADMIN_USERNAME, ADMIN_PASSWORD
echo 3. Run: npm test
echo.
echo 🚀 Ready to test your production theme!
pause
