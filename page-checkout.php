<?php
/**
 * Professional Checkout Page Template
 * Deal4u Theme - WooCommerce Checkout
 */

defined('ABSPATH') || exit;

get_header(); ?>

<div class="min-h-screen bg-gray-50 py-8">
    <div class="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">

        <!-- Breadcrumb -->
        <nav class="flex items-center space-x-2 text-sm text-gray-600 mb-6">
            <a href="<?php echo home_url(); ?>" class="hover:text-blue-600 transition-colors duration-200">Home</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <a href="<?php echo wc_get_cart_url(); ?>" class="hover:text-blue-600 transition-colors duration-200">Cart</a>
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
            </svg>
            <span class="text-gray-900 font-medium">Checkout</span>
        </nav>

        <!-- Professional Progress Steps -->
        <div class="flex justify-center items-center mb-8">
            <div class="flex items-center space-x-3">
                <!-- Step 1: Cart (Completed) -->
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg">
                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
                        </svg>
                    </div>
                    <span class="ml-2 text-sm font-medium text-green-600">Cart</span>
                </div>

                <!-- Connector Line -->
                <div class="w-8 h-0.5 bg-green-500"></div>

                <!-- Step 2: Billing Info (Current) -->
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold shadow-lg">2</div>
                    <span class="ml-2 text-sm font-bold text-blue-600">Billing Info</span>
                </div>

                <!-- Connector Line -->
                <div class="w-8 h-0.5 bg-gray-300"></div>

                <!-- Step 3: Payment Method (Next) -->
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center text-gray-500 font-bold">3</div>
                    <span class="ml-2 text-sm text-gray-500">Payment</span>
                </div>

                <!-- Connector Line -->
                <div class="w-8 h-0.5 bg-gray-300"></div>

                <!-- Step 4: Complete (Final) -->
                <div class="flex items-center">
                    <div class="w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center text-gray-500 font-bold">4</div>
                    <span class="ml-2 text-sm text-gray-500">Complete</span>
                </div>
            </div>
        </div>

        <!-- Security Notice -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 text-center">
            <div class="flex items-center justify-center">
                <svg class="w-5 h-5 text-blue-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                </svg>
                <span class="text-blue-800 font-medium">🔒 SSL Secured Checkout - Your information is protected</span>
            </div>
        </div>

        <?php
        // Check if WooCommerce is active and cart is not empty
        if (class_exists('WooCommerce') && !WC()->cart->is_empty()) {
            // Get the checkout object
            $checkout = WC()->checkout();

            do_action('woocommerce_before_checkout_form', $checkout);

            // If checkout registration is disabled and not logged in, the user cannot checkout.
            if (!$checkout->is_registration_enabled() && $checkout->is_registration_required() && !is_user_logged_in()) {
                echo '<div class="bg-white rounded-xl shadow-lg p-12 text-center">';
                echo '<div class="mb-6">';
                echo '<svg class="w-16 h-16 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>';
                echo '</svg>';
                echo '</div>';
                echo '<h2 class="text-2xl font-bold text-gray-900 mb-4">Login Required</h2>';
                echo '<p class="text-gray-600 mb-8">' . esc_html(apply_filters('woocommerce_checkout_must_be_logged_in_message', __('You must be logged in to checkout.', 'woocommerce'))) . '</p>';
                echo '<a href="' . esc_url(wp_login_url(wc_get_checkout_url())) . '" class="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200">';
                echo '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">';
                echo '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"></path>';
                echo '</svg>';
                echo 'Login to Continue';
                echo '</a>';
                echo '</div>';
                return;
            }
            ?>

            <!-- Professional Checkout Form -->
            <div class="bg-white rounded-xl shadow-lg overflow-hidden">
                <div class="p-6 lg:p-8">
                    <div class="mb-8">
                        <h1 class="text-2xl font-bold text-gray-900 mb-2">Complete Your Order</h1>
                        <p class="text-gray-600">Please fill in your details below to complete your purchase</p>
                    </div>

                    <form name="checkout" method="post" class="checkout woocommerce-checkout" action="<?php echo esc_url(wc_get_checkout_url()); ?>" enctype="multipart/form-data">

                        <!-- Step 2: Billing Information (Initially Visible) -->
                        <div id="billing-step" class="checkout-step">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <!-- Billing & Shipping Details -->
                                <div class="lg:col-span-2 space-y-6">
                                    <?php if ($checkout->get_checkout_fields()) : ?>
                                        <?php do_action('woocommerce_checkout_before_customer_details'); ?>

                                        <div id="customer_details">
                                            <!-- Billing Details -->
                                            <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                                <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                                    <svg class="w-5 h-5 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    </svg>
                                                    Billing Information
                                                </h3>
                                                <?php do_action('woocommerce_checkout_billing'); ?>
                                            </div>

                                            <!-- Shipping Details -->
                                            <?php if (WC()->cart->needs_shipping_address()) : ?>
                                                <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                                    <h3 class="text-lg font-semibent text-gray-900 mb-4 flex items-center">
                                                        <svg class="w-5 h-5 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4"></path>
                                                        </svg>
                                                        Shipping Information
                                                    </h3>
                                                    <?php do_action('woocommerce_checkout_shipping'); ?>
                                                </div>
                                            <?php endif; ?>
                                        </div>

                                        <?php do_action('woocommerce_checkout_after_customer_details'); ?>
                                    <?php endif; ?>

                                    <!-- Additional Information -->
                                    <?php do_action('woocommerce_checkout_before_order_review_heading'); ?>

                                    <?php if (apply_filters('woocommerce_enable_order_notes_field', 'yes' === get_option('woocommerce_enable_checkout_notes_field'))) : ?>
                                        <div class="bg-gray-50 rounded-lg p-6">
                                            <h3 class="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                                                <svg class="w-5 h-5 mr-2 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                                </svg>
                                                Additional Information
                                            </h3>
                                            <?php foreach ($checkout->get_checkout_fields('order') as $key => $field) : ?>
                                                <?php woocommerce_form_field($key, $field, $checkout->get_value($key)); ?>
                                            <?php endforeach; ?>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Continue to Payment Button -->
                                    <div class="mt-8 text-center">
                                        <button type="button" id="continue-to-payment" class="bg-blue-600 hover:bg-blue-700 text-white font-bold py-4 px-8 rounded-lg transition duration-200 shadow-lg text-lg">
                                            Continue to Payment →
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 3: Payment & Order Summary (Initially Hidden) -->
                        <div id="payment-step" class="checkout-step" style="display: none;">
                            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                                <!-- Order Summary -->
                                <div class="lg:col-span-2">
                                    <div class="bg-gray-50 rounded-lg p-6 mb-6">
                                        <h3 class="text-lg font-semibold text-gray-900 mb-6 flex items-center">
                                            <svg class="w-5 h-5 mr-2 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                                            </svg>
                                            Order Summary
                                        </h3>

                                        <?php do_action('woocommerce_checkout_before_order_review'); ?>

                                        <div id="order_review" class="woocommerce-checkout-review-order">
                                            <?php do_action('woocommerce_checkout_order_review'); ?>
                                        </div>

                                        <?php do_action('woocommerce_checkout_after_order_review'); ?>
                                    </div>

                                    <!-- Back to Billing Button -->
                                    <div class="mt-6 text-center">
                                        <button type="button" id="back-to-billing" class="text-blue-600 hover:text-blue-800 font-medium text-sm mb-4">
                                            ← Back to Billing Information
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>

                    <?php do_action('woocommerce_after_checkout_form', $checkout); ?>
                </div>
            </div>

        <?php } elseif (class_exists('WooCommerce') && WC()->cart->is_empty()) { ?>
                    <!-- Empty Cart -->
                    <div class="bg-white rounded-xl shadow-lg p-12 text-center">
                        <div class="mb-6">
                            <svg class="w-16 h-16 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15"></path>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Your cart is empty</h2>
                        <p class="text-gray-600 mb-8">Add some products to your cart before proceeding to checkout.</p>
                        <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>"
                           class="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16l-4-4m0 0l4-4m-4 4h18"></path>
                            </svg>
                            Continue Shopping
                        </a>
                    </div>
                <?php } else { ?>
                    <!-- WooCommerce Not Active -->
                    <div class="bg-white rounded-xl shadow-lg p-12 text-center">
                        <div class="mb-6">
                            <svg class="w-16 h-16 mx-auto text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
                            </svg>
                        </div>
                        <h2 class="text-2xl font-bold text-gray-900 mb-4">Checkout Unavailable</h2>
                        <p class="text-gray-600 mb-8">The checkout system is currently unavailable. Please try again later.</p>
                        <a href="<?php echo home_url(); ?>"
                           class="inline-flex items-center gap-2 bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors duration-200">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                            </svg>
                            Go Home
                        </a>
                    </div>
                <?php } ?>
            </div>
        </div>

        <!-- Trust Badges -->
        <div class="mt-8 text-center">
            <p class="text-gray-600 mb-4 font-medium">We accept all major payment methods</p>
            <div class="flex justify-center items-center space-x-8 opacity-80">
                <div class="flex items-center space-x-2">
                    <svg class="w-8 h-8 text-blue-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M4.5 3h15c.83 0 1.5.67 1.5 1.5v15c0 .83-.67 1.5-1.5 1.5h-15c-.83 0-1.5-.67-1.5-1.5v-15c0-.83.67-1.5 1.5-1.5z"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-700">Visa</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="w-8 h-8 text-red-600" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M12 2c5.52 0 10 4.48 10 10s-4.48 10-10 10S2 17.52 2 12 6.48 2 12 2z"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-700">Mastercard</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="w-8 h-8 text-blue-500" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M7.076 21.337H2.47a.641.641 0 0 1-.633-.74L4.944.901C5.026.382 5.474 0 5.998 0h8.46c2.57 0 4.578.543 5.69 1.81 1.01 1.15 1.304 2.42 1.012 4.287-.023.143-.047.288-.077.437-.983 5.05-4.349 6.797-8.647 6.797h-2.19c-.524 0-.968.382-1.05.9l-.635 4.005c-.082.52-.53.901-1.055.901z"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-700">PayPal</span>
                </div>
                <div class="flex items-center space-x-2">
                    <svg class="w-8 h-8 text-gray-800" fill="currentColor" viewBox="0 0 24 24">
                        <path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 ********** 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83z"/>
                    </svg>
                    <span class="text-sm font-medium text-gray-700">Apple Pay</span>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
// Professional Multi-Step Checkout Management
document.addEventListener('DOMContentLoaded', function() {
    const billingStep = document.getElementById('billing-step');
    const paymentStep = document.getElementById('payment-step');
    const continueBtn = document.getElementById('continue-to-payment');
    const backBtn = document.getElementById('back-to-billing');

    // Continue to Payment Button
    if (continueBtn) {
        continueBtn.addEventListener('click', function() {
            // Validate billing fields first
            if (validateBillingFields()) {
                // Hide billing step
                billingStep.style.display = 'none';
                // Show payment step
                paymentStep.style.display = 'block';
                // Update progress
                updateProgressToPayment();
                // Scroll to top
                window.scrollTo({ top: 0, behavior: 'smooth' });
            } else {
                // Show validation message
                showValidationMessage();
            }
        });
    }

    // Back to Billing Button
    if (backBtn) {
        backBtn.addEventListener('click', function() {
            // Hide payment step
            paymentStep.style.display = 'none';
            // Show billing step
            billingStep.style.display = 'block';
            // Update progress back to billing
            updateProgressToBilling();
            // Scroll to top
            window.scrollTo({ top: 0, behavior: 'smooth' });
        });
    }

    // Function to validate billing fields
    function validateBillingFields() {
        const requiredFields = document.querySelectorAll('#customer_details input[required], #customer_details select[required]');
        let isValid = true;

        requiredFields.forEach(field => {
            if (!field.value.trim()) {
                field.style.borderColor = '#ef4444';
                field.style.boxShadow = '0 0 0 1px #ef4444';
                isValid = false;
            } else {
                field.style.borderColor = '#d1d5db';
                field.style.boxShadow = 'none';
            }
        });

        return isValid;
    }

    // Function to show validation message
    function showValidationMessage() {
        // Remove existing message
        const existingMessage = document.querySelector('.validation-message');
        if (existingMessage) {
            existingMessage.remove();
        }

        // Create validation message
        const message = document.createElement('div');
        message.className = 'validation-message bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-4';
        message.innerHTML = '⚠️ Please fill in all required fields before continuing.';

        // Insert before continue button
        continueBtn.parentNode.insertBefore(message, continueBtn);

        // Remove message after 5 seconds
        setTimeout(() => {
            if (message.parentNode) {
                message.remove();
            }
        }, 5000);
    }

    // Function to update progress to payment step
    function updateProgressToPayment() {
        // Update step 2 (Billing Info) to completed
        const step2Circle = document.querySelector('.flex.items-center:nth-child(3) .w-10');
        const step2Text = document.querySelector('.flex.items-center:nth-child(3) span');
        const connector2 = document.querySelector('.flex.items-center .w-8:nth-child(4)');

        if (step2Circle && step2Text) {
            step2Circle.className = 'w-10 h-10 bg-green-500 rounded-full flex items-center justify-center text-white font-bold shadow-lg';
            step2Circle.innerHTML = '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path></svg>';
            step2Text.className = 'ml-2 text-sm font-medium text-green-600';
        }

        if (connector2) {
            connector2.className = 'w-8 h-0.5 bg-green-500';
        }

        // Update step 3 (Payment) to current
        const step3Circle = document.querySelector('.flex.items-center:nth-child(5) .w-10');
        const step3Text = document.querySelector('.flex.items-center:nth-child(5) span');

        if (step3Circle && step3Text) {
            step3Circle.className = 'w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold shadow-lg';
            step3Circle.textContent = '3';
            step3Text.className = 'ml-2 text-sm font-bold text-blue-600';
        }
    }

    // Function to update progress back to billing step
    function updateProgressToBilling() {
        // Update step 2 (Billing Info) back to current
        const step2Circle = document.querySelector('.flex.items-center:nth-child(3) .w-10');
        const step2Text = document.querySelector('.flex.items-center:nth-child(3) span');
        const connector2 = document.querySelector('.flex.items-center .w-8:nth-child(4)');

        if (step2Circle && step2Text) {
            step2Circle.className = 'w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center text-white font-bold shadow-lg';
            step2Circle.textContent = '2';
            step2Text.className = 'ml-2 text-sm font-bold text-blue-600';
        }

        if (connector2) {
            connector2.className = 'w-8 h-0.5 bg-gray-300';
        }

        // Update step 3 (Payment) back to inactive
        const step3Circle = document.querySelector('.flex.items-center:nth-child(5) .w-10');
        const step3Text = document.querySelector('.flex.items-center:nth-child(5) span');

        if (step3Circle && step3Text) {
            step3Circle.className = 'w-10 h-10 bg-gray-300 rounded-full flex items-center justify-center text-gray-500 font-bold';
            step3Circle.textContent = '3';
            step3Text.className = 'ml-2 text-sm text-gray-500';
        }
    }

    // Enhanced Payment Method Selection
    const paymentMethods = document.querySelectorAll('input[name="payment_method"]');
    paymentMethods.forEach(method => {
        method.addEventListener('change', function() {
            // Remove selected class from all payment methods
            document.querySelectorAll('.payment_methods li').forEach(li => {
                li.classList.remove('payment_method_selected');
            });

            // Add selected class to current method
            this.closest('li').classList.add('payment_method_selected');

            // Add visual feedback
            const label = this.nextElementSibling;
            if (label) {
                // Create success animation
                label.style.transform = 'scale(1.02)';
                setTimeout(() => {
                    label.style.transform = 'scale(1)';
                }, 200);
            }
        });
    });

    // Initialize selected state on page load
    const selectedMethod = document.querySelector('input[name="payment_method"]:checked');
    if (selectedMethod) {
        selectedMethod.closest('li').classList.add('payment_method_selected');
    }
});
</script>

<style>
/* Professional Checkout Styling */
.woocommerce-checkout {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.woocommerce-checkout .form-row {
    margin-bottom: 1.25rem;
}

.woocommerce-checkout .form-row-first,
.woocommerce-checkout .form-row-last {
    width: 48%;
    display: inline-block;
    vertical-align: top;
}

.woocommerce-checkout .form-row-first {
    margin-right: 4%;
}

@media (max-width: 768px) {
    .woocommerce-checkout .form-row-first,
    .woocommerce-checkout .form-row-last {
        width: 100%;
        display: block;
        margin-right: 0;
        margin-bottom: 1rem;
    }
}

.woocommerce-checkout input[type="text"],
.woocommerce-checkout input[type="email"],
.woocommerce-checkout input[type="tel"],
.woocommerce-checkout input[type="password"],
.woocommerce-checkout select,
.woocommerce-checkout textarea {
    width: 100%;
    padding: 0.875rem 1rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    background-color: #ffffff;
    box-sizing: border-box;
    transition: all 0.2s ease;
}

.woocommerce-checkout input:focus,
.woocommerce-checkout select:focus,
.woocommerce-checkout textarea:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    background-color: #fefefe;
}

.woocommerce-checkout label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.woocommerce-checkout .required {
    color: #ef4444;
}

/* Professional Place Order Button */
.woocommerce-checkout #place_order {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    color: white;
    font-weight: 600;
    padding: 0.875rem 1.5rem;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    font-size: 0.875rem;
    width: 100%;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(5, 150, 105, 0.2);
    text-transform: none;
    letter-spacing: normal;
    margin-top: 1rem;
    max-width: 200px;
    margin-left: auto;
    margin-right: auto;
    display: block;
}

.woocommerce-checkout #place_order:hover {
    background: linear-gradient(135deg, #047857 0%, #065f46 100%);
    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
    transform: translateY(-1px);
}

.woocommerce-checkout #place_order:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(5, 150, 105, 0.2);
}

/* Professional Order Review Table */
.woocommerce-checkout .woocommerce-checkout-review-order-table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.woocommerce-checkout .woocommerce-checkout-review-order-table th,
.woocommerce-checkout .woocommerce-checkout-review-order-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #f3f4f6;
    text-align: left;
    font-size: 0.875rem;
    vertical-align: middle;
}

.woocommerce-checkout .woocommerce-checkout-review-order-table th {
    font-weight: 600;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.75rem;
    background: #f9fafb;
}

.woocommerce-checkout .woocommerce-checkout-review-order-table td {
    color: #374151;
}

.woocommerce-checkout .woocommerce-checkout-review-order-table .product-name {
    font-weight: 500;
    color: #111827;
}

.woocommerce-checkout .woocommerce-checkout-review-order-table .product-total,
.woocommerce-checkout .woocommerce-checkout-review-order-table .amount {
    font-weight: 600;
    color: #111827;
    text-align: right;
}

.woocommerce-checkout .woocommerce-checkout-review-order-table .order-total {
    background: #f9fafb;
    border-top: 2px solid #e5e7eb;
}

.woocommerce-checkout .woocommerce-checkout-review-order-table .order-total th,
.woocommerce-checkout .woocommerce-checkout-review-order-table .order-total td {
    padding: 1rem;
    font-weight: 700;
    font-size: 1rem;
    color: #111827;
}

.woocommerce-checkout .woocommerce-checkout-review-order-table .order-total .amount {
    font-size: 1.25rem;
    color: #059669;
}

/* Professional Payment Methods Section */
.woocommerce-checkout .woocommerce-checkout-payment {
    background: white;
    border-radius: 0.75rem;
    padding: 0;
    margin-top: 1.5rem;
    border: 1px solid #e5e7eb;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* Professional Payment Methods Header */
.woocommerce-checkout .woocommerce-checkout-payment::before {
    content: "💳 Step 3: Choose Payment Method";
    display: block;
    background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
    padding: 1.25rem 1.5rem;
    font-weight: 700;
    color: white;
    border-bottom: none;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(59, 130, 246, 0.1);
}

.woocommerce-checkout .payment_methods {
    list-style: none;
    padding: 1.5rem;
    margin: 0;
    background: white;
    display: grid;
    gap: 1rem;
}

/* Professional Payment Method Cards */
.woocommerce-checkout .payment_methods li {
    margin: 0;
    padding: 0;
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.woocommerce-checkout .payment_methods li:hover {
    border-color: #3b82f6;
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
    transform: translateY(-1px);
}

.woocommerce-checkout .payment_methods li input[type="radio"]:checked + label {
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
    border-color: #3b82f6;
}

.woocommerce-checkout .payment_methods li:has(input[type="radio"]:checked) {
    border-color: #3b82f6;
    box-shadow: 0 0 0 1px #3b82f6, 0 4px 12px rgba(59, 130, 246, 0.25);
}

.woocommerce-checkout .payment_methods label {
    font-weight: 600;
    color: #374151;
    cursor: pointer;
    display: flex;
    align-items: center;
    font-size: 0.9375rem;
    padding: 1.5rem;
    margin: 0;
    width: 100%;
    box-sizing: border-box;
    position: relative;
    background: white;
    border-radius: 0.75rem;
    transition: all 0.3s ease;
}

.woocommerce-checkout .payment_methods input[type="radio"] {
    position: absolute;
    opacity: 0;
    width: 0;
    height: 0;
}

/* Custom Radio Button */
.woocommerce-checkout .payment_methods label::before {
    content: "";
    width: 20px;
    height: 20px;
    border: 2px solid #d1d5db;
    border-radius: 50%;
    margin-right: 1rem;
    flex-shrink: 0;
    transition: all 0.3s ease;
    background: white;
    position: relative;
}

.woocommerce-checkout .payment_methods input[type="radio"]:checked + label::before {
    border-color: #3b82f6;
    background: #3b82f6;
    box-shadow: inset 0 0 0 3px white;
}

/* Payment Method Icons and Content */
.woocommerce-checkout .payment_methods li[id*="bacs"] label::after {
    content: "🏦";
    position: absolute;
    right: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5rem;
    opacity: 0.7;
}

.woocommerce-checkout .payment_methods li[id*="cod"] label::after {
    content: "💵";
    position: absolute;
    right: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5rem;
    opacity: 0.7;
}

.woocommerce-checkout .payment_methods li[id*="paypal"] label::after {
    content: "💳";
    position: absolute;
    right: 1.5rem;
    top: 50%;
    transform: translateY(-50%);
    font-size: 1.5rem;
    opacity: 0.7;
}

/* Payment Method Titles */
.woocommerce-checkout .payment_methods li[id*="bacs"] label {
    position: relative;
}

.woocommerce-checkout .payment_methods li[id*="bacs"] label span {
    display: block;
    font-size: 1rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.woocommerce-checkout .payment_methods li[id*="cod"] label span {
    display: block;
    font-size: 1rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

/* Add method names */
.woocommerce-checkout .payment_methods li[id*="bacs"] label {
    flex-direction: column;
    align-items: flex-start;
}

.woocommerce-checkout .payment_methods li[id*="cod"] label {
    flex-direction: column;
    align-items: flex-start;
}

/* Professional Payment Method Descriptions */
.woocommerce-checkout .payment_methods .payment_box {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: none;
    border-top: 1px solid #e2e8f0;
    padding: 1.25rem 1.5rem;
    margin: 0;
    font-size: 0.8125rem;
    color: #64748b;
    line-height: 1.6;
    border-radius: 0 0 0.75rem 0.75rem;
}

.woocommerce-checkout .payment_methods .payment_box p {
    margin: 0;
    padding: 0;
    font-weight: 500;
}

/* Selected Payment Method Styling */
.woocommerce-checkout .payment_methods li input[type="radio"]:checked + label {
    color: #1e40af;
    font-weight: 700;
}

/* Privacy Policy Section */
.woocommerce-checkout .woocommerce-checkout-payment .form-row {
    padding: 1.5rem;
    background: #f8fafc;
    border-top: 1px solid #e2e8f0;
    margin: 0;
}

.woocommerce-checkout .woocommerce-privacy-policy-text {
    font-size: 0.75rem;
    color: #64748b;
    margin: 0.75rem 0 0 0;
    padding: 0.75rem;
    background: white;
    border-radius: 0.375rem;
    border: 1px solid #e2e8f0;
    line-height: 1.4;
}

/* Terms and Conditions */
.woocommerce-checkout .woocommerce-terms-and-conditions-wrapper {
    margin-top: 1rem;
}

.woocommerce-checkout .woocommerce-terms-and-conditions-checkbox-text {
    font-size: 0.8125rem;
    color: #374151;
    line-height: 1.4;
}

.woocommerce-checkout .woocommerce-terms-and-conditions-checkbox-text input[type="checkbox"] {
    margin-right: 0.5rem;
    accent-color: #3b82f6;
}

/* Order Review Sidebar */
.woocommerce-checkout #order_review {
    background: #f8fafc;
    padding: 2rem;
    border-radius: 1rem;
    border: 1px solid #e2e8f0;
    position: sticky;
    top: 2rem;
}

/* Order Table */
.woocommerce-checkout .shop_table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1.5rem;
    background: white;
    border-radius: 0.5rem;
    overflow: hidden;
}

.woocommerce-checkout .shop_table th,
.woocommerce-checkout .shop_table td {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    text-align: left;
}

.woocommerce-checkout .shop_table th {
    background: #f3f4f6;
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.woocommerce-checkout .order-total {
    font-size: 1.25rem;
    font-weight: 700;
    color: #111827;
    background: #f0f9ff !important;
}

/* Payment Methods - ENHANCED FOR ALL GATEWAYS */
.woocommerce-checkout .payment_methods {
    background: white;
    padding: 1.5rem;
    border-radius: 0.75rem;
    border: 2px solid #e5e7eb;
    margin-bottom: 1.5rem;
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

.woocommerce-checkout .payment_method {
    display: block !important;
    margin-bottom: 1rem;
    padding: 1rem;
    border: 1px solid #e5e7eb;
    border-radius: 0.5rem;
    background: #ffffff;
    transition: border-color 0.2s ease;
}

.woocommerce-checkout .payment_method:hover {
    border-color: #3b82f6;
}

.woocommerce-checkout .payment_method input[type="radio"] {
    margin-right: 0.75rem;
    transform: scale(1.2);
}

.woocommerce-checkout .payment_method label {
    display: inline-flex;
    align-items: center;
    font-weight: 600;
    cursor: pointer;
    font-size: 1rem;
    color: #374151;
}

.woocommerce-checkout .payment_box {
    background: #f8fafc;
    padding: 1rem;
    margin-top: 0.75rem;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    display: block !important;
}

/* All payment method types */
.woocommerce-checkout .payment_method_paypal,
.woocommerce-checkout .payment_method_stripe,
.woocommerce-checkout .payment_method_bacs,
.woocommerce-checkout .payment_method_cheque,
.woocommerce-checkout .payment_method_cod,
.woocommerce-checkout .payment_method_square,
.woocommerce-checkout .payment_method_authorize_net,
.woocommerce-checkout .payment_method_braintree,
.woocommerce-checkout .payment_method_amazon_payments,
.woocommerce-checkout .payment_method_klarna,
.woocommerce-checkout .payment_method_afterpay {
    display: block !important;
    visibility: visible !important;
}

/* Payment method icons */
.woocommerce-checkout .payment_methods img {
    max-height: 28px;
    width: auto;
    vertical-align: middle;
    margin-left: 0.5rem;
}

/* Stripe specific styling */
.woocommerce-checkout .wc-stripe-elements-field,
.woocommerce-checkout .wc-stripe-card-element {
    padding: 0.875rem;
    border: 1px solid #d1d5db;
    border-radius: 0.5rem;
    background: white;
    margin-top: 0.5rem;
}

/* PayPal button styling */
.woocommerce-checkout .paypal-button,
.woocommerce-checkout #paypal-button-container {
    margin-top: 1rem;
}

/* Apple Pay / Google Pay buttons */
.woocommerce-checkout .apple-pay-button,
.woocommerce-checkout .google-pay-button {
    margin-top: 1rem;
    border-radius: 0.5rem;
    min-height: 44px;
}

/* Place Order Button */
.woocommerce-checkout #place_order {
    background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
    color: white;
    font-weight: 700;
    padding: 1.25rem 2rem;
    border-radius: 0.75rem;
    border: none;
    cursor: pointer;
    font-size: 1.125rem;
    width: 100%;
    transition: all 0.3s ease;
    box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.3);
}

.woocommerce-checkout #place_order:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(37, 99, 235, 0.4);
}

/* Privacy Policy */
.woocommerce-checkout .woocommerce-privacy-policy-text {
    font-size: 0.875rem;
    color: #6b7280;
    margin-top: 1rem;
    padding: 1rem;
    background: #f9fafb;
    border-radius: 0.5rem;
    border-left: 4px solid #3b82f6;
}

/* Error Messages */
.woocommerce-checkout .woocommerce-error,
.woocommerce-checkout .woocommerce-message {
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.woocommerce-checkout .woocommerce-error {
    background: #fef2f2;
    border: 1px solid #fecaca;
    color: #dc2626;
}

.woocommerce-checkout .woocommerce-message {
    background: #f0fdf4;
    border: 1px solid #bbf7d0;
    color: #16a34a;
}

/* Loading State */
.woocommerce-checkout.processing {
    opacity: 0.6;
    pointer-events: none;
}

/* Mobile Responsiveness - ENHANCED */
@media (max-width: 768px) {
    .woocommerce-checkout .form-row-first,
    .woocommerce-checkout .form-row-last {
        width: 100% !important;
        display: block !important;
        margin-right: 0 !important;
        margin-bottom: 1rem;
    }

    .woocommerce-checkout #order_review {
        position: static;
        margin-top: 2rem;
    }

    .checkout-container {
        padding: 1rem;
    }
}

/* Ensure checkout form is always visible */
.woocommerce-checkout {
    overflow: visible !important;
    min-height: auto !important;
}

.woocommerce-checkout .checkout-main {
    overflow: visible !important;
    width: 100%;
}

/* Fix any potential container issues */
.bg-white.rounded-xl.shadow-lg {
    overflow: visible !important;
}

.p-4.md\\:p-8 {
    overflow: visible !important;
}
</style>

<!-- Add security badge -->
<div class="checkout-security">
    <p>🔒 Your payment information is secure and encrypted</p>
</div>

<?php get_footer(); ?>
