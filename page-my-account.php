<?php
/**
 * Template Name: My Account Page
 */

// SECURITY: Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// WOOCOMMERCE INTEGRATION: Let WooCommerce handle login/logout logic
// Remove redirect to allow WooCommerce to show login form when needed

get_header(); 

$current_user = wp_get_current_user();
?>

<main class="main-content">
    <!-- Account Header -->
    <section style="padding: 3rem 0; background: linear-gradient(to right, #2563eb, #9333ea); color: white;">
        <div class="container">
            <div style="text-align: center;">
                <div style="font-size: 4rem; margin-bottom: 1rem;">👤</div>
                <h1 style="font-size: 3rem; font-weight: bold; margin-bottom: 1rem;">My Account</h1>
                <p style="font-size: 1.25rem; color: #bfdbfe;">Welcome back, <?php echo esc_html($current_user->display_name); ?>!</p>
            </div>
        </div>
    </section>

    <!-- WooCommerce My Account Content -->
    <section style="padding: 4rem 0; background: #f9fafb;">
        <div class="container">
            <div class="woocommerce">
                <?php
                // Display WooCommerce My Account content
                if (function_exists('woocommerce_account_content')) {
                    woocommerce_account_content();
                } else {
                    // Fallback to shortcode
                    echo do_shortcode('[woocommerce_my_account]');
                }
                ?>
            </div>
        </div>
    </section>

    <!-- Custom Account Dashboard (Backup) -->
    <section style="padding: 4rem 0; background: #f9fafb; display: none;">
        <div class="container">
            <div style="display: grid; grid-template-columns: 1fr 3fr; gap: 3rem; max-width: 6xl; margin: 0 auto;">
                
                <!-- Account Navigation -->
                <div style="background: white; padding: 2rem; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); height: fit-content;">
                    <h3 style="font-size: 1.25rem; font-weight: bold; color: #111827; margin-bottom: 1.5rem;">Account Menu</h3>
                    <nav style="display: flex; flex-direction: column; gap: 0.5rem;">
                        <a href="#dashboard" onclick="showSection('dashboard')" class="nav-link active" style="padding: 0.75rem 1rem; border-radius: 0.5rem; text-decoration: none; color: #374151; transition: all 0.3s; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-tachometer-alt"></i>Dashboard
                        </a>
                        <a href="#orders" onclick="showSection('orders')" class="nav-link" style="padding: 0.75rem 1rem; border-radius: 0.5rem; text-decoration: none; color: #374151; transition: all 0.3s; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-shopping-bag"></i>Orders
                        </a>
                        <a href="#profile" onclick="showSection('profile')" class="nav-link" style="padding: 0.75rem 1rem; border-radius: 0.5rem; text-decoration: none; color: #374151; transition: all 0.3s; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-user-edit"></i>Edit Profile
                        </a>
                        <a href="#addresses" onclick="showSection('addresses')" class="nav-link" style="padding: 0.75rem 1rem; border-radius: 0.5rem; text-decoration: none; color: #374151; transition: all 0.3s; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-map-marker-alt"></i>Addresses
                        </a>
                        <a href="#password" onclick="showSection('password')" class="nav-link" style="padding: 0.75rem 1rem; border-radius: 0.5rem; text-decoration: none; color: #374151; transition: all 0.3s; display: flex; align-items: center; gap: 0.5rem;">
                            <i class="fas fa-lock"></i>Change Password
                        </a>
                        <a href="<?php echo wp_logout_url(home_url()); ?>" style="padding: 0.75rem 1rem; border-radius: 0.5rem; text-decoration: none; color: #ef4444; transition: all 0.3s; display: flex; align-items: center; gap: 0.5rem; margin-top: 1rem; border-top: 1px solid #e5e7eb; padding-top: 1rem;">
                            <i class="fas fa-sign-out-alt"></i>Logout
                        </a>
                    </nav>
                </div>

                <!-- Account Content -->
                <div style="background: white; padding: 2rem; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                    
                    <!-- Dashboard Section -->
                    <div id="dashboard" class="account-section active">
                        <h2 style="font-size: 2rem; font-weight: bold; color: #111827; margin-bottom: 2rem;">Dashboard</h2>
                        
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1.5rem; margin-bottom: 3rem;">
                            <div style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 2rem; border-radius: 1rem; text-align: center;">
                                <div style="font-size: 2rem; margin-bottom: 0.5rem;">📦</div>
                                <h3 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.5rem;">
                                    <?php 
                                    $orders = function_exists('wc_get_orders') ? wc_get_orders(array('customer' => $current_user->ID, 'limit' => -1)) : array();
                                    echo count($orders);
                                    ?>
                                </h3>
                                <p>Total Orders</p>
                            </div>
                            
                            <div style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white; padding: 2rem; border-radius: 1rem; text-align: center;">
                                <div style="font-size: 2rem; margin-bottom: 0.5rem;">💰</div>
                                <h3 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.5rem;">
                                    <?php 
                                    $total_spent = function_exists('wc_get_customer_total_spent') ? wc_get_customer_total_spent($current_user->ID) : 0;
                                    echo wc_price($total_spent);
                                    ?>
                                </h3>
                                <p>Total Spent</p>
                            </div>
                            
                            <div style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white; padding: 2rem; border-radius: 1rem; text-align: center;">
                                <div style="font-size: 2rem; margin-bottom: 0.5rem;">❤️</div>
                                <h3 style="font-size: 1.5rem; font-weight: bold; margin-bottom: 0.5rem;">0</h3>
                                <p>Wishlist Items</p>
                            </div>
                        </div>
                        
                        <div style="background: #f9fafb; padding: 2rem; border-radius: 0.5rem;">
                            <h3 style="font-size: 1.25rem; font-weight: bold; color: #111827; margin-bottom: 1rem;">Account Information</h3>
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem;">
                                <div>
                                    <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 0.25rem;">Name</p>
                                    <p style="font-weight: 500; color: #111827;"><?php echo esc_html($current_user->display_name); ?></p>
                                </div>
                                <div>
                                    <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 0.25rem;">Email</p>
                                    <p style="font-weight: 500; color: #111827;"><?php echo esc_html($current_user->user_email); ?></p>
                                </div>
                                <div>
                                    <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 0.25rem;">Member Since</p>
                                    <p style="font-weight: 500; color: #111827;"><?php echo date('F j, Y', strtotime($current_user->user_registered)); ?></p>
                                </div>
                                <div>
                                    <p style="color: #6b7280; font-size: 0.875rem; margin-bottom: 0.25rem;">Account Status</p>
                                    <p style="font-weight: 500; color: #10b981;">Active</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Orders Section -->
                    <div id="orders" class="account-section" style="display: none;">
                        <h2 style="font-size: 2rem; font-weight: bold; color: #111827; margin-bottom: 2rem;">My Orders</h2>
                        
                        <?php if (function_exists('wc_get_orders')): ?>
                            <?php 
                            $orders = wc_get_orders(array(
                                'customer' => $current_user->ID,
                                'limit' => 10,
                                'orderby' => 'date',
                                'order' => 'DESC'
                            ));
                            
                            if ($orders): ?>
                                <div style="overflow-x: auto;">
                                    <table style="width: 100%; border-collapse: collapse;">
                                        <thead>
                                            <tr style="background: #f9fafb;">
                                                <th style="padding: 1rem; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Order</th>
                                                <th style="padding: 1rem; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Date</th>
                                                <th style="padding: 1rem; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Status</th>
                                                <th style="padding: 1rem; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Total</th>
                                                <th style="padding: 1rem; text-align: left; font-weight: 600; color: #374151; border-bottom: 1px solid #e5e7eb;">Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <?php foreach ($orders as $order): ?>
                                                <tr>
                                                    <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">#<?php echo $order->get_order_number(); ?></td>
                                                    <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;"><?php echo $order->get_date_created()->format('M j, Y'); ?></td>
                                                    <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">
                                                        <span style="background: #10b981; color: white; padding: 0.25rem 0.75rem; border-radius: 9999px; font-size: 0.875rem;">
                                                            <?php echo ucfirst($order->get_status()); ?>
                                                        </span>
                                                    </td>
                                                    <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb; font-weight: 600;"><?php echo $order->get_formatted_order_total(); ?></td>
                                                    <td style="padding: 1rem; border-bottom: 1px solid #e5e7eb;">
                                                        <a href="<?php echo $order->get_view_order_url(); ?>" style="color: #2563eb; text-decoration: none; font-weight: 500;">View</a>
                                                    </td>
                                                </tr>
                                            <?php endforeach; ?>
                                        </tbody>
                                    </table>
                                </div>
                            <?php else: ?>
                                <div style="text-align: center; padding: 3rem; color: #6b7280;">
                                    <i class="fas fa-shopping-bag" style="font-size: 4rem; margin-bottom: 1rem; color: #d1d5db;"></i>
                                    <h3>No orders yet</h3>
                                    <p>You haven't placed any orders yet.</p>
                                    <a href="<?php echo home_url('/shop/'); ?>" style="background: linear-gradient(to right, #2563eb, #9333ea); color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; text-decoration: none; display: inline-block; margin-top: 1rem; font-weight: bold;">
                                        Start Shopping
                                    </a>
                                </div>
                            <?php endif; ?>
                        <?php else: ?>
                            <p>WooCommerce is required to view orders.</p>
                        <?php endif; ?>
                    </div>

                    <!-- Profile Section -->
                    <div id="profile" class="account-section" style="display: none;">
                        <h2 style="font-size: 2rem; font-weight: bold; color: #111827; margin-bottom: 2rem;">Edit Profile</h2>
                        
                        <form method="post" style="display: flex; flex-direction: column; gap: 1.5rem;">
                            <div style="display: grid; grid-template-columns: repeat(2, 1fr); gap: 1rem;">
                                <div>
                                    <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">First Name</label>
                                    <input type="text" name="first_name" value="<?php echo esc_attr(get_user_meta($current_user->ID, 'first_name', true)); ?>" style="width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                                </div>
                                <div>
                                    <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Last Name</label>
                                    <input type="text" name="last_name" value="<?php echo esc_attr(get_user_meta($current_user->ID, 'last_name', true)); ?>" style="width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                                </div>
                            </div>
                            
                            <div>
                                <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Display Name</label>
                                <input type="text" name="display_name" value="<?php echo esc_attr($current_user->display_name); ?>" style="width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                            </div>
                            
                            <div>
                                <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Email Address</label>
                                <input type="email" name="user_email" value="<?php echo esc_attr($current_user->user_email); ?>" style="width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;">
                            </div>
                            
                            <button type="submit" name="update_profile" style="background: linear-gradient(to right, #2563eb, #9333ea); color: white; font-weight: bold; padding: 0.75rem 1.5rem; border-radius: 0.5rem; border: none; cursor: pointer; font-size: 1rem; transition: all 0.3s; width: fit-content;" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                <i class="fas fa-save" style="margin-right: 0.5rem;"></i>Update Profile
                            </button>
                        </form>
                    </div>

                    <!-- Addresses Section -->
                    <div id="addresses" class="account-section" style="display: none;">
                        <h2 style="font-size: 2rem; font-weight: bold; color: #111827; margin-bottom: 2rem;">My Addresses</h2>
                        
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 2rem;">
                            <div style="border: 2px dashed #d1d5db; border-radius: 1rem; padding: 2rem; text-align: center; color: #6b7280;">
                                <i class="fas fa-plus-circle" style="font-size: 3rem; margin-bottom: 1rem; color: #d1d5db;"></i>
                                <h3>Add New Address</h3>
                                <p>Add a billing or shipping address</p>
                                <button style="background: linear-gradient(to right, #2563eb, #9333ea); color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; border: none; cursor: pointer; margin-top: 1rem; font-weight: bold;">
                                    Add Address
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Password Section -->
                    <div id="password" class="account-section" style="display: none;">
                        <h2 style="font-size: 2rem; font-weight: bold; color: #111827; margin-bottom: 2rem;">Change Password</h2>
                        
                        <form method="post" style="display: flex; flex-direction: column; gap: 1.5rem; max-width: 400px;">
                            <div>
                                <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Current Password</label>
                                <input type="password" name="current_password" style="width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;" required>
                            </div>
                            
                            <div>
                                <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">New Password</label>
                                <input type="password" name="new_password" style="width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;" required>
                            </div>
                            
                            <div>
                                <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Confirm New Password</label>
                                <input type="password" name="confirm_password" style="width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;" required>
                            </div>
                            
                            <button type="submit" name="change_password" style="background: linear-gradient(to right, #2563eb, #9333ea); color: white; font-weight: bold; padding: 0.75rem 1.5rem; border-radius: 0.5rem; border: none; cursor: pointer; font-size: 1rem; transition: all 0.3s; width: fit-content;" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                <i class="fas fa-lock" style="margin-right: 0.5rem;"></i>Change Password
                            </button>
                        </form>
                    </div>

                </div>
            </div>
        </div>
    </section>
</main>

<style>
    .nav-link.active {
        background: linear-gradient(to right, #2563eb, #9333ea) !important;
        color: white !important;
    }
    
    .nav-link:hover {
        background: #f3f4f6;
        color: #2563eb;
    }
    
    .nav-link.active:hover {
        background: linear-gradient(to right, #2563eb, #9333ea) !important;
        color: white !important;
    }
    
    @media (max-width: 768px) {
        .container > div {
            grid-template-columns: 1fr !important;
        }
    }
</style>

<script>
function showSection(sectionId) {
    // Hide all sections
    const sections = document.querySelectorAll('.account-section');
    sections.forEach(section => {
        section.style.display = 'none';
    });
    
    // Remove active class from all nav links
    const navLinks = document.querySelectorAll('.nav-link');
    navLinks.forEach(link => {
        link.classList.remove('active');
    });
    
    // Show selected section
    document.getElementById(sectionId).style.display = 'block';
    
    // Add active class to clicked nav link
    event.currentTarget.classList.add('active');
}
</script>

<?php get_footer(); ?>
