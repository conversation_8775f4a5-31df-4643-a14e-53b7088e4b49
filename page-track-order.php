<?php
/**
 * Template Name: Track Order Page
 */

get_header(); ?>

<main class="main-content">
    <!-- Track Order Header -->
    <section style="padding: 3rem 0; background: linear-gradient(to right, #2563eb, #9333ea); color: white;">
        <div class="container">
            <div style="text-align: center;">
                <h1 style="font-size: 3rem; font-weight: bold; margin-bottom: 1rem;">Track Your Order</h1>
                <p style="font-size: 1.25rem; color: #bfdbfe;">Enter your order number to see real-time tracking information</p>
            </div>
        </div>
    </section>

    <!-- Track Order Content -->
    <section style="padding: 4rem 0; background: #f9fafb;">
        <div class="container">
            <div style="max-width: 4xl; margin: 0 auto;">
                
                <!-- Order Tracking Form -->
                <div style="background: white; padding: 3rem; border-radius: 1rem; box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1); margin-bottom: 3rem;">
                    <div style="text-align: center; margin-bottom: 2rem;">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">📦</div>
                        <h2 style="font-size: 2rem; font-weight: bold; color: #111827; margin-bottom: 1rem;">Track Your Package</h2>
                        <p style="color: #6b7280;">Enter your order number and email to get real-time updates</p>
                    </div>
                    
                    <form id="track-order-form" style="max-width: 28rem; margin: 0 auto;">
                        <div style="display: flex; flex-direction: column; gap: 1rem;">
                            <div>
                                <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Order Number</label>
                                <input type="text" id="order-number" name="order_number" style="width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;" placeholder="e.g., DL4U-123456789" required>
                            </div>

                            <div>
                                <label style="display: block; font-weight: 500; color: #374151; margin-bottom: 0.5rem;">Email Address</label>
                                <input type="email" id="email-address" name="email" style="width: 100%; padding: 0.75rem 1rem; border: 1px solid #d1d5db; border-radius: 0.5rem; font-size: 1rem;" placeholder="<EMAIL>" required>
                            </div>

                            <button type="submit" id="track-button" style="background: linear-gradient(to right, #2563eb, #9333ea); color: white; font-weight: bold; padding: 0.75rem 1.5rem; border-radius: 0.5rem; border: none; cursor: pointer; font-size: 1rem; transition: all 0.3s; transform: scale(1);" onmouseover="this.style.transform='scale(1.02)'" onmouseout="this.style.transform='scale(1)'">
                                <i class="fas fa-search" style="margin-right: 0.5rem;"></i>Track Order
                            </button>
                        </div>
                    </form>
                </div>

                <!-- Order Results (Hidden by default) -->
                <div id="order-results" style="background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); overflow: hidden; margin-bottom: 3rem; display: none;">
                    <div id="order-status-header" style="background: linear-gradient(to right, #22c55e, #16a34a); color: white; padding: 1.5rem; text-align: center;">
                        <h3 id="order-status-title" style="font-size: 1.25rem; font-weight: bold;">Order Status: Processing</h3>
                        <p id="order-number-display" style="color: #dcfce7; margin-top: 0.5rem;">Order #</p>
                    </div>
                    
                    <div style="padding: 2rem;">
                        <!-- Order Progress -->
                        <div style="margin-bottom: 2rem;">
                            <div id="order-timeline" style="display: flex; justify-content: space-between; margin-bottom: 1rem;">
                                <!-- Timeline will be populated by JavaScript -->
                            </div>
                        </div>

                        <!-- Order Details -->
                        <div style="border-top: 1px solid #e5e7eb; padding-top: 2rem;">
                            <h4 style="font-size: 1.125rem; font-weight: bold; color: #111827; margin-bottom: 1rem;">Order Details</h4>

                            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 2rem;">
                                <div>
                                    <h5 style="font-weight: 600; color: #374151; margin-bottom: 0.5rem;">Shipping Address</h5>
                                    <div id="shipping-address" style="color: #6b7280; line-height: 1.6;">
                                        <!-- Address will be populated by JavaScript -->
                                    </div>
                                </div>

                                <div>
                                    <h5 style="font-weight: 600; color: #374151; margin-bottom: 0.5rem;">Estimated Delivery</h5>
                                    <p id="estimated-delivery" style="color: #6b7280; margin-bottom: 1rem;">Loading...</p>

                                    <h5 style="font-weight: 600; color: #374151; margin-bottom: 0.5rem;">Tracking Number</h5>
                                    <p id="tracking-number" style="color: #2563eb; font-family: monospace;">Loading...</p>
                                </div>
                            </div>
                        </div>

                        <!-- Items in Order -->
                        <div style="border-top: 1px solid #e5e7eb; padding-top: 2rem; margin-top: 2rem;">
                            <h4 style="font-size: 1.125rem; font-weight: bold; color: #111827; margin-bottom: 1rem;">Items in This Order</h4>

                            <div id="order-items" style="display: flex; flex-direction: column; gap: 1rem;">
                                <!-- Items will be populated by JavaScript -->
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Professional Help Section -->
                <div style="background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border-radius: 1rem; padding: 3rem; margin-top: 2rem;">
                    <div style="text-align: center; margin-bottom: 3rem;">
                        <h3 style="font-size: 2rem; font-weight: bold; color: #1e293b; margin-bottom: 0.5rem;">Customer Support</h3>
                        <p style="color: #64748b; font-size: 1.125rem;">We're here to help with any questions or concerns</p>
                    </div>

                    <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(320px, 1fr)); gap: 2rem;">

                        <!-- Need Help -->
                        <div style="background: white; padding: 2.5rem; border-radius: 1rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08); border: 1px solid #e2e8f0; text-align: center; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.12)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.08)'">
                            <div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 1rem; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; color: white; font-size: 1.5rem;">
                                <i class="fas fa-headset"></i>
                            </div>
                            <h4 style="font-size: 1.25rem; font-weight: 600; color: #1e293b; margin-bottom: 1rem;">Need Help?</h4>
                            <p style="color: #64748b; margin-bottom: 2rem; line-height: 1.6;">Can't find your order or have questions about shipping? Our support team is ready to assist you.</p>
                            <a href="<?php echo esc_url(home_url('/contact/')); ?>"
                               style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; font-weight: 600; padding: 0.875rem 2rem; border-radius: 0.75rem; text-decoration: none; display: inline-flex; align-items: center; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);"
                               onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 8px 20px rgba(59, 130, 246, 0.4)'" onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.3)'">
                                <i class="fas fa-comments" style="margin-right: 0.5rem;"></i>Contact Support
                            </a>
                        </div>

                        <!-- Order Issues -->
                        <div style="background: white; padding: 2.5rem; border-radius: 1rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08); border: 1px solid #e2e8f0; text-align: center; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.12)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.08)'">
                            <div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #ef4444, #dc2626); border-radius: 1rem; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; color: white; font-size: 1.5rem;">
                                <i class="fas fa-exclamation-circle"></i>
                            </div>
                            <h4 style="font-size: 1.25rem; font-weight: 600; color: #1e293b; margin-bottom: 1rem;">Order Issues?</h4>
                            <p style="color: #64748b; margin-bottom: 2rem; line-height: 1.6;">Report damaged, missing, or incorrect items. We'll resolve your issue quickly and efficiently.</p>
                            <a href="<?php echo esc_url(home_url('/contact/')); ?>"
                               style="background: linear-gradient(135deg, #ef4444, #dc2626); color: white; font-weight: 600; padding: 0.875rem 2rem; border-radius: 0.75rem; text-decoration: none; display: inline-flex; align-items: center; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);"
                               onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 8px 20px rgba(239, 68, 68, 0.4)'" onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(239, 68, 68, 0.3)'">
                                <i class="fas fa-flag" style="margin-right: 0.5rem;"></i>Report Issue
                            </a>
                        </div>

                        <!-- Returns -->
                        <div style="background: white; padding: 2.5rem; border-radius: 1rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08); border: 1px solid #e2e8f0; text-align: center; transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.12)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.08)'">
                            <div style="width: 4rem; height: 4rem; background: linear-gradient(135deg, #f59e0b, #d97706); border-radius: 1rem; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; color: white; font-size: 1.5rem;">
                                <i class="fas fa-undo-alt"></i>
                            </div>
                            <h4 style="font-size: 1.25rem; font-weight: 600; color: #1e293b; margin-bottom: 1rem;">Returns & Exchanges</h4>
                            <p style="color: #64748b; margin-bottom: 2rem; line-height: 1.6;">Start a return or exchange for your order. Easy process with free return shipping available.</p>
                            <a href="<?php echo esc_url(home_url('/contact/')); ?>"
                               style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white; font-weight: 600; padding: 0.875rem 2rem; border-radius: 0.75rem; text-decoration: none; display: inline-flex; align-items: center; transition: all 0.3s ease; box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3);"
                               onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 8px 20px rgba(245, 158, 11, 0.4)'" onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(245, 158, 11, 0.3)'">
                                <i class="fas fa-exchange-alt" style="margin-right: 0.5rem;"></i>Start Return
                            </a>
                        </div>

                    </div>

                    <!-- Additional Support Info -->
                    <div style="margin-top: 3rem; text-align: center; padding: 2rem; background: white; border-radius: 1rem; border: 1px solid #e2e8f0;">
                        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 2rem; align-items: center;">
                            <div>
                                <div style="color: #3b82f6; font-size: 1.5rem; margin-bottom: 0.5rem;">
                                    <i class="fas fa-clock"></i>
                                </div>
                                <h5 style="font-weight: 600; color: #1e293b; margin-bottom: 0.25rem;">Response Time</h5>
                                <p style="color: #64748b; font-size: 0.875rem;">Within 24 hours</p>
                            </div>
                            <div>
                                <div style="color: #10b981; font-size: 1.5rem; margin-bottom: 0.5rem;">
                                    <i class="fas fa-phone"></i>
                                </div>
                                <h5 style="font-weight: 600; color: #1e293b; margin-bottom: 0.25rem;">Phone Support</h5>
                                <p style="color: #64748b; font-size: 0.875rem;">+447447186806</p>
                            </div>
                            <div>
                                <div style="color: #8b5cf6; font-size: 1.5rem; margin-bottom: 0.5rem;">
                                    <i class="fas fa-envelope"></i>
                                </div>
                                <h5 style="font-weight: 600; color: #1e293b; margin-bottom: 0.25rem;">Email Support</h5>
                                <p style="color: #64748b; font-size: 0.875rem;"><EMAIL></p>
                            </div>
                            <div>
                                <div style="color: #f59e0b; font-size: 1.5rem; margin-bottom: 0.5rem;">
                                    <i class="fas fa-star"></i>
                                </div>
                                <h5 style="font-weight: 600; color: #1e293b; margin-bottom: 0.25rem;">Satisfaction</h5>
                                <p style="color: #64748b; font-size: 0.875rem;">99% Customer Rating</p>
                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </section>
</main>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const trackForm = document.getElementById('track-order-form');
    const orderResults = document.getElementById('order-results');
    const trackButton = document.getElementById('track-button');

    trackForm.addEventListener('submit', function(e) {
        e.preventDefault();

        const orderNumber = document.getElementById('order-number').value.trim();
        const email = document.getElementById('email-address').value.trim();

        if (!orderNumber || !email) {
            alert('Please enter both order number and email address.');
            return;
        }

        // Show loading state
        trackButton.innerHTML = '<i class="fas fa-spinner fa-spin" style="margin-right: 0.5rem;"></i>Tracking...';
        trackButton.disabled = true;

        // Make AJAX request
        fetch('<?php echo admin_url('admin-ajax.php'); ?>', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
            },
            body: new URLSearchParams({
                action: 'deal4u_track_order',
                order_number: orderNumber,
                email: email,
                nonce: '<?php echo wp_create_nonce('deal4u_nonce'); ?>'
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                displayOrderResults(data.data);
                orderResults.style.display = 'block';
                orderResults.scrollIntoView({ behavior: 'smooth' });
            } else {
                alert('Error: ' + data.data.message);
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('An error occurred while tracking your order. Please try again.');
        })
        .finally(() => {
            // Reset button state
            trackButton.innerHTML = '<i class="fas fa-search" style="margin-right: 0.5rem;"></i>Track Order';
            trackButton.disabled = false;
        });
    });

    function displayOrderResults(orderData) {
        // Update order status header
        const statusColors = {
            'pending': '#f59e0b',
            'processing': '#2563eb',
            'shipped': '#22c55e',
            'completed': '#16a34a',
            'cancelled': '#ef4444',
            'refunded': '#6b7280'
        };

        const statusColor = statusColors[orderData.status] || '#6b7280';
        document.getElementById('order-status-header').style.background = `linear-gradient(to right, ${statusColor}, ${statusColor}dd)`;
        document.getElementById('order-status-title').textContent = `Order Status: ${orderData.status_name}`;
        document.getElementById('order-number-display').textContent = `Order #${orderData.order_number}`;

        // Update timeline
        displayTimeline(orderData.timeline);

        // Update shipping address
        const shippingAddr = orderData.shipping_address;
        const addressHtml = `
            ${shippingAddr.first_name} ${shippingAddr.last_name}<br>
            ${shippingAddr.address_1}<br>
            ${shippingAddr.address_2 ? shippingAddr.address_2 + '<br>' : ''}
            ${shippingAddr.city}, ${shippingAddr.state} ${shippingAddr.postcode}
        `;
        document.getElementById('shipping-address').innerHTML = addressHtml;

        // Update delivery and tracking info
        document.getElementById('estimated-delivery').textContent = orderData.estimated_delivery || 'To be determined';
        document.getElementById('tracking-number').textContent = orderData.tracking_number || 'Not available yet';

        // Update order items
        displayOrderItems(orderData.items);
    }

    function displayTimeline(timeline) {
        const timelineContainer = document.getElementById('order-timeline');
        timelineContainer.innerHTML = '';

        timeline.forEach(step => {
            const iconClass = step.icon === 'check' ? 'fas fa-check' :
                             step.icon === 'truck' ? 'fas fa-truck' :
                             step.icon === 'home' ? 'fas fa-home' : 'fas fa-clock';

            const bgColor = step.completed ? '#22c55e' : (step.current ? '#2563eb' : '#d1d5db');
            const textColor = step.completed || step.current ? '#111827' : '#6b7280';

            const stepHtml = `
                <div style="text-align: center; flex: 1;">
                    <div style="width: 3rem; height: 3rem; background: ${bgColor}; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: white; margin: 0 auto 0.5rem;">
                        <i class="${iconClass}"></i>
                    </div>
                    <p style="font-size: 0.875rem; font-weight: 500; color: ${textColor};">${step.title}</p>
                    <p style="font-size: 0.75rem; color: #6b7280;">${step.date}</p>
                </div>
            `;
            timelineContainer.innerHTML += stepHtml;
        });
    }

    function displayOrderItems(items) {
        const itemsContainer = document.getElementById('order-items');
        itemsContainer.innerHTML = '';

        items.forEach(item => {
            const itemHtml = `
                <div style="display: flex; align-items: center; padding: 1rem; background: #f9fafb; border-radius: 0.5rem;">
                    <div style="width: 4rem; height: 4rem; background: #e5e7eb; border-radius: 0.5rem; display: flex; align-items: center; justify-content: center; font-size: 1.5rem; margin-right: 1rem; overflow: hidden;">
                        ${item.image ? `<img src="${item.image}" style="width: 100%; height: 100%; object-fit: cover;">` : '📦'}
                    </div>
                    <div style="flex: 1;">
                        <h5 style="font-weight: 600; color: #111827;">${item.name}</h5>
                        <p style="color: #6b7280; font-size: 0.875rem;">Quantity: ${item.quantity}</p>
                    </div>
                    <div style="font-weight: bold; color: #2563eb;">${item.total}</div>
                </div>
            `;
            itemsContainer.innerHTML += itemHtml;
        });
    }
});
</script>

<?php get_footer(); ?>
