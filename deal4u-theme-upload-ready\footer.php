<!-- Footer -->
<footer class="bg-gray-900 text-white py-16">
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            <!-- Company Info -->
            <div>
                <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-2 rounded-lg font-bold text-xl inline-block mb-4">
                    Deal4u
                </div>
                <p class="text-gray-300 mb-6">Your premier destination for high-quality gaming consoles, electronics, and tech products at unbeatable prices.</p>
                <div class="flex space-x-4">
                    <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors">
                        <i class="fab fa-facebook text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors">
                        <i class="fab fa-twitter text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors">
                        <i class="fab fa-instagram text-xl"></i>
                    </a>
                    <a href="#" class="text-gray-400 hover:text-blue-400 transition-colors">
                        <i class="fab fa-youtube text-xl"></i>
                    </a>
                </div>
            </div>

            <!-- Quick Links -->
            <div>
                <h3 class="text-lg font-semibold mb-4">Quick Links</h3>
                <ul class="space-y-2">
                    <li><a href="<?php echo esc_url(home_url('/')); ?>" class="text-gray-300 hover:text-blue-400 transition-colors">Home</a></li>
                    <li><a href="<?php echo function_exists('wc_get_page_permalink') ? esc_url(wc_get_page_permalink('shop')) : esc_url(home_url('/shop/')); ?>" class="text-gray-300 hover:text-blue-400 transition-colors">Shop</a></li>
                    <li><a href="<?php echo esc_url(home_url('/categories/')); ?>" class="text-gray-300 hover:text-blue-400 transition-colors">Categories</a></li>
                    <li><a href="<?php echo esc_url(home_url('/about/')); ?>" class="text-gray-300 hover:text-blue-400 transition-colors">About</a></li>
                    <li><a href="<?php echo esc_url(home_url('/contact/')); ?>" class="text-gray-300 hover:text-blue-400 transition-colors">Contact</a></li>
                </ul>
            </div>

            <!-- Customer Service -->
            <div>
                <h3 class="text-lg font-semibold mb-4">Customer Service</h3>
                <ul class="space-y-2">
                    <li><a href="<?php echo esc_url(home_url('/track-order/')); ?>" class="text-gray-300 hover:text-blue-400 transition-colors">Track Order</a></li>
                    <li><a href="<?php echo esc_url(home_url('/faq/')); ?>" class="text-gray-300 hover:text-blue-400 transition-colors">FAQ</a></li>
                    <li><a href="<?php echo esc_url(home_url('/shipping-info/')); ?>" class="text-gray-300 hover:text-blue-400 transition-colors">Shipping Info</a></li>
                    <li><a href="<?php echo function_exists('wc_get_page_permalink') ? esc_url(wc_get_page_permalink('myaccount')) : esc_url(home_url('/my-account/')); ?>" class="text-gray-300 hover:text-blue-400 transition-colors">My Account</a></li>
                    <li><a href="<?php echo esc_url(home_url('/contact/')); ?>" class="text-gray-300 hover:text-blue-400 transition-colors">Support</a></li>
                </ul>
            </div>

            <!-- Contact Info -->
            <div>
                <h3 class="text-lg font-semibold mb-4">Contact Info</h3>
                <ul class="space-y-3 text-gray-300">
                    <li class="flex items-center">
                        <i class="fas fa-map-marker-alt mr-3 text-blue-400"></i>
                        Fröbelstraße 12, 41515 Grevenbroich, Germany
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-phone mr-3 text-blue-400"></i>
                        +************
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-envelope mr-3 text-blue-400"></i>
                        <EMAIL>
                    </li>
                    <li class="flex items-center">
                        <i class="fas fa-clock mr-3 text-blue-400"></i>
                        Mon-Fri: 9AM-6PM
                    </li>
                </ul>
            </div>
        </div>

        <!-- Bottom Section -->
        <div class="border-t border-gray-800 mt-12 pt-8">
            <div class="flex flex-col md:flex-row justify-between items-center">
                <p class="text-gray-400 text-sm">
                    &copy; <?php echo date('Y'); ?> Deal4u. All rights reserved.
                </p>
                <div class="flex space-x-6 mt-4 md:mt-0">
                    <a href="<?php echo esc_url(home_url('/privacy-policy/')); ?>" class="text-gray-400 hover:text-blue-400 text-sm transition-colors">Privacy Policy</a>
                    <a href="<?php echo esc_url(home_url('/terms-of-service/')); ?>" class="text-gray-400 hover:text-blue-400 text-sm transition-colors">Terms of Service</a>
                    <a href="<?php echo esc_url(home_url('/sitemap/')); ?>" class="text-gray-400 hover:text-blue-400 text-sm transition-colors">Sitemap</a>
                </div>
            </div>
        </div>
    </div>
</footer>

<!-- WhatsApp Floating Button -->
<div class="fixed bottom-6 right-6 z-50">
    <a href="https://wa.me/************?text=Hello%20Deal4u!%20I%20need%20help%20with%20my%20order" target="_blank" class="flex items-center justify-center w-14 h-14 bg-green-500 hover:bg-green-600 text-white rounded-full shadow-lg hover:shadow-xl transition-all duration-300 transform hover:scale-110">
        <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 24 24">
            <path d="M17.472 14.382c-.297-.149-1.758-.867-2.03-.967-.273-.099-.471-.148-.67.15-.197.297-.767.966-.94 1.164-.173.199-.347.223-.644.075-.297-.15-1.255-.463-2.39-1.475-.883-.788-1.48-1.761-1.653-2.059-.173-.297-.018-.458.13-.606.134-.133.298-.347.446-.52.149-.174.198-.298.298-.497.099-.198.05-.371-.025-.52-.075-.149-.669-1.612-.916-2.207-.242-.579-.487-.5-.669-.51-.173-.008-.371-.01-.57-.01-.198 0-.52.074-.792.372-.272.297-1.04 1.016-1.04 2.479 0 1.462 1.065 2.875 1.213 3.074.149.198 2.096 3.2 5.077 4.487.709.306 1.262.489 1.694.625.712.227 1.36.195 1.871.118.571-.085 1.758-.719 2.006-1.413.248-.694.248-1.289.173-1.413-.074-.124-.272-.198-.57-.347m-5.421 7.403h-.004a9.87 9.87 0 01-5.031-1.378l-.361-.214-3.741.982.998-3.648-.235-.374a9.86 9.86 0 01-1.51-5.26c.001-5.45 4.436-9.884 9.888-9.884 2.64 0 5.122 1.03 6.988 2.898a9.825 9.825 0 012.893 6.994c-.003 5.45-4.437 9.884-9.885 9.884m8.413-18.297A11.815 11.815 0 0012.05 0C5.495 0 .16 5.335.157 11.892c0 2.096.547 4.142 1.588 5.945L.057 24l6.305-1.654a11.882 11.882 0 005.683 1.448h.005c6.554 0 11.89-5.335 11.893-11.893A11.821 11.821 0 0020.885 3.515z"/>
        </svg>
    </a>
</div>

<?php wp_footer(); ?>

<!-- Custom JavaScript -->
<script>
    // Add to cart functionality
    document.addEventListener('DOMContentLoaded', function() {
        const addToCartButtons = document.querySelectorAll('.add-to-cart-btn');

        addToCartButtons.forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();

                const productId = this.dataset.productId;
                const quantity = this.dataset.quantity || 1;

                // AJAX call to add to cart
                if (typeof deal4u_ajax !== 'undefined') {
                    fetch(deal4u_ajax.ajax_url, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/x-www-form-urlencoded',
                        },
                        body: new URLSearchParams({
                            action: 'deal4u_add_to_cart',
                            product_id: productId,
                            quantity: quantity,
                            nonce: deal4u_ajax.nonce
                        })
                    })
                    .then(response => response.json())
                    .then(data => {
                        if (data.success) {
                            alert('Product added to cart!');
                            // Update cart count if element exists
                            const cartCount = document.querySelector('.cart-count');
                            if (cartCount) {
                                cartCount.textContent = parseInt(cartCount.textContent) + parseInt(quantity);
                            }
                        } else {
                            alert('Error: ' + data.data.message);
                        }
                    })
                    .catch(error => {
                        console.error('Error:', error);
                        alert('An error occurred. Please try again.');
                    });
                } else {
                    window.location.href = '<?php echo wc_get_cart_url(); ?>?add-to-cart=' + productId + '&quantity=' + quantity;
                }
            });
        });
    });
</script>

</body>
</html>
