<?php
/**
 * <PERSON><PERSON>
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/emails/email-header.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates\Emails
 * @version 9.8.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit; // Exit if accessed directly
}

?>
<!DOCTYPE html>
<html <?php language_attributes(); ?>>
	<head>
		<meta http-equiv="Content-Type" content="text/html; charset=<?php bloginfo( 'charset' ); ?>" />
		<meta content="width=device-width, initial-scale=1.0" name="viewport">
		<title><?php echo get_bloginfo( 'name', 'display' ); ?></title>
		<style type="text/css">
			/* Email Styles */
			body {
				background-color: #f7f7f7;
				margin: 0;
				padding: 0;
				-webkit-text-size-adjust: none !important;
				width: 100% !important;
				font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
			}
			
			.email-container {
				max-width: 600px;
				margin: 0 auto;
				background-color: #ffffff;
			}
			
			.email-header {
				background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
				padding: 40px 30px;
				text-align: center;
			}
			
			.email-header h1 {
				color: #ffffff;
				font-size: 32px;
				font-weight: 700;
				margin: 0;
				text-shadow: 0 2px 4px rgba(0,0,0,0.1);
			}
			
			.email-header p {
				color: #e0e7ff;
				font-size: 16px;
				margin: 10px 0 0 0;
			}
			
			.email-content {
				padding: 40px 30px;
				line-height: 1.6;
				color: #333333;
			}
			
			.email-content h2 {
				color: #2563eb;
				font-size: 24px;
				font-weight: 600;
				margin: 0 0 20px 0;
			}
			
			.email-content h3 {
				color: #374151;
				font-size: 18px;
				font-weight: 600;
				margin: 25px 0 15px 0;
			}
			
			.email-content p {
				margin: 0 0 16px 0;
				font-size: 16px;
			}
			
			.order-details {
				background-color: #f8fafc;
				border: 1px solid #e2e8f0;
				border-radius: 8px;
				padding: 20px;
				margin: 20px 0;
			}
			
			.order-table {
				width: 100%;
				border-collapse: collapse;
				margin: 20px 0;
			}
			
			.order-table th,
			.order-table td {
				padding: 12px;
				text-align: left;
				border-bottom: 1px solid #e2e8f0;
			}
			
			.order-table th {
				background-color: #f1f5f9;
				font-weight: 600;
				color: #475569;
				font-size: 14px;
				text-transform: uppercase;
				letter-spacing: 0.05em;
			}
			
			.order-total {
				font-weight: 700;
				font-size: 18px;
				color: #2563eb;
			}
			
			.button {
				display: inline-block;
				background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
				color: #ffffff !important;
				text-decoration: none;
				padding: 15px 30px;
				border-radius: 8px;
				font-weight: 600;
				font-size: 16px;
				margin: 20px 0;
				box-shadow: 0 4px 14px 0 rgba(37, 99, 235, 0.3);
			}
			
			.button:hover {
				background: linear-gradient(135deg, #1d4ed8 0%, #6d28d9 100%);
			}
			
			.email-footer {
				background-color: #1f2937;
				color: #9ca3af;
				padding: 30px;
				text-align: center;
				font-size: 14px;
			}
			
			.email-footer a {
				color: #60a5fa;
				text-decoration: none;
			}
			
			.social-links {
				margin: 20px 0;
			}
			
			.social-links a {
				display: inline-block;
				margin: 0 10px;
				color: #60a5fa;
				text-decoration: none;
			}
			
			/* Mobile Responsive */
			@media only screen and (max-width: 600px) {
				.email-container {
					width: 100% !important;
				}
				
				.email-header,
				.email-content,
				.email-footer {
					padding: 20px !important;
				}
				
				.email-header h1 {
					font-size: 24px !important;
				}
				
				.order-table th,
				.order-table td {
					padding: 8px !important;
					font-size: 14px !important;
				}
			}
		</style>
	</head>
	<body>
		<div class="email-container">
			<div class="email-header">
				<h1><?php echo esc_html( get_bloginfo( 'name' ) ); ?></h1>
				<p>Your trusted online shopping destination</p>
			</div>
			<div class="email-content">
