<?php
/**
 * Customer processing order email
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/emails/customer-processing-order.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates\Emails
 * @version 9.9.0
 */

if ( ! defined( 'ABSPATH' ) ) {
	exit;
}

/*
 * @hooked WC_Emails::email_header() Output the email header
 */
do_action( 'woocommerce_email_header', $email_heading, $email ); ?>

<h2 style="color: #2563eb; font-size: 24px; font-weight: 600; margin: 0 0 20px 0;">
	🎉 Thank you for your order!
</h2>

<p style="font-size: 16px; margin: 0 0 16px 0;">
	Hi <?php echo esc_html( $order->get_billing_first_name() ); ?>,
</p>

<p style="font-size: 16px; margin: 0 0 16px 0;">
	We're excited to let you know that we've received your order and it's being processed. 
	Your order details are shown below for your reference:
</p>

<div class="order-details">
	<h3 style="color: #374151; font-size: 18px; font-weight: 600; margin: 0 0 15px 0;">
		📦 Order #<?php echo esc_html( $order->get_order_number() ); ?>
	</h3>
	
	<p style="margin: 0 0 10px 0;">
		<strong>Order Date:</strong> <?php echo esc_html( wc_format_datetime( $order->get_date_created() ) ); ?><br>
		<strong>Order Status:</strong> <span style="color: #059669; font-weight: 600;">Processing</span><br>
		<strong>Payment Method:</strong> <?php echo esc_html( $order->get_payment_method_title() ); ?>
	</p>
</div>

<?php
/*
 * @hooked WC_Emails::order_details() Shows the order details table.
 * @hooked WC_Emails::order_schema_markup() Adds Schema.org markup.
 * @since 2.5.0
 */
do_action( 'woocommerce_email_order_details', $order, $sent_to_admin, $plain_text, $email );

/*
 * @hooked WC_Emails::order_meta() Shows order meta data.
 */
do_action( 'woocommerce_email_order_meta', $order, $sent_to_admin, $plain_text, $email );

/*
 * @hooked WC_Emails::customer_details() Shows customer details
 * @hooked WC_Emails::email_address() Shows email address
 */
do_action( 'woocommerce_email_customer_details', $order, $sent_to_admin, $plain_text, $email );
?>

<div style="background-color: #f0f9ff; border: 1px solid #0ea5e9; border-radius: 8px; padding: 20px; margin: 30px 0;">
	<h3 style="color: #0369a1; font-size: 18px; font-weight: 600; margin: 0 0 15px 0;">
		📋 What happens next?
	</h3>
	
	<div style="display: flex; justify-content: space-between; flex-wrap: wrap; margin: 20px 0;">
		<div style="text-align: center; margin: 10px; flex: 1; min-width: 120px;">
			<div style="font-size: 24px; margin-bottom: 8px;">📦</div>
			<div style="font-size: 14px; font-weight: 600; color: #374151;">Processing</div>
			<div style="font-size: 12px; color: #6b7280;">We're preparing your order</div>
		</div>
		<div style="text-align: center; margin: 10px; flex: 1; min-width: 120px;">
			<div style="font-size: 24px; margin-bottom: 8px;">🚚</div>
			<div style="font-size: 14px; font-weight: 600; color: #374151;">Shipping</div>
			<div style="font-size: 12px; color: #6b7280;">Your order is on its way</div>
		</div>
		<div style="text-align: center; margin: 10px; flex: 1; min-width: 120px;">
			<div style="font-size: 24px; margin-bottom: 8px;">📬</div>
			<div style="font-size: 14px; font-weight: 600; color: #374151;">Delivered</div>
			<div style="font-size: 12px; color: #6b7280;">Enjoy your purchase!</div>
		</div>
	</div>
	
	<p style="margin: 15px 0 0 0; font-size: 14px; color: #374151;">
		<strong>Estimated delivery:</strong> 3-5 business days<br>
		<strong>Tracking information:</strong> We'll send you tracking details once your order ships
	</p>
</div>

<div style="text-align: center; margin: 30px 0;">
	<a href="<?php echo esc_url( $order->get_view_order_url() ); ?>" class="button">
		👁️ View Your Order
	</a>
	
	<a href="<?php echo esc_url( home_url( '/my-account/' ) ); ?>" class="button" style="margin-left: 10px;">
		👤 My Account
	</a>
</div>

<div style="background-color: #fef3c7; border: 1px solid #f59e0b; border-radius: 8px; padding: 20px; margin: 30px 0;">
	<h3 style="color: #92400e; font-size: 16px; font-weight: 600; margin: 0 0 10px 0;">
		💡 Need help with your order?
	</h3>
	<p style="margin: 0; font-size: 14px; color: #78350f;">
		Our customer support team is here to help! Contact us at 
		<a href="mailto:<EMAIL>" style="color: #92400e; font-weight: 600;"><EMAIL></a> 
		or visit our <a href="<?php echo esc_url( home_url( '/faq/' ) ); ?>" style="color: #92400e; font-weight: 600;">FAQ page</a> 
		for quick answers.
	</p>
</div>

<p style="font-size: 16px; margin: 30px 0 0 0;">
	Thanks for choosing Deal4u! We appreciate your business and look forward to serving you again.
</p>

<p style="font-size: 16px; margin: 10px 0 0 0;">
	Best regards,<br>
	<strong>The Deal4u Team</strong>
</p>

<?php
/*
 * @hooked WC_Emails::email_footer() Output the email footer
 */
do_action( 'woocommerce_email_footer', $email );
?>
