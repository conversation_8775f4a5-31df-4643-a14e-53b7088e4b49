<?php
/**
 * Template Name: About Page
 */

get_header(); ?>

<main class="main-content">
    <!-- Modern Dynamic Hero Section -->
    <section style="padding: 8rem 0; background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%); color: white; position: relative; overflow: hidden;">
        <!-- Dynamic Background Elements -->
        <div style="position: absolute; top: -10%; left: -10%; width: 30%; height: 30%; background: linear-gradient(45deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05)); border-radius: 50%; animation: float 6s ease-in-out infinite;"></div>
        <div style="position: absolute; top: 20%; right: -5%; width: 25%; height: 25%; background: linear-gradient(45deg, rgba(255,255,255,0.08), rgba(255,255,255,0.03)); border-radius: 50%; animation: float 8s ease-in-out infinite reverse;"></div>
        <div style="position: absolute; bottom: -15%; left: 20%; width: 35%; height: 35%; background: linear-gradient(45deg, rgba(255,255,255,0.06), rgba(255,255,255,0.02)); border-radius: 50%; animation: float 10s ease-in-out infinite;"></div>

        <!-- Geometric Shapes -->
        <div style="position: absolute; top: 15%; left: 10%; width: 100px; height: 100px; border: 2px solid rgba(255,255,255,0.2); border-radius: 20px; transform: rotate(45deg); animation: rotate 20s linear infinite;"></div>
        <div style="position: absolute; bottom: 20%; right: 15%; width: 80px; height: 80px; border: 2px solid rgba(255,255,255,0.15); border-radius: 50%; animation: pulse 4s ease-in-out infinite;"></div>
        <div style="position: absolute; top: 40%; right: 25%; width: 60px; height: 60px; background: rgba(255,255,255,0.1); transform: rotate(45deg); animation: float 7s ease-in-out infinite;"></div>

        <div class="container" style="position: relative; z-index: 10;">
            <div style="text-align: center; max-width: 1000px; margin: 0 auto;">
                <!-- Modern Icon Design -->
                <div style="display: inline-flex; align-items: center; justify-content: center; width: 8rem; height: 8rem; background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1)); border-radius: 2rem; margin-bottom: 3rem; backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.3); box-shadow: 0 8px 32px rgba(0,0,0,0.1);">
                    <i class="fas fa-rocket" style="font-size: 3rem; background: linear-gradient(135deg, #ffffff, #f0f9ff); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;"></i>
                </div>

                <!-- Dynamic Typography -->
                <h1 style="font-size: 5rem; font-weight: 900; margin-bottom: 2rem; line-height: 1.1; background: linear-gradient(135deg, #ffffff 0%, #f0f9ff 50%, #e0f2fe 100%); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; text-shadow: 0 4px 20px rgba(0,0,0,0.1);">
                    About Deal4u
                </h1>

                <p style="font-size: 1.75rem; color: rgba(255,255,255,0.9); max-width: 800px; margin: 0 auto 3rem; line-height: 1.7; font-weight: 300;">
                    Your trusted partner for <span style="font-weight: 600; background: linear-gradient(135deg, #fbbf24, #f59e0b); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">amazing deals</span> on premium products. We're committed to bringing you quality, value, and exceptional service.
                </p>

                <!-- Enhanced Stats with Modern Design -->
                <div style="display: flex; justify-content: center; gap: 1.5rem; margin-top: 4rem; max-width: 900px; margin-left: auto; margin-right: auto; flex-wrap: nowrap;">
                    <div style="text-align: center; padding: 1.5rem 1rem; background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05)); border-radius: 1.2rem; backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.2); transition: all 0.3s; box-shadow: 0 8px 32px rgba(0,0,0,0.1); min-width: 140px; flex: 1;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 12px 40px rgba(0,0,0,0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 32px rgba(0,0,0,0.1)'">
                        <div style="font-size: 2.2rem; font-weight: 900; background: linear-gradient(135deg, #60a5fa, #3b82f6); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 0.5rem;">2019</div>
                        <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem; font-weight: 500; text-transform: uppercase; letter-spacing: 1px;">Founded</div>
                    </div>

                    <div style="text-align: center; padding: 1.5rem 1rem; background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05)); border-radius: 1.2rem; backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.2); transition: all 0.3s; box-shadow: 0 8px 32px rgba(0,0,0,0.1); min-width: 140px; flex: 1;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 12px 40px rgba(0,0,0,0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 32px rgba(0,0,0,0.1)'">
                        <div style="font-size: 2.2rem; font-weight: 900; background: linear-gradient(135deg, #34d399, #10b981); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 0.5rem;">15K+</div>
                        <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem; font-weight: 500; text-transform: uppercase; letter-spacing: 1px;">Happy Customers</div>
                    </div>

                    <div style="text-align: center; padding: 1.5rem 1rem; background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05)); border-radius: 1.2rem; backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.2); transition: all 0.3s; box-shadow: 0 8px 32px rgba(0,0,0,0.1); min-width: 140px; flex: 1;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 12px 40px rgba(0,0,0,0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 32px rgba(0,0,0,0.1)'">
                        <div style="font-size: 2.2rem; font-weight: 900; background: linear-gradient(135deg, #fbbf24, #f59e0b); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 0.5rem;">1000+</div>
                        <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem; font-weight: 500; text-transform: uppercase; letter-spacing: 1px;">Products</div>
                    </div>

                    <div style="text-align: center; padding: 1.5rem 1rem; background: linear-gradient(135deg, rgba(255,255,255,0.15), rgba(255,255,255,0.05)); border-radius: 1.2rem; backdrop-filter: blur(20px); border: 1px solid rgba(255,255,255,0.2); transition: all 0.3s; box-shadow: 0 8px 32px rgba(0,0,0,0.1); min-width: 140px; flex: 1;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 12px 40px rgba(0,0,0,0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 8px 32px rgba(0,0,0,0.1)'">
                        <div style="font-size: 2.2rem; font-weight: 900; background: linear-gradient(135deg, #f472b6, #ec4899); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; margin-bottom: 0.5rem;">24/7</div>
                        <div style="color: rgba(255,255,255,0.8); font-size: 0.8rem; font-weight: 500; text-transform: uppercase; letter-spacing: 1px;">Support</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- CSS Animations -->
        <style>
            @keyframes float {
                0%, 100% { transform: translateY(0px); }
                50% { transform: translateY(-20px); }
            }
            @keyframes rotate {
                0% { transform: rotate(45deg); }
                100% { transform: rotate(405deg); }
            }
            @keyframes pulse {
                0%, 100% { transform: scale(1); opacity: 0.8; }
                50% { transform: scale(1.1); opacity: 1; }
            }
        </style>
    </section>

    <!-- Main Content -->
    <section style="padding: 5rem 0; background: #f8fafc;">
        <div class="container">
            <!-- Our Story -->
            <div style="margin-bottom: 6rem;">
                <div style="max-width: 1200px; margin: 0 auto;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 4rem; align-items: center;">
                        <div>
                            <div style="display: inline-flex; align-items: center; padding: 0.5rem 1rem; background: #dbeafe; color: #1d4ed8; border-radius: 2rem; font-size: 0.875rem; font-weight: 600; margin-bottom: 1.5rem;">
                                <i class="fas fa-history" style="margin-right: 0.5rem;"></i>
                                Our Journey
                            </div>
                            <h2 style="font-size: 3rem; font-weight: bold; color: #1e293b; margin-bottom: 2rem; line-height: 1.2;">Our Story</h2>
                            <p style="color: #475569; margin-bottom: 2rem; line-height: 1.7; font-size: 1.125rem;">
                                Founded in <strong>2019</strong> with a passion for bringing customers the best deals on quality products, Deal4u has grown from a small startup to a trusted e-commerce destination. We believe that everyone deserves access to premium products at affordable prices.
                            </p>
                            <p style="color: #475569; margin-bottom: 2rem; line-height: 1.7; font-size: 1.125rem;">
                                Our team works tirelessly to source the latest gaming consoles, electronics, and tech accessories from trusted manufacturers, ensuring that every product meets our high standards for quality and value.
                            </p>

                            <!-- Achievement Stats -->
                            <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 1.5rem; margin-top: 2rem;">
                                <div style="text-align: center; padding: 1.5rem; background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                                    <div style="font-size: 2.5rem; font-weight: bold; color: #2563eb; margin-bottom: 0.5rem;">98%</div>
                                    <div style="color: #64748b; font-size: 0.875rem; font-weight: 500;">Customer Satisfaction</div>
                                </div>
                                <div style="text-align: center; padding: 1.5rem; background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                                    <div style="font-size: 2.5rem; font-weight: bold; color: #16a34a; margin-bottom: 0.5rem;">50+</div>
                                    <div style="color: #64748b; font-size: 0.875rem; font-weight: 500;">Brand Partners</div>
                                </div>
                                <div style="text-align: center; padding: 1.5rem; background: white; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);">
                                    <div style="font-size: 2.5rem; font-weight: bold; color: #dc2626; margin-bottom: 0.5rem;">5★</div>
                                    <div style="color: #64748b; font-size: 0.875rem; font-weight: 500;">Average Rating</div>
                                </div>
                            </div>
                        </div>

                        <div style="position: relative;">
                            <!-- Mission Card -->
                            <div style="background: linear-gradient(135deg, #2563eb, #1d4ed8); color: white; padding: 3rem; border-radius: 1.5rem; box-shadow: 0 20px 40px rgba(37, 99, 235, 0.3); position: relative; overflow: hidden;">
                                <!-- Background Pattern -->
                                <div style="position: absolute; top: -50%; right: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px); background-size: 20px 20px; opacity: 0.5;"></div>

                                <div style="position: relative; z-index: 1;">
                                    <div style="display: inline-flex; align-items: center; justify-content: center; width: 4rem; height: 4rem; background: rgba(255, 255, 255, 0.2); border-radius: 1rem; margin-bottom: 2rem;">
                                        <i class="fas fa-bullseye" style="font-size: 1.5rem;"></i>
                                    </div>
                                    <h3 style="font-size: 2rem; font-weight: bold; margin-bottom: 1.5rem;">Our Mission</h3>
                                    <p style="color: #bfdbfe; line-height: 1.6; font-size: 1.125rem;">To make premium technology accessible to everyone through unbeatable deals and exceptional customer service.</p>

                                    <!-- Mission Points -->
                                    <div style="margin-top: 2rem; display: flex; flex-direction: column; gap: 1rem;">
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 0.5rem; height: 0.5rem; background: #60a5fa; border-radius: 50%; margin-right: 1rem;"></div>
                                            <span style="color: #e2e8f0; font-size: 0.875rem;">Quality products at affordable prices</span>
                                        </div>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 0.5rem; height: 0.5rem; background: #60a5fa; border-radius: 50%; margin-right: 1rem;"></div>
                                            <span style="color: #e2e8f0; font-size: 0.875rem;">Exceptional customer experience</span>
                                        </div>
                                        <div style="display: flex; align-items: center;">
                                            <div style="width: 0.5rem; height: 0.5rem; background: #60a5fa; border-radius: 50%; margin-right: 1rem;"></div>
                                            <span style="color: #e2e8f0; font-size: 0.875rem;">Building lasting relationships</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Company Timeline -->
            <div style="margin-bottom: 6rem;">
                <div style="text-align: center; margin-bottom: 4rem;">
                    <div style="display: inline-flex; align-items: center; padding: 0.5rem 1rem; background: #f0fdf4; color: #16a34a; border-radius: 2rem; font-size: 0.875rem; font-weight: 600; margin-bottom: 1.5rem;">
                        <i class="fas fa-timeline" style="margin-right: 0.5rem;"></i>
                        Our Journey
                    </div>
                    <h2 style="font-size: 3rem; font-weight: bold; color: #1e293b; margin-bottom: 1rem;">Company Timeline</h2>
                    <p style="color: #64748b; max-width: 600px; margin: 0 auto; font-size: 1.125rem;">Key milestones in our journey to becoming a trusted technology retailer</p>
                </div>

                <div style="max-width: 1000px; margin: 0 auto;">
                    <div style="position: relative;">
                        <!-- Timeline Line -->
                        <div style="position: absolute; left: 50%; top: 0; bottom: 0; width: 2px; background: linear-gradient(to bottom, #3b82f6, #8b5cf6); transform: translateX(-50%);"></div>

                        <!-- Timeline Items -->
                        <div style="display: flex; flex-direction: column; gap: 3rem;">
                            <!-- 2019 -->
                            <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 2rem; align-items: center;">
                                <div style="text-align: right;">
                                    <div style="background: white; padding: 2rem; border-radius: 1rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); border-left: 4px solid #3b82f6;">
                                        <h4 style="font-size: 1.25rem; font-weight: bold; color: #1e293b; margin-bottom: 0.5rem;">Company Founded</h4>
                                        <p style="color: #64748b; margin-bottom: 1rem; font-size: 0.875rem;">Started with a vision to make premium technology accessible to everyone</p>
                                        <div style="display: flex; align-items: center; justify-content: flex-end; gap: 0.5rem;">
                                            <i class="fas fa-rocket" style="color: #3b82f6;"></i>
                                            <span style="color: #3b82f6; font-weight: 600; font-size: 0.875rem;">Launch</span>
                                        </div>
                                    </div>
                                </div>
                                <div style="display: flex; align-items: center; justify-content: center; width: 4rem; height: 4rem; background: #3b82f6; color: white; border-radius: 50%; font-weight: bold; box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3); z-index: 1;">
                                    2019
                                </div>
                                <div></div>
                            </div>

                            <!-- 2020 -->
                            <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 2rem; align-items: center;">
                                <div></div>
                                <div style="display: flex; align-items: center; justify-content: center; width: 4rem; height: 4rem; background: #16a34a; color: white; border-radius: 50%; font-weight: bold; box-shadow: 0 4px 12px rgba(22, 163, 74, 0.3); z-index: 1;">
                                    2020
                                </div>
                                <div style="text-align: left;">
                                    <div style="background: white; padding: 2rem; border-radius: 1rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); border-left: 4px solid #16a34a;">
                                        <h4 style="font-size: 1.25rem; font-weight: bold; color: #1e293b; margin-bottom: 0.5rem;">First 1,000 Customers</h4>
                                        <p style="color: #64748b; margin-bottom: 1rem; font-size: 0.875rem;">Reached our first major milestone with exceptional customer satisfaction</p>
                                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                                            <i class="fas fa-users" style="color: #16a34a;"></i>
                                            <span style="color: #16a34a; font-weight: 600; font-size: 0.875rem;">Growth</span>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- 2022 -->
                            <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 2rem; align-items: center;">
                                <div style="text-align: right;">
                                    <div style="background: white; padding: 2rem; border-radius: 1rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); border-left: 4px solid #8b5cf6;">
                                        <h4 style="font-size: 1.25rem; font-weight: bold; color: #1e293b; margin-bottom: 0.5rem;">Major Brand Partnerships</h4>
                                        <p style="color: #64748b; margin-bottom: 1rem; font-size: 0.875rem;">Secured partnerships with leading technology manufacturers</p>
                                        <div style="display: flex; align-items: center; justify-content: flex-end; gap: 0.5rem;">
                                            <i class="fas fa-handshake" style="color: #8b5cf6;"></i>
                                            <span style="color: #8b5cf6; font-weight: 600; font-size: 0.875rem;">Partnerships</span>
                                        </div>
                                    </div>
                                </div>
                                <div style="display: flex; align-items: center; justify-content: center; width: 4rem; height: 4rem; background: #8b5cf6; color: white; border-radius: 50%; font-weight: bold; box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3); z-index: 1;">
                                    2022
                                </div>
                                <div></div>
                            </div>

                            <!-- 2024 -->
                            <div style="display: grid; grid-template-columns: 1fr auto 1fr; gap: 2rem; align-items: center;">
                                <div></div>
                                <div style="display: flex; align-items: center; justify-content: center; width: 4rem; height: 4rem; background: #f59e0b; color: white; border-radius: 50%; font-weight: bold; box-shadow: 0 4px 12px rgba(245, 158, 11, 0.3); z-index: 1;">
                                    2024
                                </div>
                                <div style="text-align: left;">
                                    <div style="background: white; padding: 2rem; border-radius: 1rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); border-left: 4px solid #f59e0b;">
                                        <h4 style="font-size: 1.25rem; font-weight: bold; color: #1e293b; margin-bottom: 0.5rem;">15,000+ Happy Customers</h4>
                                        <p style="color: #64748b; margin-bottom: 1rem; font-size: 0.875rem;">Celebrating our growing community and expanding product range</p>
                                        <div style="display: flex; align-items: center; gap: 0.5rem;">
                                            <i class="fas fa-trophy" style="color: #f59e0b;"></i>
                                            <span style="color: #f59e0b; font-weight: 600; font-size: 0.875rem;">Achievement</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Values Section -->
            <div style="margin-bottom: 6rem; background: white; padding: 4rem 0; border-radius: 2rem; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);">
                <div style="text-align: center; margin-bottom: 4rem;">
                    <div style="display: inline-flex; align-items: center; padding: 0.5rem 1rem; background: #fef3c7; color: #d97706; border-radius: 2rem; font-size: 0.875rem; font-weight: 600; margin-bottom: 1.5rem;">
                        <i class="fas fa-heart" style="margin-right: 0.5rem;"></i>
                        Our Core Values
                    </div>
                    <h2 style="font-size: 3rem; font-weight: bold; color: #1e293b; margin-bottom: 1rem;">What Drives Us</h2>
                    <p style="color: #64748b; max-width: 600px; margin: 0 auto; font-size: 1.125rem;">The principles that guide everything we do and shape our commitment to excellence</p>
                </div>

                <div style="display: flex; justify-content: center; gap: 1.5rem; max-width: 1200px; margin: 0 auto; flex-wrap: nowrap;">
                    <div style="background: linear-gradient(135deg, #f8fafc, #ffffff); padding: 2rem 1.5rem; border-radius: 1.2rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08); text-align: center; border: 1px solid #e2e8f0; transition: all 0.3s; position: relative; overflow: hidden; flex: 1; min-width: 200px;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.08)'">
                        <div style="position: absolute; top: -50%; right: -50%; width: 80px; height: 80px; background: linear-gradient(45deg, #dbeafe, #bfdbfe); border-radius: 50%; opacity: 0.3;"></div>
                        <div style="background: linear-gradient(135deg, #dbeafe, #bfdbfe); color: #1d4ed8; width: 4rem; height: 4rem; border-radius: 1.2rem; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; font-size: 1.5rem; position: relative; z-index: 1;">
                            <i class="fas fa-gem"></i>
                        </div>
                        <h3 style="font-size: 1.2rem; font-weight: bold; color: #1e293b; margin-bottom: 0.8rem;">Quality First</h3>
                        <p style="color: #64748b; line-height: 1.5; font-size: 0.9rem;">We carefully curate every product to ensure it meets our high standards for quality and performance, giving you confidence in every purchase.</p>
                    </div>

                    <div style="background: linear-gradient(135deg, #f8fafc, #ffffff); padding: 2rem 1.5rem; border-radius: 1.2rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08); text-align: center; border: 1px solid #e2e8f0; transition: all 0.3s; position: relative; overflow: hidden; flex: 1; min-width: 200px;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.08)'">
                        <div style="position: absolute; top: -50%; right: -50%; width: 80px; height: 80px; background: linear-gradient(45deg, #dcfce7, #bbf7d0); border-radius: 50%; opacity: 0.3;"></div>
                        <div style="background: linear-gradient(135deg, #dcfce7, #bbf7d0); color: #15803d; width: 4rem; height: 4rem; border-radius: 1.2rem; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; font-size: 1.5rem; position: relative; z-index: 1;">
                            <i class="fas fa-dollar-sign"></i>
                        </div>
                        <h3 style="font-size: 1.2rem; font-weight: bold; color: #1e293b; margin-bottom: 0.8rem;">Best Prices</h3>
                        <p style="color: #64748b; line-height: 1.5; font-size: 0.9rem;">We negotiate directly with manufacturers and leverage our partnerships to bring you the most competitive prices on the market.</p>
                    </div>

                    <div style="background: linear-gradient(135deg, #f8fafc, #ffffff); padding: 2rem 1.5rem; border-radius: 1.2rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08); text-align: center; border: 1px solid #e2e8f0; transition: all 0.3s; position: relative; overflow: hidden; flex: 1; min-width: 200px;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.08)'">
                        <div style="position: absolute; top: -50%; right: -50%; width: 80px; height: 80px; background: linear-gradient(45deg, #fef3c7, #fde68a); border-radius: 50%; opacity: 0.3;"></div>
                        <div style="background: linear-gradient(135deg, #fef3c7, #fde68a); color: #b45309; width: 4rem; height: 4rem; border-radius: 1.2rem; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; font-size: 1.5rem; position: relative; z-index: 1;">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                        <h3 style="font-size: 1.2rem; font-weight: bold; color: #1e293b; margin-bottom: 0.8rem;">Fast Shipping</h3>
                        <p style="color: #64748b; line-height: 1.5; font-size: 0.9rem;">Quick processing and reliable shipping partners ensure your orders arrive when you need them, with tracking every step of the way.</p>
                    </div>

                    <div style="background: linear-gradient(135deg, #f8fafc, #ffffff); padding: 2rem 1.5rem; border-radius: 1.2rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.08); text-align: center; border: 1px solid #e2e8f0; transition: all 0.3s; position: relative; overflow: hidden; flex: 1; min-width: 200px;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.08)'">
                        <div style="position: absolute; top: -50%; right: -50%; width: 80px; height: 80px; background: linear-gradient(45deg, #fce7f3, #fbcfe8); border-radius: 50%; opacity: 0.3;"></div>
                        <div style="background: linear-gradient(135deg, #fce7f3, #fbcfe8); color: #be185d; width: 4rem; height: 4rem; border-radius: 1.2rem; display: flex; align-items: center; justify-content: center; margin: 0 auto 1.5rem; font-size: 1.5rem; position: relative; z-index: 1;">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h3 style="font-size: 1.2rem; font-weight: bold; color: #1e293b; margin-bottom: 0.8rem;">Customer Care</h3>
                        <p style="color: #64748b; line-height: 1.5; font-size: 0.9rem;">Our dedicated support team is here to help you every step of the way, from purchase to delivery and beyond.</p>
                    </div>
                </div>
            </div>

            <!-- Leadership Team Section -->
            <div style="margin-bottom: 6rem;">
                <div style="text-align: center; margin-bottom: 4rem;">
                    <div style="display: inline-flex; align-items: center; padding: 0.5rem 1rem; background: #fce7f3; color: #be185d; border-radius: 2rem; font-size: 0.875rem; font-weight: 600; margin-bottom: 1.5rem;">
                        <i class="fas fa-users" style="margin-right: 0.5rem;"></i>
                        Our Team
                    </div>
                    <h2 style="font-size: 3rem; font-weight: bold; color: #1e293b; margin-bottom: 1rem;">Meet Our Leadership</h2>
                    <p style="color: #64748b; max-width: 600px; margin: 0 auto; font-size: 1.125rem;">The passionate professionals driving Deal4u's mission forward</p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 3rem; max-width: 1000px; margin: 0 auto;">
                    <!-- CEO -->
                    <div style="background: white; border-radius: 1.5rem; overflow: hidden; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); transition: all 0.3s;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.1)'">
                        <div style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); padding: 2rem; text-align: center; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: -50%; right: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px); background-size: 20px 20px; opacity: 0.5;"></div>
                            <div style="position: relative; z-index: 1;">
                                <div style="width: 5rem; height: 5rem; background: rgba(255, 255, 255, 0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; backdrop-filter: blur(10px);">
                                    <i class="fas fa-user-tie" style="font-size: 2rem; color: white;"></i>
                                </div>
                                <h4 style="color: white; font-size: 1.25rem; font-weight: bold; margin-bottom: 0.5rem;">Michael Schmidt</h4>
                                <p style="color: #bfdbfe; font-size: 0.875rem;">Chief Executive Officer</p>
                            </div>
                        </div>
                        <div style="padding: 2rem;">
                            <p style="color: #64748b; line-height: 1.6; margin-bottom: 1.5rem;">Leading Deal4u with over 10 years of experience in e-commerce and technology retail. Passionate about customer satisfaction and innovation.</p>
                            <div style="display: flex; gap: 1rem;">
                                <div style="flex: 1; text-align: center; padding: 1rem; background: #f8fafc; border-radius: 0.5rem;">
                                    <div style="font-weight: bold; color: #1e293b; margin-bottom: 0.25rem;">10+</div>
                                    <div style="color: #64748b; font-size: 0.75rem;">Years Experience</div>
                                </div>
                                <div style="flex: 1; text-align: center; padding: 1rem; background: #f8fafc; border-radius: 0.5rem;">
                                    <div style="font-weight: bold; color: #1e293b; margin-bottom: 0.25rem;">MBA</div>
                                    <div style="color: #64748b; font-size: 0.75rem;">Business Admin</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- CTO -->
                    <div style="background: white; border-radius: 1.5rem; overflow: hidden; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); transition: all 0.3s;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.1)'">
                        <div style="background: linear-gradient(135deg, #16a34a, #15803d); padding: 2rem; text-align: center; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: -50%; right: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px); background-size: 20px 20px; opacity: 0.5;"></div>
                            <div style="position: relative; z-index: 1;">
                                <div style="width: 5rem; height: 5rem; background: rgba(255, 255, 255, 0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; backdrop-filter: blur(10px);">
                                    <i class="fas fa-laptop-code" style="font-size: 2rem; color: white;"></i>
                                </div>
                                <h4 style="color: white; font-size: 1.25rem; font-weight: bold; margin-bottom: 0.5rem;">Sarah Johnson</h4>
                                <p style="color: #bbf7d0; font-size: 0.875rem;">Chief Technology Officer</p>
                            </div>
                        </div>
                        <div style="padding: 2rem;">
                            <p style="color: #64748b; line-height: 1.6; margin-bottom: 1.5rem;">Driving our technology vision with expertise in e-commerce platforms, security, and user experience. Ensuring our platform stays cutting-edge.</p>
                            <div style="display: flex; gap: 1rem;">
                                <div style="flex: 1; text-align: center; padding: 1rem; background: #f8fafc; border-radius: 0.5rem;">
                                    <div style="font-weight: bold; color: #1e293b; margin-bottom: 0.25rem;">8+</div>
                                    <div style="color: #64748b; font-size: 0.75rem;">Years Tech</div>
                                </div>
                                <div style="flex: 1; text-align: center; padding: 1rem; background: #f8fafc; border-radius: 0.5rem;">
                                    <div style="font-weight: bold; color: #1e293b; margin-bottom: 0.25rem;">MS</div>
                                    <div style="color: #64748b; font-size: 0.75rem;">Computer Science</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Head of Customer Success -->
                    <div style="background: white; border-radius: 1.5rem; overflow: hidden; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); transition: all 0.3s;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.1)'">
                        <div style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); padding: 2rem; text-align: center; position: relative; overflow: hidden;">
                            <div style="position: absolute; top: -50%; right: -50%; width: 200%; height: 200%; background: radial-gradient(circle, rgba(255,255,255,0.1) 1px, transparent 1px); background-size: 20px 20px; opacity: 0.5;"></div>
                            <div style="position: relative; z-index: 1;">
                                <div style="width: 5rem; height: 5rem; background: rgba(255, 255, 255, 0.2); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin: 0 auto 1rem; backdrop-filter: blur(10px);">
                                    <i class="fas fa-heart" style="font-size: 2rem; color: white;"></i>
                                </div>
                                <h4 style="color: white; font-size: 1.25rem; font-weight: bold; margin-bottom: 0.5rem;">David Chen</h4>
                                <p style="color: #ddd6fe; font-size: 0.875rem;">Head of Customer Success</p>
                            </div>
                        </div>
                        <div style="padding: 2rem;">
                            <p style="color: #64748b; line-height: 1.6; margin-bottom: 1.5rem;">Dedicated to ensuring every customer has an exceptional experience. Leading our support team with a focus on satisfaction and loyalty.</p>
                            <div style="display: flex; gap: 1rem;">
                                <div style="flex: 1; text-align: center; padding: 1rem; background: #f8fafc; border-radius: 0.5rem;">
                                    <div style="font-weight: bold; color: #1e293b; margin-bottom: 0.25rem;">98%</div>
                                    <div style="color: #64748b; font-size: 0.75rem;">Satisfaction Rate</div>
                                </div>
                                <div style="flex: 1; text-align: center; padding: 1rem; background: #f8fafc; border-radius: 0.5rem;">
                                    <div style="font-weight: bold; color: #1e293b; margin-bottom: 0.25rem;">24/7</div>
                                    <div style="color: #64748b; font-size: 0.75rem;">Support</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Why Choose Deal4u Section -->
            <div style="margin-bottom: 6rem; background: linear-gradient(135deg, #f8fafc, #e2e8f0); padding: 4rem 0; border-radius: 2rem;">
                <div style="text-align: center; margin-bottom: 4rem;">
                    <div style="display: inline-flex; align-items: center; padding: 0.5rem 1rem; background: #e0f2fe; color: #0369a1; border-radius: 2rem; font-size: 0.875rem; font-weight: 600; margin-bottom: 1.5rem;">
                        <i class="fas fa-star" style="margin-right: 0.5rem;"></i>
                        Why Choose Us
                    </div>
                    <h2 style="font-size: 3rem; font-weight: bold; color: #1e293b; margin-bottom: 1rem;">The Deal4u Advantage</h2>
                    <p style="color: #64748b; max-width: 600px; margin: 0 auto; font-size: 1.125rem;">We're more than just an online store - we're your technology partner committed to excellence</p>
                </div>

                <div style="display: flex; justify-content: center; gap: 1.5rem; max-width: 1200px; margin: 0 auto; flex-wrap: nowrap;">
                    <div style="background: white; padding: 2rem 1.5rem; border-radius: 1.2rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); transition: all 0.3s; border-top: 4px solid #3b82f6; flex: 1; min-width: 220px;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.1)'">
                        <div style="display: flex; align-items: center; margin-bottom: 1.2rem;">
                            <div style="width: 3rem; height: 3rem; background: linear-gradient(135deg, #dbeafe, #bfdbfe); color: #1d4ed8; border-radius: 0.8rem; display: flex; align-items: center; justify-content: center; margin-right: 0.8rem;">
                                <i class="fas fa-shield-alt" style="font-size: 1.2rem;"></i>
                            </div>
                            <h3 style="font-size: 1.1rem; font-weight: bold; color: #1e293b; margin: 0;">2-Year Warranty</h3>
                        </div>
                        <p style="color: #64748b; line-height: 1.5; margin-bottom: 0.8rem; font-size: 0.9rem;">Comprehensive warranty coverage on all electronics for your complete peace of mind and protection.</p>
                        <div style="display: flex; align-items: center; color: #3b82f6; font-size: 0.8rem; font-weight: 600;">
                            <i class="fas fa-check-circle" style="margin-right: 0.4rem;"></i>
                            Full Coverage Included
                        </div>
                    </div>

                    <div style="background: white; padding: 2rem 1.5rem; border-radius: 1.2rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); transition: all 0.3s; border-top: 4px solid #16a34a; flex: 1; min-width: 220px;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.1)'">
                        <div style="display: flex; align-items: center; margin-bottom: 1.2rem;">
                            <div style="width: 3rem; height: 3rem; background: linear-gradient(135deg, #dcfce7, #bbf7d0); color: #15803d; border-radius: 0.8rem; display: flex; align-items: center; justify-content: center; margin-right: 0.8rem;">
                                <i class="fas fa-undo-alt" style="font-size: 1.2rem;"></i>
                            </div>
                            <h3 style="font-size: 1.1rem; font-weight: bold; color: #1e293b; margin: 0;">30-Day Returns</h3>
                        </div>
                        <p style="color: #64748b; line-height: 1.5; margin-bottom: 0.8rem; font-size: 0.9rem;">Not satisfied? Return any item within 30 days for a full refund. No questions asked, hassle-free process.</p>
                        <div style="display: flex; align-items: center; color: #16a34a; font-size: 0.8rem; font-weight: 600;">
                            <i class="fas fa-check-circle" style="margin-right: 0.4rem;"></i>
                            Money-Back Guarantee
                        </div>
                    </div>

                    <div style="background: white; padding: 2rem 1.5rem; border-radius: 1.2rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); transition: all 0.3s; border-top: 4px solid #f59e0b; flex: 1; min-width: 220px;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.1)'">
                        <div style="display: flex; align-items: center; margin-bottom: 1.2rem;">
                            <div style="width: 3rem; height: 3rem; background: linear-gradient(135deg, #fef3c7, #fde68a); color: #b45309; border-radius: 0.8rem; display: flex; align-items: center; justify-content: center; margin-right: 0.8rem;">
                                <i class="fas fa-truck" style="font-size: 1.2rem;"></i>
                            </div>
                            <h3 style="font-size: 1.1rem; font-weight: bold; color: #1e293b; margin: 0;">Free Shipping</h3>
                        </div>
                        <p style="color: #64748b; line-height: 1.5; margin-bottom: 0.8rem; font-size: 0.9rem;">Free shipping on all orders over $50. No hidden fees or surprises, just transparent pricing.</p>
                        <div style="display: flex; align-items: center; color: #f59e0b; font-size: 0.8rem; font-weight: 600;">
                            <i class="fas fa-check-circle" style="margin-right: 0.4rem;"></i>
                            Fast & Reliable Delivery
                        </div>
                    </div>

                    <div style="background: white; padding: 2rem 1.5rem; border-radius: 1.2rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); transition: all 0.3s; border-top: 4px solid #8b5cf6; flex: 1; min-width: 220px;" onmouseover="this.style.transform='translateY(-5px)'; this.style.boxShadow='0 20px 40px rgba(0, 0, 0, 0.15)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 10px 25px rgba(0, 0, 0, 0.1)'">
                        <div style="display: flex; align-items: center; margin-bottom: 1.2rem;">
                            <div style="width: 3rem; height: 3rem; background: linear-gradient(135deg, #ede9fe, #ddd6fe); color: #7c3aed; border-radius: 0.8rem; display: flex; align-items: center; justify-content: center; margin-right: 0.8rem;">
                                <i class="fas fa-headset" style="font-size: 1.2rem;"></i>
                            </div>
                            <h3 style="font-size: 1.1rem; font-weight: bold; color: #1e293b; margin: 0;">24/7 Support</h3>
                        </div>
                        <p style="color: #64748b; line-height: 1.5; margin-bottom: 0.8rem; font-size: 0.9rem;">Round-the-clock customer support via chat, email, and phone. We're always here when you need us.</p>
                        <div style="display: flex; align-items: center; color: #8b5cf6; font-size: 0.8rem; font-weight: 600;">
                            <i class="fas fa-check-circle" style="margin-right: 0.4rem;"></i>
                            Always Available
                        </div>
                    </div>
                </div>
            </div>

            <!-- Customer Testimonials -->
            <div style="margin-bottom: 6rem;">
                <div style="text-align: center; margin-bottom: 4rem;">
                    <div style="display: inline-flex; align-items: center; padding: 0.5rem 1rem; background: #fef7cd; color: #a16207; border-radius: 2rem; font-size: 0.875rem; font-weight: 600; margin-bottom: 1.5rem;">
                        <i class="fas fa-quote-left" style="margin-right: 0.5rem;"></i>
                        Customer Stories
                    </div>
                    <h2 style="font-size: 3rem; font-weight: bold; color: #1e293b; margin-bottom: 1rem;">What Our Customers Say</h2>
                    <p style="color: #64748b; max-width: 600px; margin: 0 auto; font-size: 1.125rem;">Real feedback from real customers who trust Deal4u for their technology needs</p>
                </div>

                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(350px, 1fr)); gap: 2rem; max-width: 1200px; margin: 0 auto;">
                    <div style="background: white; padding: 2.5rem; border-radius: 1.5rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); position: relative;">
                        <div style="position: absolute; top: 1rem; right: 1rem; color: #fbbf24; font-size: 1.5rem;">
                            <i class="fas fa-quote-right"></i>
                        </div>
                        <div style="margin-bottom: 1.5rem;">
                            <div style="display: flex; color: #fbbf24; margin-bottom: 1rem;">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <p style="color: #374151; line-height: 1.6; font-style: italic;">"Absolutely fantastic service! My gaming laptop arrived exactly as described and the customer support was incredibly helpful throughout the process."</p>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <div style="width: 3rem; height: 3rem; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
                                <i class="fas fa-user" style="color: white;"></i>
                            </div>
                            <div>
                                <div style="font-weight: bold; color: #1e293b;">Alex Thompson</div>
                                <div style="color: #64748b; font-size: 0.875rem;">Gaming Enthusiast</div>
                            </div>
                        </div>
                    </div>

                    <div style="background: white; padding: 2.5rem; border-radius: 1.5rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); position: relative;">
                        <div style="position: absolute; top: 1rem; right: 1rem; color: #fbbf24; font-size: 1.5rem;">
                            <i class="fas fa-quote-right"></i>
                        </div>
                        <div style="margin-bottom: 1.5rem;">
                            <div style="display: flex; color: #fbbf24; margin-bottom: 1rem;">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <p style="color: #374151; line-height: 1.6; font-style: italic;">"Deal4u has become my go-to for all tech purchases. Great prices, fast shipping, and their warranty service is top-notch. Highly recommended!"</p>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <div style="width: 3rem; height: 3rem; background: linear-gradient(135deg, #16a34a, #15803d); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
                                <i class="fas fa-user" style="color: white;"></i>
                            </div>
                            <div>
                                <div style="font-weight: bold; color: #1e293b;">Maria Rodriguez</div>
                                <div style="color: #64748b; font-size: 0.875rem;">Small Business Owner</div>
                            </div>
                        </div>
                    </div>

                    <div style="background: white; padding: 2.5rem; border-radius: 1.5rem; box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1); position: relative;">
                        <div style="position: absolute; top: 1rem; right: 1rem; color: #fbbf24; font-size: 1.5rem;">
                            <i class="fas fa-quote-right"></i>
                        </div>
                        <div style="margin-bottom: 1.5rem;">
                            <div style="display: flex; color: #fbbf24; margin-bottom: 1rem;">
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                                <i class="fas fa-star"></i>
                            </div>
                            <p style="color: #374151; line-height: 1.6; font-style: italic;">"The customer service team went above and beyond to help me choose the right products for my home office setup. Professional and knowledgeable!"</p>
                        </div>
                        <div style="display: flex; align-items: center;">
                            <div style="width: 3rem; height: 3rem; background: linear-gradient(135deg, #8b5cf6, #7c3aed); border-radius: 50%; display: flex; align-items: center; justify-content: center; margin-right: 1rem;">
                                <i class="fas fa-user" style="color: white;"></i>
                            </div>
                            <div>
                                <div style="font-weight: bold; color: #1e293b;">James Wilson</div>
                                <div style="color: #64748b; font-size: 0.875rem;">Remote Worker</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Professional CTA Section -->
            <div style="background: linear-gradient(135deg, #1e293b 0%, #334155 50%, #475569 100%); color: white; padding: 4rem 3rem; border-radius: 2rem; text-align: center; position: relative; overflow: hidden; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.2);">
                <!-- Background Pattern -->
                <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.1; background-image: url('data:image/svg+xml,<svg width=\"60\" height=\"60\" viewBox=\"0 0 60 60\" xmlns=\"http://www.w3.org/2000/svg\"><g fill=\"none\" fill-rule=\"evenodd\"><g fill=\"%23ffffff\" fill-opacity=\"0.1\"><circle cx=\"30\" cy=\"30\" r=\"2\"/></g></g></svg>');"></div>

                <div style="position: relative; z-index: 1; max-width: 800px; margin: 0 auto;">
                    <!-- Icon -->
                    <div style="display: inline-flex; align-items: center; justify-content: center; width: 5rem; height: 5rem; background: rgba(255, 255, 255, 0.1); border-radius: 1.5rem; margin-bottom: 2rem; backdrop-filter: blur(10px);">
                        <i class="fas fa-rocket" style="font-size: 2rem; color: #60a5fa;"></i>
                    </div>

                    <h2 style="font-size: 3rem; font-weight: bold; margin-bottom: 1.5rem; line-height: 1.2; background: linear-gradient(135deg, #ffffff, #cbd5e1); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Ready to Start Shopping?</h2>

                    <p style="color: #cbd5e1; margin-bottom: 3rem; font-size: 1.25rem; line-height: 1.6; max-width: 600px; margin-left: auto; margin-right: auto;">Join thousands of satisfied customers and discover amazing deals on premium technology products today!</p>

                    <!-- Stats Row -->
                    <div style="display: flex; justify-content: center; gap: 1.5rem; margin-bottom: 3rem; max-width: 600px; margin-left: auto; margin-right: auto; flex-wrap: nowrap;">
                        <div style="text-align: center; flex: 1; min-width: 100px;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #60a5fa; margin-bottom: 0.25rem;">15K+</div>
                            <div style="color: #e2e8f0; font-size: 0.75rem;">Happy Customers</div>
                        </div>
                        <div style="text-align: center; flex: 1; min-width: 100px;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #34d399; margin-bottom: 0.25rem;">98%</div>
                            <div style="color: #e2e8f0; font-size: 0.75rem;">Satisfaction</div>
                        </div>
                        <div style="text-align: center; flex: 1; min-width: 100px;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #fbbf24; margin-bottom: 0.25rem;">5★</div>
                            <div style="color: #e2e8f0; font-size: 0.75rem;">Average Rating</div>
                        </div>
                        <div style="text-align: center; flex: 1; min-width: 100px;">
                            <div style="font-size: 1.5rem; font-weight: bold; color: #f472b6; margin-bottom: 0.25rem;">24/7</div>
                            <div style="color: #e2e8f0; font-size: 0.75rem;">Support</div>
                        </div>
                    </div>

                    <!-- CTA Buttons -->
                    <div style="display: flex; gap: 1.5rem; justify-content: center; flex-wrap: wrap;">
                        <a href="<?php echo function_exists('wc_get_page_permalink') ? esc_url(wc_get_page_permalink('shop')) : esc_url(home_url('/shop/')); ?>"
                           style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; font-weight: bold; padding: 1rem 2.5rem; border-radius: 0.75rem; text-decoration: none; display: inline-flex; align-items: center; transition: all 0.3s; box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3); font-size: 1.125rem;"
                           onmouseover="this.style.transform='scale(1.05)'; this.style.boxShadow='0 8px 20px rgba(59, 130, 246, 0.4)'"
                           onmouseout="this.style.transform='scale(1)'; this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.3)'">
                            <i class="fas fa-shopping-bag" style="margin-right: 0.75rem; font-size: 1.125rem;"></i>
                            Explore Products
                        </a>

                        <a href="<?php echo esc_url(home_url('/contact/')); ?>"
                           style="background: rgba(255, 255, 255, 0.1); color: white; font-weight: bold; padding: 1rem 2.5rem; border-radius: 0.75rem; text-decoration: none; display: inline-flex; align-items: center; transition: all 0.3s; border: 2px solid rgba(255, 255, 255, 0.3); backdrop-filter: blur(10px); font-size: 1.125rem;"
                           onmouseover="this.style.background='rgba(255, 255, 255, 0.2)'; this.style.borderColor='rgba(255, 255, 255, 0.5)'"
                           onmouseout="this.style.background='rgba(255, 255, 255, 0.1)'; this.style.borderColor='rgba(255, 255, 255, 0.3)'">
                            <i class="fas fa-envelope" style="margin-right: 0.75rem; font-size: 1.125rem;"></i>
                            Get In Touch
                        </a>
                    </div>

                    <!-- Trust Indicators -->
                    <div style="margin-top: 3rem; padding-top: 2rem; border-top: 1px solid rgba(255, 255, 255, 0.2);">
                        <div style="display: flex; justify-content: center; align-items: center; gap: 2rem; flex-wrap: wrap; color: #cbd5e1; font-size: 0.875rem;">
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-shield-alt" style="color: #60a5fa;"></i>
                                <span>2-Year Warranty</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-truck" style="color: #34d399;"></i>
                                <span>Free Shipping</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-undo-alt" style="color: #fbbf24;"></i>
                                <span>30-Day Returns</span>
                            </div>
                            <div style="display: flex; align-items: center; gap: 0.5rem;">
                                <i class="fas fa-headset" style="color: #f472b6;"></i>
                                <span>24/7 Support</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
</main>

<?php get_footer(); ?>
