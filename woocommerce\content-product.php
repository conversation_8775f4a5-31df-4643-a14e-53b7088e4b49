<?php
/**
 * The template for displaying product content within loops
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 9.4.0
 */

defined( 'ABSPATH' ) || exit;

global $product;

// Ensure visibility.
if ( empty( $product ) || ! $product->is_visible() ) {
	return;
}
?>
<div <?php wc_product_class( 'bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 hover:scale-[1.02] border border-gray-100 group flex flex-col min-h-[320px]', $product ); ?>>

    <?php
    /**
     * Hook: woocommerce_before_shop_loop_item.
     *
     * @hooked woocommerce_template_loop_product_link_open - 10
     */
    do_action( 'woocommerce_before_shop_loop_item' );
    ?>

    <!-- Product Image - COMPACT -->
    <div class="relative overflow-hidden h-40 bg-gradient-to-br from-gray-50 to-gray-100">
        <!-- Remove default WooCommerce sale flash to avoid duplicates -->
        <?php
        // Only show product thumbnail, not the sale flash
        if (function_exists('woocommerce_template_loop_product_thumbnail')) {
            woocommerce_template_loop_product_thumbnail();
        }
        ?>

        <!-- Custom badges (only our custom ones) -->
        <div class="absolute top-2 left-2 flex flex-col gap-1">
            <?php if ($product->is_on_sale()) : ?>
                <div class="bg-red-500 text-white px-2 py-1 rounded text-xs font-bold shadow-lg">
                    SALE
                </div>
            <?php endif; ?>

            <?php if ($product->is_featured()) : ?>
                <div class="bg-yellow-500 text-white px-2 py-1 rounded text-xs font-bold shadow-lg">
                    FEATURED
                </div>
            <?php endif; ?>
        </div>

        <div class="absolute top-2 right-2 flex flex-col gap-1">
            <?php if (!$product->is_in_stock()) : ?>
                <div class="bg-gray-500 text-white px-2 py-1 rounded text-xs font-bold shadow-lg">
                    OUT OF STOCK
                </div>
            <?php endif; ?>


        </div>
    </div>

    <!-- Product Info - COMPACT PADDING -->
    <div class="p-4 flex flex-col h-full flex-grow">
        <!-- Product Title - COMPACT -->
        <h3 class="text-sm font-semibold text-gray-800 mb-2 line-clamp-2 group-hover:text-blue-600 transition-colors duration-300">
            <a href="<?php echo esc_url(get_permalink()); ?>" class="hover:no-underline">
                <?php echo esc_html($product->get_name()); ?>
            </a>
        </h3>

        <!-- Product Rating -->
        <div class="mb-2">
            <?php
            $rating_count = $product->get_rating_count();
            $average_rating = $product->get_average_rating();

            if ($rating_count > 0) :
            ?>
                <div class="flex items-center gap-1">
                    <div class="flex items-center">
                        <?php for ($i = 1; $i <= 5; $i++) : ?>
                            <svg class="w-3 h-3 <?php echo $i <= $average_rating ? 'text-yellow-400' : 'text-gray-300'; ?>" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                            </svg>
                        <?php endfor; ?>
                    </div>
                    <span class="text-xs text-gray-600">(<?php echo esc_html($rating_count); ?>)</span>
                </div>
            <?php endif; ?>
        </div>

        <!-- Price and Add to Cart Section -->
        <div class="mt-auto">
            <!-- Price - COMPACT -->
            <div class="mb-3">
                <?php
                // Improved price display with fallbacks
                $price_html = $product->get_price_html();

                if (!empty($price_html)) {
                    if ($product->is_on_sale()) {
                        $regular_price = $product->get_regular_price();
                        $sale_price = $product->get_sale_price();

                        // Check if we have valid prices
                        if (!empty($sale_price) && !empty($regular_price)) {
                            echo '<div class="flex items-center gap-2">';
                            echo '<span class="text-sm font-bold text-red-600">' . wc_price($sale_price) . '</span>';
                            echo '<span class="text-xs text-gray-500 line-through">' . wc_price($regular_price) . '</span>';
                            echo '</div>';
                        } else {
                            // Fallback to default price HTML if custom logic fails
                            echo '<span class="text-sm font-bold text-gray-800">' . $price_html . '</span>';
                        }
                    } else {
                        echo '<span class="text-sm font-bold text-gray-800">' . $price_html . '</span>';
                    }
                } else {
                    // If no price is available, show a message or contact for price
                    echo '<span class="text-sm text-gray-600">Contact for price</span>';
                }
                ?>
            </div>

            <!-- Clean Single Action Button - COMPACT -->
            <div class="mt-auto">
                <!-- Clean Professional Button - COMPACT -->
                <a href="<?php echo esc_url(get_permalink()); ?>"
                   class="block w-full bg-gradient-to-r from-slate-800 to-slate-900 hover:from-slate-900 hover:to-black text-white font-medium py-2 px-4 rounded-lg transition-all duration-300 text-center shadow-md hover:shadow-lg transform hover:scale-[1.01] group">
                    <span class="flex items-center justify-center gap-1">
                        <span class="text-xs">View Details</span>
                        <svg class="w-3 h-3 transition-transform group-hover:translate-x-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 8l4 4m0 0l-4 4m4-4H3"></path>
                        </svg>
                    </span>
                </a>

                <!-- Stock Status Indicator -->
                <?php if (!$product->is_in_stock()) : ?>
                    <div class="mt-3 text-center">
                        <span class="inline-flex items-center px-3 py-1 rounded-full text-sm font-medium bg-red-100 text-red-800">
                            <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                            </svg>
                            Out of Stock
                        </span>
                    </div>
                <?php endif; ?>
            </div>
        </div>

        <!-- Product Meta -->
        <div class="mt-3 pt-3 border-t border-gray-100 flex justify-between items-center text-xs text-gray-500">
            <span>SKU: <?php echo $product->get_sku() ? $product->get_sku() : 'N/A'; ?></span>
            <?php if ($product->is_in_stock()) : ?>
                <span class="text-green-600 font-semibold">✓ In Stock</span>
            <?php else : ?>
                <span class="text-red-600 font-semibold">✗ Out of Stock</span>
            <?php endif; ?>
        </div>
    </div>
</div>


