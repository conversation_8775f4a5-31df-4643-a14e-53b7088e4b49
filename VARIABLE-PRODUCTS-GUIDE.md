# 🛍️ Variable Products Setup Guide

## What are Variable Products?

Variable products allow customers to choose different options (variations) for the same product, such as:
- **Size**: 2g, 4g, 6g, 8g
- **Color**: Red, Blue, Green, Black
- **Material**: Cotton, Polyester, Silk
- **Storage**: 16GB, 32GB, 64GB, 128GB

## 🚀 How to Create Variable Products

### Step 1: Create Product Attributes

1. **Go to WordPress Admin** → Products → Attributes
2. **Add New Attribute**:
   - **Name**: Size (or Color, Storage, etc.)
   - **Slug**: size
   - **Enable Archives**: ✅ (optional)
   - **Default sort order**: Custom ordering
3. **Click "Add attribute"**
4. **Configure Terms**:
   - Click "Configure terms" for your new attribute
   - Add terms like: 2g, 4g, 6g, 8g
   - Save each term

### Step 2: Create Variable Product

1. **Go to Products** → Add New
2. **Product Data** → Select "Variable product"
3. **Attributes Tab**:
   - Add your attribute (e.g., "Size")
   - Check "Used for variations"
   - Check "Visible on the product page"
   - Select values: 2g, 4g, 6g, 8g
   - Save attributes
4. **Variations Tab**:
   - Click "Create variations from all attributes"
   - This creates all possible combinations
   - Set price, stock, image for each variation
   - Save

### Step 3: Configure Each Variation

For each variation (2g, 4g, 6g, 8g):
- **Regular Price**: Set different prices
- **Sale Price**: Optional discount prices
- **Stock**: Set individual stock quantities
- **Image**: Upload specific images for each variation
- **Weight/Dimensions**: Set if different
- **Description**: Add variation-specific details

## 🎨 How It Works on Your Site

### Shop Page
- **Variable products** show "SELECT OPTIONS" button (purple)
- **Simple products** show "ADD TO CART" button (orange)
- Customers click "SELECT OPTIONS" to go to product page

### Product Page
- **Variation selectors** appear as styled buttons
- **Dynamic price updates** when options are selected
- **Stock status** updates per variation
- **Add to cart** enabled only when all options selected

## 📋 Example Product Setup

### Product: "Gaming Headset"
**Attributes:**
- **Color**: Black, White, Red
- **Connection**: Wired, Wireless

**Variations:**
1. Black + Wired = $49.99
2. Black + Wireless = $79.99
3. White + Wired = $54.99
4. White + Wireless = $84.99
5. Red + Wired = $59.99
6. Red + Wireless = $89.99

## 🔧 Technical Features Added

### Frontend Features:
- ✅ **Variation selection buttons** with hover effects
- ✅ **Dynamic price display** updates in real-time
- ✅ **Stock status** shows per variation
- ✅ **Smart add to cart** - disabled until all options selected
- ✅ **Visual feedback** - selected options highlighted
- ✅ **Mobile responsive** variation selectors

### Backend Features:
- ✅ **AJAX add to cart** for variable products
- ✅ **Proper cart fragments** update
- ✅ **Variation data handling** in cart
- ✅ **Error handling** for invalid selections

## 🎯 Customer Experience

1. **Browse shop** → See "SELECT OPTIONS" for variable products
2. **Click product** → Go to product page
3. **Choose options** → Select size, color, etc.
4. **See price update** → Price changes based on selection
5. **Add to cart** → Button enables when all options selected
6. **Cart shows** → Specific variation with chosen options

## 💡 Pro Tips

### For Better UX:
- **Use clear attribute names**: "Size" not "pa_size"
- **Logical ordering**: Small to Large, Light to Dark
- **Consistent pricing**: Similar markup across variations
- **Good images**: Show actual variation differences
- **Stock management**: Keep accurate inventory per variation

### For SEO:
- **Unique descriptions** for each variation
- **Proper meta data** for variable products
- **Structured data** for product variations
- **Clear URLs** for variation permalinks

## 🛠️ Troubleshooting

### Common Issues:
1. **"Select Options" not showing**: Check product type is "Variable"
2. **No variations created**: Ensure attributes are "Used for variations"
3. **Price not updating**: Check JavaScript console for errors
4. **Add to cart disabled**: Ensure all required attributes selected

### Debug Mode:
Add `?debug_prices=1` to any page URL (as admin) to see product price debug info.

## 📞 Need Help?

Your variable products system is now fully functional! Customers can:
- ✅ Choose product variations (size, color, etc.)
- ✅ See dynamic price updates
- ✅ Add specific variations to cart
- ✅ Enjoy smooth shopping experience

The system automatically handles all the technical complexity while providing an intuitive interface for your customers.
