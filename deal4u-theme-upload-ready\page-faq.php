<?php
/**
 * Template Name: FAQ Page
 */

get_header(); ?>

<main class="main-content">
    <!-- FAQ Header -->
    <section style="padding: 4rem 0; background: linear-gradient(135deg, #1e293b, #334155, #475569); color: white; position: relative; overflow: hidden;">
        <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0); background-size: 20px 20px; opacity: 0.3;"></div>
        <div class="container" style="position: relative; z-index: 1;">
            <div style="text-align: center; max-width: 800px; margin: 0 auto;">
                <div style="display: inline-flex; align-items: center; padding: 0.75rem 1.5rem; background: rgba(59, 130, 246, 0.2); color: #60a5fa; border-radius: 2rem; font-size: 0.875rem; font-weight: 600; margin-bottom: 2rem; backdrop-filter: blur(10px); border: 1px solid rgba(59, 130, 246, 0.3);">
                    <i class="fas fa-question-circle" style="margin-right: 0.5rem;"></i>
                    Help Center
                </div>
                <h1 style="font-size: 3.5rem; font-weight: bold; margin-bottom: 1.5rem; background: linear-gradient(135deg, #ffffff, #cbd5e1); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;">Frequently Asked Questions</h1>
                <p style="font-size: 1.25rem; color: #cbd5e1; line-height: 1.6;">Find quick answers to common questions about our products, services, and policies. Our comprehensive FAQ covers everything you need to know.</p>
            </div>
        </div>
    </section>

    <!-- FAQ Content -->
    <section style="padding: 5rem 0; background: linear-gradient(135deg, #f8fafc, #e2e8f0);">
        <div class="container">
            <div style="max-width: 1200px; margin: 0 auto;">

                <!-- Search FAQ -->
                <div style="background: white; padding: 3rem; border-radius: 1.5rem; box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1); margin-bottom: 4rem; text-align: center; border: 1px solid #e2e8f0; position: relative; overflow: hidden;">
                    <div style="position: absolute; top: -50%; right: -50%; width: 200px; height: 200px; background: linear-gradient(45deg, #dbeafe, #bfdbfe); border-radius: 50%; opacity: 0.1;"></div>
                    <div style="position: relative; z-index: 1;">
                        <div style="display: inline-flex; align-items: center; justify-content: center; width: 4rem; height: 4rem; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 1rem; margin-bottom: 1.5rem;">
                            <i class="fas fa-search" style="color: white; font-size: 1.25rem;"></i>
                        </div>
                        <h2 style="font-size: 2rem; font-weight: bold; color: #1e293b; margin-bottom: 1rem;">Search Our FAQ</h2>
                        <p style="color: #64748b; margin-bottom: 2rem; font-size: 1rem;">Type your question below to find instant answers</p>
                        <div style="position: relative; max-width: 500px; margin: 0 auto;">
                            <input type="text" id="faqSearch" placeholder="What can we help you with?" style="width: 100%; padding: 1rem 1.5rem 1rem 3.5rem; border: 2px solid #e2e8f0; border-radius: 1rem; font-size: 1rem; transition: all 0.3s; background: #f8fafc;" onfocus="this.style.borderColor='#3b82f6'; this.style.background='white'; this.style.boxShadow='0 0 0 3px rgba(59, 130, 246, 0.1)'" onblur="this.style.borderColor='#e2e8f0'; this.style.background='#f8fafc'; this.style.boxShadow='none'" onkeyup="searchFAQ()">
                            <i class="fas fa-search" style="position: absolute; left: 1.25rem; top: 50%; transform: translateY(-50%); color: #9ca3af; font-size: 1.1rem;"></i>
                        </div>
                    </div>
                </div>

                <!-- FAQ Categories -->
                <div style="max-width: 1200px; margin: 0 auto; display: flex; flex-direction: column; gap: 1.5rem;">

                    <!-- Orders & Shipping -->
                    <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);">
                        <div style="background: linear-gradient(135deg, #f8fafc, #f1f5f9); padding: 1.75rem 2rem; border-bottom: 1px solid #e5e7eb;">
                            <div style="display: flex; align-items: center;">
                                <div style="width: 44px; height: 44px; background: linear-gradient(135deg, #dbeafe, #bfdbfe); border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 1rem; box-shadow: 0 2px 4px rgba(37, 99, 235, 0.1);">
                                    <i class="fas fa-shipping-fast" style="color: #2563eb; font-size: 18px;"></i>
                                </div>
                                <div>
                                    <h3 style="font-size: 1.125rem; font-weight: 600; color: #111827; margin: 0; letter-spacing: -0.025em;">Orders & Shipping</h3>
                                    <p style="font-size: 0.875rem; color: #6b7280; margin: 0.25rem 0 0 0;">Everything about your orders and delivery</p>
                                </div>
                            </div>
                        </div>
                        <div style="padding: 0;">
                            <div style="border-bottom: 1px solid #f3f4f6;">
                                <div style="padding: 1.5rem 2rem; cursor: pointer; transition: all 0.15s ease; position: relative;" onclick="toggleFAQ(this)" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <span style="font-size: 0.95rem; font-weight: 500; color: #374151;">How long does shipping take?</span>
                                        <i class="fas fa-chevron-down" style="color: #9ca3af; font-size: 12px; transition: transform 0.2s ease;"></i>
                                    </div>
                                </div>
                                <div style="display: none; padding: 0 2rem 1.5rem 2rem;">
                                    <div style="background: #f8fafc; padding: 1.25rem; border-radius: 8px; border-left: 4px solid #2563eb;">
                                        <div style="color: #374151; font-size: 0.9rem; line-height: 1.6;">
                                            <div style="margin-bottom: 0.75rem;"><strong style="color: #111827;">Standard Shipping:</strong> 3-5 business days</div>
                                            <div style="margin-bottom: 0.75rem;"><strong style="color: #111827;">Express Shipping:</strong> 1-2 business days (additional fee)</div>
                                            <div><strong style="color: #111827;">Free Shipping:</strong> Available on orders over £50</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="border-bottom: 1px solid #f3f4f6;">
                                <div style="padding: 1.5rem 2rem; cursor: pointer; transition: all 0.15s ease;" onclick="toggleFAQ(this)" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <span style="font-size: 0.95rem; font-weight: 500; color: #374151;">Can I track my order?</span>
                                        <i class="fas fa-chevron-down" style="color: #9ca3af; font-size: 12px; transition: transform 0.2s ease;"></i>
                                    </div>
                                </div>
                                <div style="display: none; padding: 0 2rem 1.5rem 2rem;">
                                    <div style="background: #f8fafc; padding: 1.25rem; border-radius: 8px; border-left: 4px solid #2563eb;">
                                        <div style="color: #374151; font-size: 0.9rem; line-height: 1.6;">
                                            Yes! Once your order ships, you'll receive a tracking number via email and SMS. You can also track your order anytime on our <a href="<?php echo home_url('/track-order/'); ?>" style="color: #2563eb; text-decoration: none; font-weight: 500; border-bottom: 1px solid transparent; transition: border-color 0.2s;" onmouseover="this.style.borderBottomColor='#2563eb'" onmouseout="this.style.borderBottomColor='transparent'">Track Order page</a>.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div style="padding: 1.5rem 2rem; cursor: pointer; transition: all 0.15s ease;" onclick="toggleFAQ(this)" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <span style="font-size: 0.95rem; font-weight: 500; color: #374151;">Do you ship internationally?</span>
                                        <i class="fas fa-chevron-down" style="color: #9ca3af; font-size: 12px; transition: transform 0.2s ease;"></i>
                                    </div>
                                </div>
                                <div style="display: none; padding: 0 2rem 1.5rem 2rem;">
                                    <div style="background: #f8fafc; padding: 1.25rem; border-radius: 8px; border-left: 4px solid #2563eb;">
                                        <div style="color: #374151; font-size: 0.9rem; line-height: 1.6;">
                                            Yes, we ship worldwide to over 200 countries. International shipping rates and delivery times vary by location. Customs duties may apply depending on your country's regulations.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Returns & Refunds -->
                    <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);">
                        <div style="background: linear-gradient(135deg, #f0fdf4, #ecfdf5); padding: 1.75rem 2rem; border-bottom: 1px solid #e5e7eb;">
                            <div style="display: flex; align-items: center;">
                                <div style="width: 44px; height: 44px; background: linear-gradient(135deg, #dcfce7, #bbf7d0); border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 1rem; box-shadow: 0 2px 4px rgba(34, 197, 94, 0.1);">
                                    <i class="fas fa-undo-alt" style="color: #16a34a; font-size: 18px;"></i>
                                </div>
                                <div>
                                    <h3 style="font-size: 1.125rem; font-weight: 600; color: #111827; margin: 0; letter-spacing: -0.025em;">Returns & Refunds</h3>
                                    <p style="font-size: 0.875rem; color: #6b7280; margin: 0.25rem 0 0 0;">Hassle-free returns and quick refunds</p>
                                </div>
                            </div>
                        </div>
                        <div style="padding: 0;">
                            <div style="border-bottom: 1px solid #f3f4f6;">
                                <div style="padding: 1.5rem 2rem; cursor: pointer; transition: all 0.15s ease;" onclick="toggleFAQ(this)" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <span style="font-size: 0.95rem; font-weight: 500; color: #374151;">What is your return policy?</span>
                                        <i class="fas fa-chevron-down" style="color: #9ca3af; font-size: 12px; transition: transform 0.2s ease;"></i>
                                    </div>
                                </div>
                                <div style="display: none; padding: 0 2rem 1.5rem 2rem;">
                                    <div style="background: #f0fdf4; padding: 1.25rem; border-radius: 8px; border-left: 4px solid #16a34a;">
                                        <div style="color: #374151; font-size: 0.9rem; line-height: 1.6;">
                                            We offer a <strong style="color: #111827;">30-day hassle-free return policy</strong> for most items. Products must be in original condition with all packaging, accessories, and documentation. Some restrictions apply to software and personalized items.
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="border-bottom: 1px solid #f3f4f6;">
                                <div style="padding: 1.5rem 2rem; cursor: pointer; transition: all 0.15s ease;" onclick="toggleFAQ(this)" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <span style="font-size: 0.95rem; font-weight: 500; color: #374151;">How do I return an item?</span>
                                        <i class="fas fa-chevron-down" style="color: #9ca3af; font-size: 12px; transition: transform 0.2s ease;"></i>
                                    </div>
                                </div>
                                <div style="display: none; padding: 0 2rem 1.5rem 2rem;">
                                    <div style="background: #f0fdf4; padding: 1.25rem; border-radius: 8px; border-left: 4px solid #16a34a;">
                                        <div style="color: #374151; font-size: 0.9rem; line-height: 1.6;">
                                            <div style="margin-bottom: 0.5rem;"><strong style="color: #111827;">Easy 3-step process:</strong></div>
                                            <div style="margin-bottom: 0.5rem;">1. <a href="<?php echo home_url('/contact/'); ?>" style="color: #16a34a; text-decoration: none; font-weight: 500;">Contact our support team</a> with your order number</div>
                                            <div style="margin-bottom: 0.5rem;">2. We'll email you a prepaid return label and instructions</div>
                                            <div>3. Pack the item securely and drop it off at any authorized location</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div style="padding: 1.5rem 2rem; cursor: pointer; transition: all 0.15s ease;" onclick="toggleFAQ(this)" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <span style="font-size: 0.95rem; font-weight: 500; color: #374151;">When will I receive my refund?</span>
                                        <i class="fas fa-chevron-down" style="color: #9ca3af; font-size: 12px; transition: transform 0.2s ease;"></i>
                                    </div>
                                </div>
                                <div style="display: none; padding: 0 2rem 1.5rem 2rem;">
                                    <div style="background: #f0fdf4; padding: 1.25rem; border-radius: 8px; border-left: 4px solid #16a34a;">
                                        <div style="color: #374151; font-size: 0.9rem; line-height: 1.6;">
                                            <div style="margin-bottom: 0.75rem;"><strong style="color: #111827;">Fast refund processing:</strong></div>
                                            <div style="margin-bottom: 0.5rem;">• Refunds processed within <strong style="color: #111827;">24-48 hours</strong> of receiving your return</div>
                                            <div style="margin-bottom: 0.5rem;">• Funds appear on your original payment method within 3-5 business days</div>
                                            <div>• You'll receive email confirmation once the refund is processed</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Payment & Security -->
                    <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);">
                        <div style="background: linear-gradient(135deg, #fffbeb, #fef3c7); padding: 1.75rem 2rem; border-bottom: 1px solid #e5e7eb;">
                            <div style="display: flex; align-items: center;">
                                <div style="width: 44px; height: 44px; background: linear-gradient(135deg, #fef3c7, #fde68a); border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 1rem; box-shadow: 0 2px 4px rgba(245, 158, 11, 0.1);">
                                    <i class="fas fa-shield-alt" style="color: #d97706; font-size: 18px;"></i>
                                </div>
                                <div>
                                    <h3 style="font-size: 1.125rem; font-weight: 600; color: #111827; margin: 0; letter-spacing: -0.025em;">Payment & Security</h3>
                                    <p style="font-size: 0.875rem; color: #6b7280; margin: 0.25rem 0 0 0;">Secure payments and data protection</p>
                                </div>
                            </div>
                        </div>
                        <div style="padding: 0;">
                            <div style="border-bottom: 1px solid #f3f4f6;">
                                <div style="padding: 1.5rem 2rem; cursor: pointer; transition: all 0.15s ease;" onclick="toggleFAQ(this)" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <span style="font-size: 0.95rem; font-weight: 500; color: #374151;">What payment methods do you accept?</span>
                                        <i class="fas fa-chevron-down" style="color: #9ca3af; font-size: 12px; transition: transform 0.2s ease;"></i>
                                    </div>
                                </div>
                                <div style="display: none; padding: 0 2rem 1.5rem 2rem;">
                                    <div style="background: #fffbeb; padding: 1.25rem; border-radius: 8px; border-left: 4px solid #d97706;">
                                        <div style="color: #374151; font-size: 0.9rem; line-height: 1.6;">
                                            <div style="margin-bottom: 0.75rem;"><strong style="color: #111827;">We accept all major payment methods:</strong></div>
                                            <div style="margin-bottom: 0.5rem;">• <strong style="color: #111827;">Credit Cards:</strong> Visa, MasterCard, American Express</div>
                                            <div style="margin-bottom: 0.5rem;">• <strong style="color: #111827;">Digital Wallets:</strong> PayPal, Apple Pay, Google Pay</div>
                                            <div>• <strong style="color: #111827;">Buy Now, Pay Later:</strong> Klarna, Afterpay</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="border-bottom: 1px solid #f3f4f6;">
                                <div style="padding: 1.5rem 2rem; cursor: pointer; transition: all 0.15s ease;" onclick="toggleFAQ(this)" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <span style="font-size: 0.95rem; font-weight: 500; color: #374151;">Is my payment information secure?</span>
                                        <i class="fas fa-chevron-down" style="color: #9ca3af; font-size: 12px; transition: transform 0.2s ease;"></i>
                                    </div>
                                </div>
                                <div style="display: none; padding: 0 2rem 1.5rem 2rem;">
                                    <div style="background: #fffbeb; padding: 1.25rem; border-radius: 8px; border-left: 4px solid #d97706;">
                                        <div style="color: #374151; font-size: 0.9rem; line-height: 1.6;">
                                            <div style="margin-bottom: 0.75rem;"><strong style="color: #111827;">Your security is our top priority:</strong></div>
                                            <div style="margin-bottom: 0.5rem;">• <strong style="color: #111827;">PCI DSS Compliant:</strong> Industry-standard security protocols</div>
                                            <div style="margin-bottom: 0.5rem;">• <strong style="color: #111827;">SSL Encryption:</strong> All data transmitted securely</div>
                                            <div>• <strong style="color: #111827;">No Storage:</strong> We never store your credit card details</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div style="padding: 1.5rem 2rem; cursor: pointer; transition: all 0.15s ease;" onclick="toggleFAQ(this)" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <span style="font-size: 0.95rem; font-weight: 500; color: #374151;">Can I save my payment information?</span>
                                        <i class="fas fa-chevron-down" style="color: #9ca3af; font-size: 12px; transition: transform 0.2s ease;"></i>
                                    </div>
                                </div>
                                <div style="display: none; padding: 0 2rem 1.5rem 2rem;">
                                    <div style="background: #fffbeb; padding: 1.25rem; border-radius: 8px; border-left: 4px solid #d97706;">
                                        <div style="color: #374151; font-size: 0.9rem; line-height: 1.6;">
                                            Yes! You can securely save multiple payment methods to your account for faster checkout. All saved information is encrypted and tokenized for maximum security. You can manage, add, or remove payment methods anytime in your account settings.
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Products & Warranty -->
                    <div style="background: #ffffff; border: 1px solid #e5e7eb; border-radius: 12px; overflow: hidden; box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);">
                        <div style="background: linear-gradient(135deg, #faf5ff, #f3e8ff); padding: 1.75rem 2rem; border-bottom: 1px solid #e5e7eb;">
                            <div style="display: flex; align-items: center;">
                                <div style="width: 44px; height: 44px; background: linear-gradient(135deg, #e9d5ff, #d8b4fe); border-radius: 10px; display: flex; align-items: center; justify-content: center; margin-right: 1rem; box-shadow: 0 2px 4px rgba(139, 92, 246, 0.1);">
                                    <i class="fas fa-certificate" style="color: #7c3aed; font-size: 18px;"></i>
                                </div>
                                <div>
                                    <h3 style="font-size: 1.125rem; font-weight: 600; color: #111827; margin: 0; letter-spacing: -0.025em;">Products & Warranty</h3>
                                    <p style="font-size: 0.875rem; color: #6b7280; margin: 0.25rem 0 0 0;">Authentic products with comprehensive coverage</p>
                                </div>
                            </div>
                        </div>
                        <div style="padding: 0;">
                            <div style="border-bottom: 1px solid #f3f4f6;">
                                <div style="padding: 1.5rem 2rem; cursor: pointer; transition: all 0.15s ease;" onclick="toggleFAQ(this)" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <span style="font-size: 0.95rem; font-weight: 500; color: #374151;">Are your products authentic?</span>
                                        <i class="fas fa-chevron-down" style="color: #9ca3af; font-size: 12px; transition: transform 0.2s ease;"></i>
                                    </div>
                                </div>
                                <div style="display: none; padding: 0 2rem 1.5rem 2rem;">
                                    <div style="background: #faf5ff; padding: 1.25rem; border-radius: 8px; border-left: 4px solid #7c3aed;">
                                        <div style="color: #374151; font-size: 0.9rem; line-height: 1.6;">
                                            <div style="margin-bottom: 0.75rem;"><strong style="color: #111827;">100% Authentic Guarantee:</strong></div>
                                            <div style="margin-bottom: 0.5rem;">• All products sourced directly from manufacturers</div>
                                            <div style="margin-bottom: 0.5rem;">• Authorized distributor partnerships only</div>
                                            <div>• <strong style="color: #111827;">Money-back guarantee</strong> if any product is found to be counterfeit</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div style="border-bottom: 1px solid #f3f4f6;">
                                <div style="padding: 1.5rem 2rem; cursor: pointer; transition: all 0.15s ease;" onclick="toggleFAQ(this)" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <span style="font-size: 0.95rem; font-weight: 500; color: #374151;">What warranty do you offer?</span>
                                        <i class="fas fa-chevron-down" style="color: #9ca3af; font-size: 12px; transition: transform 0.2s ease;"></i>
                                    </div>
                                </div>
                                <div style="display: none; padding: 0 2rem 1.5rem 2rem;">
                                    <div style="background: #faf5ff; padding: 1.25rem; border-radius: 8px; border-left: 4px solid #7c3aed;">
                                        <div style="color: #374151; font-size: 0.9rem; line-height: 1.6;">
                                            <div style="margin-bottom: 0.75rem;"><strong style="color: #111827;">Comprehensive Warranty Coverage:</strong></div>
                                            <div style="margin-bottom: 0.5rem;">• <strong style="color: #111827;">Electronics:</strong> 2-year manufacturer warranty</div>
                                            <div style="margin-bottom: 0.5rem;">• <strong style="color: #111827;">Accessories:</strong> 1-year warranty</div>
                                            <div>• <strong style="color: #111827;">Free repairs</strong> for manufacturing defects</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div>
                                <div style="padding: 1.5rem 2rem; cursor: pointer; transition: all 0.15s ease;" onclick="toggleFAQ(this)" onmouseover="this.style.backgroundColor='#f9fafb'" onmouseout="this.style.backgroundColor='transparent'">
                                    <div style="display: flex; align-items: center; justify-content: space-between;">
                                        <span style="font-size: 0.95rem; font-weight: 500; color: #374151;">Do you offer technical support?</span>
                                        <i class="fas fa-chevron-down" style="color: #9ca3af; font-size: 12px; transition: transform 0.2s ease;"></i>
                                    </div>
                                </div>
                                <div style="display: none; padding: 0 2rem 1.5rem 2rem;">
                                    <div style="background: #faf5ff; padding: 1.25rem; border-radius: 8px; border-left: 4px solid #7c3aed;">
                                        <div style="color: #374151; font-size: 0.9rem; line-height: 1.6;">
                                            <div style="margin-bottom: 0.75rem;"><strong style="color: #111827;">24/7 Technical Support Available:</strong></div>
                                            <div style="margin-bottom: 0.5rem;">• Product setup and installation guidance</div>
                                            <div style="margin-bottom: 0.5rem;">• Troubleshooting and problem resolution</div>
                                            <div>• Live chat, phone, and email support</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                </div>

                <!-- Still Need Help -->
                <div style="background: white; padding: 3rem; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); margin-top: 3rem; text-align: center; border: 1px solid #e2e8f0;">
                    <div style="display: inline-flex; align-items: center; justify-content: center; width: 4rem; height: 4rem; background: linear-gradient(135deg, #3b82f6, #1d4ed8); border-radius: 1rem; margin-bottom: 1.5rem;">
                        <i class="fas fa-headset" style="color: white; font-size: 1.5rem;"></i>
                    </div>
                    <h2 style="font-size: 2rem; font-weight: bold; color: #1e293b; margin-bottom: 1rem;">Still Need Help?</h2>
                    <p style="font-size: 1.125rem; color: #64748b; margin-bottom: 2rem; max-width: 500px; margin-left: auto; margin-right: auto;">
                        Can't find what you're looking for? Our expert support team is available 24/7 to assist you.
                    </p>
                    <div style="display: flex; flex-wrap: wrap; gap: 1rem; justify-content: center;">
                        <a href="<?php echo esc_url(home_url('/contact/')); ?>"
                           style="background: linear-gradient(135deg, #3b82f6, #1d4ed8); color: white; font-weight: 600; padding: 1rem 2rem; border-radius: 0.75rem; text-decoration: none; display: inline-flex; align-items: center; transition: all 0.3s; box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);"
                           onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 20px rgba(59, 130, 246, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(59, 130, 246, 0.3)'">
                            <i class="fas fa-envelope" style="margin-right: 0.75rem;"></i>Contact Support
                        </a>
                        <a href="https://wa.me/447447186806?text=Hello%20Deal4u!%20I%20have%20a%20question" target="_blank"
                           style="background: #22c55e; color: white; font-weight: 600; padding: 1rem 2rem; border-radius: 0.75rem; text-decoration: none; display: inline-flex; align-items: center; transition: all 0.3s; box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);"
                           onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 20px rgba(34, 197, 94, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 12px rgba(34, 197, 94, 0.3)'">
                            <i class="fab fa-whatsapp" style="margin-right: 0.75rem;"></i>WhatsApp Chat
                        </a>
                    </div>
                </div>

            </div>
        </div>
    </section>
</main>

<script>
function toggleFAQ(element) {
    const content = element.nextElementSibling;
    const icon = element.querySelector('i');

    if (content.style.display === 'none' || content.style.display === '') {
        content.style.display = 'block';
        icon.style.transform = 'rotate(180deg)';
        element.style.backgroundColor = '#f3f4f6';
    } else {
        content.style.display = 'none';
        icon.style.transform = 'rotate(0deg)';
        element.style.backgroundColor = 'transparent';
    }
}

function searchFAQ() {
    const searchTerm = document.getElementById('faqSearch').value.toLowerCase();
    const faqCategories = document.querySelectorAll('[style*="background: white"][style*="border-radius: 1rem"]');

    faqCategories.forEach(category => {
        if (!category.querySelector('h4')) return; // Skip non-FAQ cards

        const questions = category.querySelectorAll('h4');
        let hasVisibleQuestion = false;

        questions.forEach(question => {
            const questionText = question.textContent.toLowerCase();
            const answerDiv = question.nextElementSibling;
            const answerText = answerDiv ? answerDiv.textContent.toLowerCase() : '';
            const questionContainer = question.parentElement;

            if (!searchTerm || questionText.includes(searchTerm) || answerText.includes(searchTerm)) {
                questionContainer.style.display = 'block';
                hasVisibleQuestion = true;
            } else {
                questionContainer.style.display = 'none';
            }
        });

        // Show/hide entire category based on whether it has visible questions
        category.style.display = hasVisibleQuestion ? 'block' : 'none';
    });
}
</script>

<?php get_footer(); ?>
