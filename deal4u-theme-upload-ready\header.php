<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php wp_title('|', true, 'right'); ?><?php bloginfo('name'); ?></title>

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,500,600&display=swap" rel="stylesheet" />

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- FIXED: Re-enable Tailwind CSS (templates depend on it) -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    fontFamily: {
                        'sans': ['Figtree', 'system-ui', 'sans-serif'],
                    },
                    animation: {
                        'bounce-slow': 'bounce 3s ease-in-out infinite',
                        'float': 'float 6s ease-in-out infinite',
                        'float-delayed': 'float-delayed 6s ease-in-out infinite 2s',
                    },
                    keyframes: {
                        float: {
                            '0%, 100%': { transform: 'translateY(0px) rotate(12deg)' },
                            '50%': { transform: 'translateY(-20px) rotate(12deg)' }
                        },
                        'float-delayed': {
                            '0%, 100%': { transform: 'translateY(0px) rotate(-6deg)' },
                            '50%': { transform: 'translateY(-15px) rotate(-6deg)' }
                        }
                    }
                }
            }
        }
    </script>

    <!-- Alpine.js -->
    <script defer src="https://unpkg.com/alpinejs@3.x.x/dist/cdn.min.js"></script>

    <?php wp_head(); ?>
</head>

<body <?php body_class('font-sans antialiased bg-gray-50'); ?>>
    <?php wp_body_open(); ?>

    <header x-data="headerData()" class="sticky top-0 z-50 transition-all duration-300" :class="isScrolled ? 'bg-white shadow-lg' : 'bg-white'">
        <!-- Top Banner -->
        <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white py-2 px-4 text-center text-sm">
            <div class="container mx-auto flex items-center justify-center gap-2">
                <span class="animate-pulse">🔥</span>
                <span class="font-semibold">FREE SHIPPING ON ORDERS OVER $50 | USE CODE: FREESHIP</span>
                <span class="animate-pulse">🔥</span>
            </div>
        </div>

        <!-- Main Header -->
        <div class="bg-white border-b border-gray-200">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex items-center justify-between h-16">

                    <!-- Logo -->
                    <div class="flex-shrink-0">
                        <a href="<?php echo esc_url(home_url('/')); ?>" class="flex items-center space-x-2">
                            <div class="bg-gradient-to-r from-blue-600 to-purple-600 text-white px-3 py-2 rounded-lg font-bold text-xl hover:from-blue-700 hover:to-purple-700 transition-colors">
                                Deal4u
                            </div>
                        </a>
                    </div>

                    <!-- Desktop Navigation -->
                    <nav class="hidden md:flex space-x-8">
                        <a href="<?php echo esc_url(home_url('/')); ?>" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors <?php echo is_home() || is_front_page() ? 'text-blue-600 border-b-2 border-blue-600' : ''; ?>">
                            Home
                        </a>
                        <a href="<?php echo function_exists('wc_get_page_permalink') ? esc_url(wc_get_page_permalink('shop')) : esc_url(home_url('/shop/')); ?>" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors <?php echo is_shop() || is_product_category() || is_product() ? 'text-blue-600 border-b-2 border-blue-600' : ''; ?>">
                            Shop
                        </a>
                        <a href="<?php echo esc_url(home_url('/categories/')); ?>" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors <?php echo is_page('categories') ? 'text-blue-600 border-b-2 border-blue-600' : ''; ?>">
                            Categories
                        </a>
                        <a href="<?php echo esc_url(home_url('/track-order/')); ?>" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors <?php echo is_page('track-order') ? 'text-blue-600 border-b-2 border-blue-600' : ''; ?>">
                            Track Order
                        </a>
                        <a href="<?php echo esc_url(home_url('/about/')); ?>" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors <?php echo is_page('about') ? 'text-blue-600 border-b-2 border-blue-600' : ''; ?>">
                            About
                        </a>
                        <a href="<?php echo esc_url(home_url('/contact/')); ?>" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors <?php echo is_page('contact') ? 'text-blue-600 border-b-2 border-blue-600' : ''; ?>">
                            Contact
                        </a>
                        <a href="<?php echo esc_url(home_url('/faq/')); ?>" class="text-gray-700 hover:text-blue-600 px-3 py-2 text-sm font-medium transition-colors <?php echo is_page('faq') ? 'text-blue-600 border-b-2 border-blue-600' : ''; ?>">
                            FAQ
                        </a>
                    </nav>

                    <!-- Right Side Icons -->
                    <div class="flex items-center space-x-4">

                        <!-- Search -->
                        <div class="relative" x-data="{ searchOpen: false }">
                            <button @click="searchOpen = !searchOpen" class="p-2 text-gray-600 hover:text-blue-600 transition-colors">
                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </button>

                            <!-- Search Dropdown -->
                            <div x-show="searchOpen" @click.away="searchOpen = false" x-transition class="absolute right-0 mt-2 w-80 bg-white rounded-lg shadow-lg border border-gray-200 p-4 z-50">
                                <form action="<?php echo function_exists('wc_get_page_permalink') ? esc_url(wc_get_page_permalink('shop')) : esc_url(home_url('/shop/')); ?>" method="GET" class="space-y-3">
                                    <div class="relative">
                                        <input type="text" name="s" placeholder="Search products..." class="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                                        <svg class="absolute left-3 top-2.5 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                        </svg>
                                    </div>
                                    <button type="submit" class="w-full bg-blue-600 text-white py-2 rounded-lg hover:bg-blue-700 transition-colors">
                                        Search Products
                                    </button>
                                </form>
                            </div>
                        </div>

                        <!-- Wishlist -->
                        <a href="<?php echo esc_url(home_url('/wishlist/')); ?>" class="p-2 text-gray-600 hover:text-red-600 transition-colors relative">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                            </svg>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center" x-text="wishlistCount" x-show="wishlistCount > 0" style="display: none;">0</span>
                        </a>

                        <!-- Cart -->
                        <a href="<?php echo function_exists('wc_get_page_permalink') ? esc_url(wc_get_page_permalink('cart')) : esc_url(home_url('/cart/')); ?>" class="p-2 text-gray-600 hover:text-blue-600 transition-colors relative">
                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24" stroke-width="2">
                                <circle cx="8" cy="21" r="1"/>
                                <circle cx="19" cy="21" r="1"/>
                                <path d="m2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43h-15.12"/>
                            </svg>
                            <?php if (function_exists('WC') && WC()->cart): ?>
                                <span class="cart-count absolute -top-1 -right-1 bg-blue-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                    <?php echo WC()->cart->get_cart_contents_count(); ?>
                                </span>
                            <?php endif; ?>
                        </a>

                        <!-- User Menu -->
                        <?php if (is_user_logged_in()): ?>
                            <?php $current_user = wp_get_current_user(); ?>
                            <div class="relative" x-data="{ userMenuOpen: false }">
                                <button @click="userMenuOpen = !userMenuOpen" class="flex items-center space-x-2 p-2 text-gray-600 hover:text-blue-600 transition-colors">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                    </svg>
                                    <span class="hidden md:block text-sm font-medium"><?php echo esc_html($current_user->display_name); ?></span>
                                    <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                    </svg>
                                </button>

                                <!-- User Dropdown -->
                                <div x-show="userMenuOpen" @click.away="userMenuOpen = false" x-transition class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-2 z-50">
                                    <a href="<?php echo esc_url(home_url('/my-account/')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
                                        </svg>
                                        My Account
                                    </a>
                                    <a href="<?php echo esc_url(home_url('/wishlist/')); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                        </svg>
                                        Wishlist
                                    </a>
                                    <div class="border-t border-gray-200 my-1"></div>
                                    <a href="<?php echo wp_logout_url(home_url()); ?>" class="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100">
                                        <svg class="w-4 h-4 inline mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                                        </svg>
                                        Logout
                                    </a>
                                </div>
                            </div>
                        <?php else: ?>
                            <div class="flex items-center space-x-2">
                                <button onclick="openAuthModal('login')" class="text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors cursor-pointer">Login</button>
                                <span class="text-gray-400">|</span>
                                <button onclick="openAuthModal('register')" class="text-gray-600 hover:text-blue-600 text-sm font-medium transition-colors cursor-pointer">Register</button>
                            </div>
                        <?php endif; ?>

                        <!-- Mobile Menu Button -->
                        <button @click="mobileMenuOpen = !mobileMenuOpen" class="md:hidden p-2 text-gray-600 hover:text-blue-600 transition-colors">
                            <svg x-show="!mobileMenuOpen" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
                            </svg>
                            <svg x-show="mobileMenuOpen" class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Menu -->
        <div x-show="mobileMenuOpen" x-transition class="md:hidden bg-white border-b border-gray-200">
            <div class="px-4 py-2 space-y-1">
                <a href="<?php echo esc_url(home_url('/')); ?>" class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md <?php echo is_home() || is_front_page() ? 'text-blue-600 bg-blue-50' : ''; ?>">Home</a>
                <a href="<?php echo function_exists('wc_get_page_permalink') ? esc_url(wc_get_page_permalink('shop')) : esc_url(home_url('/shop/')); ?>" class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md <?php echo is_shop() ? 'text-blue-600 bg-blue-50' : ''; ?>">Shop</a>
                <a href="<?php echo esc_url(home_url('/categories/')); ?>" class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md <?php echo is_page('categories') ? 'text-blue-600 bg-blue-50' : ''; ?>">Categories</a>
                <a href="<?php echo esc_url(home_url('/track-order/')); ?>" class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md <?php echo is_page('track-order') ? 'text-blue-600 bg-blue-50' : ''; ?>">Track Order</a>
                <a href="<?php echo esc_url(home_url('/about/')); ?>" class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md <?php echo is_page('about') ? 'text-blue-600 bg-blue-50' : ''; ?>">About</a>
                <a href="<?php echo esc_url(home_url('/contact/')); ?>" class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md <?php echo is_page('contact') ? 'text-blue-600 bg-blue-50' : ''; ?>">Contact</a>
                <a href="<?php echo esc_url(home_url('/faq/')); ?>" class="block px-3 py-2 text-gray-700 hover:text-blue-600 hover:bg-gray-50 rounded-md <?php echo is_page('faq') ? 'text-blue-600 bg-blue-50' : ''; ?>">FAQ</a>
            </div>
        </div>
    </header>

    <script>
    function headerData() {
        return {
            isScrolled: false,
            mobileMenuOpen: false,
            wishlistCount: 0,

            init() {
                // Handle scroll effect
                window.addEventListener('scroll', () => {
                    this.isScrolled = window.scrollY > 10;
                });

                // Load wishlist count only (let WooCommerce handle cart)
                this.loadWishlistCount();
            },

            loadWishlistCount() {
                // Load from localStorage
                const wishlistItems = JSON.parse(localStorage.getItem('wishlist_items') || '[]');
                this.wishlistCount = wishlistItems.length;
            }
        }
    }
    </script>

<!-- Professional Auth Modal -->
<div id="authModal" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.6); z-index: 9999; backdrop-filter: blur(8px);">
    <div style="display: flex; align-items: center; justify-content: center; min-height: 100vh; padding: 1rem;">
        <div style="background: white; border-radius: 20px; box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25); max-width: 420px; width: 100%; position: relative; animation: modalSlideIn 0.4s cubic-bezier(0.16, 1, 0.3, 1); border: 1px solid rgba(255, 255, 255, 0.2);">

            <!-- Close Button -->
            <button onclick="closeAuthModal()" style="position: absolute; top: 1.25rem; right: 1.25rem; width: 2.25rem; height: 2.25rem; background: rgba(107, 114, 128, 0.1); border: none; border-radius: 50%; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all 0.2s; z-index: 10;" onmouseover="this.style.background='rgba(107, 114, 128, 0.15)'; this.style.transform='scale(1.05)'" onmouseout="this.style.background='rgba(107, 114, 128, 0.1)'; this.style.transform='scale(1)'">
                <i class="fas fa-times" style="color: #6b7280; font-size: 0.875rem;"></i>
            </button>

            <!-- Modal Header -->
            <div style="padding: 2.5rem 2.5rem 1.5rem 2.5rem; text-align: center;">
                <div style="display: inline-flex; align-items: center; justify-content: center; width: 4.5rem; height: 4.5rem; background: linear-gradient(135deg, #f8fafc, #e2e8f0); border-radius: 16px; margin-bottom: 1.5rem; border: 1px solid #e5e7eb;">
                    <i id="modalIcon" class="fas fa-sign-in-alt" style="color: #475569; font-size: 1.5rem;"></i>
                </div>
                <h2 id="modalTitle" style="font-size: 1.75rem; font-weight: 700; color: #0f172a; margin: 0; letter-spacing: -0.025em;">Welcome Back</h2>
                <p id="modalSubtitle" style="color: #64748b; margin: 0.75rem 0 0 0; font-size: 1rem; line-height: 1.5;">Please sign in to your account to continue</p>
            </div>

            <!-- Modal Content -->
            <div style="padding: 0 2.5rem 2.5rem 2.5rem;">

                <!-- Login Form -->
                <div id="loginForm" style="display: none;">
                    <form onsubmit="handleLogin(event)">
                        <div style="margin-bottom: 1.75rem;">
                            <label style="display: block; font-size: 0.9rem; font-weight: 600; color: #334155; margin-bottom: 0.75rem; letter-spacing: -0.01em;">Email Address</label>
                            <input type="email" required style="width: 100%; padding: 1rem 1.25rem; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 1rem; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-sizing: border-box; background: #fafafa;" placeholder="Enter your email address" onfocus="this.style.borderColor='#3b82f6'; this.style.boxShadow='0 0 0 4px rgba(59, 130, 246, 0.1)'; this.style.background='#ffffff'" onblur="this.style.borderColor='#e2e8f0'; this.style.boxShadow='none'; this.style.background='#fafafa'">
                        </div>
                        <div style="margin-bottom: 1.75rem;">
                            <label style="display: block; font-size: 0.9rem; font-weight: 600; color: #334155; margin-bottom: 0.75rem; letter-spacing: -0.01em;">Password</label>
                            <input type="password" required style="width: 100%; padding: 1rem 1.25rem; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 1rem; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-sizing: border-box; background: #fafafa;" placeholder="Enter your password" onfocus="this.style.borderColor='#3b82f6'; this.style.boxShadow='0 0 0 4px rgba(59, 130, 246, 0.1)'; this.style.background='#ffffff'" onblur="this.style.borderColor='#e2e8f0'; this.style.boxShadow='none'; this.style.background='#fafafa'">
                        </div>
                        <div style="display: flex; align-items: center; justify-content: space-between; margin-bottom: 2rem;">
                            <label style="display: flex; align-items: center; font-size: 0.9rem; color: #475569; cursor: pointer;">
                                <input type="checkbox" style="margin-right: 0.75rem; width: 1.125rem; height: 1.125rem; accent-color: #3b82f6; cursor: pointer;">
                                <span>Remember me</span>
                            </label>
                            <a href="#" onclick="alert('Password reset functionality would be implemented here')" style="font-size: 0.9rem; color: #3b82f6; text-decoration: none; font-weight: 600; transition: color 0.2s;" onmouseover="this.style.color='#2563eb'" onmouseout="this.style.color='#3b82f6'">Forgot password?</a>
                        </div>
                        <button type="submit" style="width: 100%; background: linear-gradient(135deg, #3b82f6, #2563eb); color: white; font-weight: 600; padding: 1.125rem 1.5rem; border: none; border-radius: 12px; font-size: 1rem; cursor: pointer; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3); letter-spacing: -0.01em;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(59, 130, 246, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 14px rgba(59, 130, 246, 0.3)'">
                            <i class="fas fa-sign-in-alt" style="margin-right: 0.5rem;"></i>Sign In to Account
                        </button>
                    </form>

                    <!-- Divider -->
                    <div style="margin: 2rem 0; position: relative;">
                        <div style="position: absolute; inset: 0; display: flex; align-items: center;">
                            <div style="width: 100%; border-top: 1px solid #e2e8f0;"></div>
                        </div>
                        <div style="position: relative; display: flex; justify-content: center; font-size: 0.875rem;">
                            <span style="background: white; padding: 0 1rem; color: #64748b; font-weight: 500;">New to Deal4u?</span>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <button onclick="switchToRegister()" style="color: #3b82f6; font-weight: 600; background: none; border: none; cursor: pointer; font-size: 0.95rem; padding: 0.5rem; border-radius: 6px; transition: all 0.2s;" onmouseover="this.style.background='rgba(59, 130, 246, 0.05)'; this.style.color='#2563eb'" onmouseout="this.style.background='none'; this.style.color='#3b82f6'">
                            Create a new account <i class="fas fa-arrow-right" style="margin-left: 0.5rem; font-size: 0.8rem;"></i>
                        </button>
                    </div>
                </div>

                <!-- Register Form -->
                <div id="registerForm" style="display: none;">
                    <form onsubmit="handleRegister(event)">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 1rem; margin-bottom: 1.75rem;">
                            <div>
                                <label style="display: block; font-size: 0.9rem; font-weight: 600; color: #334155; margin-bottom: 0.75rem; letter-spacing: -0.01em;">First Name</label>
                                <input type="text" required style="width: 100%; padding: 1rem 1.25rem; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 1rem; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-sizing: border-box; background: #fafafa;" placeholder="John" onfocus="this.style.borderColor='#16a34a'; this.style.boxShadow='0 0 0 4px rgba(34, 197, 94, 0.1)'; this.style.background='#ffffff'" onblur="this.style.borderColor='#e2e8f0'; this.style.boxShadow='none'; this.style.background='#fafafa'">
                            </div>
                            <div>
                                <label style="display: block; font-size: 0.9rem; font-weight: 600; color: #334155; margin-bottom: 0.75rem; letter-spacing: -0.01em;">Last Name</label>
                                <input type="text" required style="width: 100%; padding: 1rem 1.25rem; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 1rem; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-sizing: border-box; background: #fafafa;" placeholder="Doe" onfocus="this.style.borderColor='#16a34a'; this.style.boxShadow='0 0 0 4px rgba(34, 197, 94, 0.1)'; this.style.background='#ffffff'" onblur="this.style.borderColor='#e2e8f0'; this.style.boxShadow='none'; this.style.background='#fafafa'">
                            </div>
                        </div>
                        <div style="margin-bottom: 1.75rem;">
                            <label style="display: block; font-size: 0.9rem; font-weight: 600; color: #334155; margin-bottom: 0.75rem; letter-spacing: -0.01em;">Email Address</label>
                            <input type="email" required style="width: 100%; padding: 1rem 1.25rem; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 1rem; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-sizing: border-box; background: #fafafa;" placeholder="<EMAIL>" onfocus="this.style.borderColor='#16a34a'; this.style.boxShadow='0 0 0 4px rgba(34, 197, 94, 0.1)'; this.style.background='#ffffff'" onblur="this.style.borderColor='#e2e8f0'; this.style.boxShadow='none'; this.style.background='#fafafa'">
                        </div>
                        <div style="margin-bottom: 1.75rem;">
                            <label style="display: block; font-size: 0.9rem; font-weight: 600; color: #334155; margin-bottom: 0.75rem; letter-spacing: -0.01em;">Password</label>
                            <input type="password" required style="width: 100%; padding: 1rem 1.25rem; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 1rem; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-sizing: border-box; background: #fafafa;" placeholder="Create a secure password" onfocus="this.style.borderColor='#16a34a'; this.style.boxShadow='0 0 0 4px rgba(34, 197, 94, 0.1)'; this.style.background='#ffffff'" onblur="this.style.borderColor='#e2e8f0'; this.style.boxShadow='none'; this.style.background='#fafafa'">
                        </div>
                        <div style="margin-bottom: 2rem;">
                            <label style="display: block; font-size: 0.9rem; font-weight: 600; color: #334155; margin-bottom: 0.75rem; letter-spacing: -0.01em;">Confirm Password</label>
                            <input type="password" required style="width: 100%; padding: 1rem 1.25rem; border: 2px solid #e2e8f0; border-radius: 12px; font-size: 1rem; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-sizing: border-box; background: #fafafa;" placeholder="Confirm your password" onfocus="this.style.borderColor='#16a34a'; this.style.boxShadow='0 0 0 4px rgba(34, 197, 94, 0.1)'; this.style.background='#ffffff'" onblur="this.style.borderColor='#e2e8f0'; this.style.boxShadow='none'; this.style.background='#fafafa'">
                        </div>
                        <div style="margin-bottom: 2rem;">
                            <label style="display: flex; align-items: flex-start; font-size: 0.9rem; color: #475569; cursor: pointer; line-height: 1.5;">
                                <input type="checkbox" required style="margin-right: 0.75rem; margin-top: 0.125rem; width: 1.125rem; height: 1.125rem; accent-color: #16a34a; cursor: pointer;">
                                <span>I agree to the <a href="#" style="color: #16a34a; text-decoration: none; font-weight: 600; border-bottom: 1px solid transparent; transition: border-color 0.2s;" onmouseover="this.style.borderBottomColor='#16a34a'" onmouseout="this.style.borderBottomColor='transparent'">Terms of Service</a> and <a href="#" style="color: #16a34a; text-decoration: none; font-weight: 600; border-bottom: 1px solid transparent; transition: border-color 0.2s;" onmouseover="this.style.borderBottomColor='#16a34a'" onmouseout="this.style.borderBottomColor='transparent'">Privacy Policy</a></span>
                            </label>
                        </div>
                        <button type="submit" style="width: 100%; background: linear-gradient(135deg, #16a34a, #15803d); color: white; font-weight: 600; padding: 1.125rem 1.5rem; border: none; border-radius: 12px; font-size: 1rem; cursor: pointer; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); box-shadow: 0 4px 14px rgba(34, 197, 94, 0.3); letter-spacing: -0.01em;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 8px 25px rgba(34, 197, 94, 0.4)'" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 4px 14px rgba(34, 197, 94, 0.3)'">
                            <i class="fas fa-user-plus" style="margin-right: 0.5rem;"></i>Create Your Account
                        </button>
                    </form>

                    <!-- Divider -->
                    <div style="margin: 2rem 0; position: relative;">
                        <div style="position: absolute; inset: 0; display: flex; align-items: center;">
                            <div style="width: 100%; border-top: 1px solid #e2e8f0;"></div>
                        </div>
                        <div style="position: relative; display: flex; justify-content: center; font-size: 0.875rem;">
                            <span style="background: white; padding: 0 1rem; color: #64748b; font-weight: 500;">Already a member?</span>
                        </div>
                    </div>

                    <div style="text-align: center;">
                        <button onclick="switchToLogin()" style="color: #16a34a; font-weight: 600; background: none; border: none; cursor: pointer; font-size: 0.95rem; padding: 0.5rem; border-radius: 6px; transition: all 0.2s;" onmouseover="this.style.background='rgba(34, 197, 94, 0.05)'; this.style.color='#15803d'" onmouseout="this.style.background='none'; this.style.color='#16a34a'">
                            Sign in to your account <i class="fas fa-arrow-right" style="margin-left: 0.5rem; font-size: 0.8rem;"></i>
                        </button>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>

<style>
@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
        filter: blur(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
        filter: blur(0);
    }
}

/* Professional input focus styles */
.auth-input:focus {
    outline: none;
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 4px rgba(59, 130, 246, 0.1) !important;
    background: #ffffff !important;
}

.auth-input-register:focus {
    outline: none;
    border-color: #16a34a !important;
    box-shadow: 0 0 0 4px rgba(34, 197, 94, 0.1) !important;
    background: #ffffff !important;
}
</style>

<script>
function openAuthModal(type) {
    const modal = document.getElementById('authModal');
    const modalIcon = document.getElementById('modalIcon');
    const modalTitle = document.getElementById('modalTitle');
    const modalSubtitle = document.getElementById('modalSubtitle');
    const loginForm = document.getElementById('loginForm');
    const registerForm = document.getElementById('registerForm');

    if (type === 'login') {
        modalIcon.className = 'fas fa-sign-in-alt';
        modalIcon.style.color = '#475569';
        modalTitle.textContent = 'Welcome Back';
        modalSubtitle.textContent = 'Please sign in to your account to continue';
        loginForm.style.display = 'block';
        registerForm.style.display = 'none';
    } else {
        modalIcon.className = 'fas fa-user-plus';
        modalIcon.style.color = '#475569';
        modalTitle.textContent = 'Create Account';
        modalSubtitle.textContent = 'Join our community and start shopping today';
        loginForm.style.display = 'none';
        registerForm.style.display = 'block';
    }

    modal.style.display = 'block';
    document.body.style.overflow = 'hidden';

    // Focus first input field
    setTimeout(() => {
        const firstInput = modal.querySelector('input[type="email"], input[type="text"]');
        if (firstInput) firstInput.focus();
    }, 100);
}

function closeAuthModal() {
    const modal = document.getElementById('authModal');
    modal.style.display = 'none';
    document.body.style.overflow = 'auto';
}

function switchToLogin() {
    openAuthModal('login');
}

function switchToRegister() {
    openAuthModal('register');
}

function handleLogin(event) {
    event.preventDefault();
    // Here you would implement actual login logic
    alert('Login functionality would be implemented here with your authentication system');
    closeAuthModal();
}

function handleRegister(event) {
    event.preventDefault();
    // Here you would implement actual registration logic
    alert('Registration functionality would be implemented here with your authentication system');
    closeAuthModal();
}

// Close modal when clicking outside
document.addEventListener('DOMContentLoaded', function() {
    const modal = document.getElementById('authModal');
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === this) {
                closeAuthModal();
            }
        });
    }
});

// Close modal with Escape key
document.addEventListener('keydown', function(e) {
    if (e.key === 'Escape') {
        closeAuthModal();
    }
});
</script>
