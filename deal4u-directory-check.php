<?php
/**
 * Deal4u Theme Directory Diagnostic Tool
 * 
 * This script specifically checks for multiple theme directories
 * and identifies which one WordPress is actually using
 * 
 * Usage: Upload to WordPress root and access via browser
 * https://yourdomain.com/deal4u-directory-check.php?password=deal4u2025
 */

// Security check
$access_password = 'deal4u2025';
if (!isset($_GET['password']) || $_GET['password'] !== $access_password) {
    die('Access denied. Add ?password=deal4u2025 to URL');
}

// Include WordPress
if (file_exists('./wp-config.php')) {
    require_once('./wp-config.php');
    require_once('./wp-load.php');
} else {
    die('WordPress not found. Place this file in your WordPress root directory.');
}

?>
<!DOCTYPE html>
<html>
<head>
    <title>Deal4u Theme Directory Diagnostic</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
        .container { max-width: 1000px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; color: #155724; }
        .warning { background: #fff3cd; border-color: #ffeaa7; color: #856404; }
        .error { background: #f8d7da; border-color: #f5c6cb; color: #721c24; }
        .info { background: #d1ecf1; border-color: #bee5eb; color: #0c5460; }
        h1, h2 { color: #333; }
        pre { background: #f8f9fa; padding: 10px; border-radius: 4px; overflow-x: auto; }
        table { width: 100%; border-collapse: collapse; margin: 10px 0; }
        th, td { padding: 8px; text-align: left; border-bottom: 1px solid #ddd; }
        th { background-color: #f2f2f2; }
        .active-theme { background: #d4edda; font-weight: bold; }
        .inactive-theme { background: #f8d7da; }
        .file-hash { font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Deal4u Theme Directory Diagnostic</h1>
        <p><strong>Generated:</strong> <?php echo date('Y-m-d H:i:s'); ?></p>

        <?php
        // Get WordPress theme directory
        $wp_themes_dir = get_theme_root();
        
        // Function to scan for Deal4u related directories
        function find_deal4u_directories($base_path) {
            $directories = [];
            if (is_dir($base_path)) {
                $items = scandir($base_path);
                foreach ($items as $item) {
                    if ($item !== '.' && $item !== '..' && is_dir($base_path . '/' . $item)) {
                        if (stripos($item, 'deal4u') !== false || stripos($item, 'laravel') !== false) {
                            $directories[] = $item;
                        }
                    }
                }
            }
            return $directories;
        }

        // Function to check if directory contains theme files
        function is_valid_theme_directory($path) {
            $required_files = ['style.css', 'index.php'];
            foreach ($required_files as $file) {
                if (!file_exists($path . '/' . $file)) {
                    return false;
                }
            }
            return true;
        }

        // Function to get theme info from style.css
        function get_theme_info($path) {
            $style_css = $path . '/style.css';
            if (file_exists($style_css)) {
                $content = file_get_contents($style_css);
                preg_match('/Theme Name:\s*(.+)/i', $content, $name_match);
                preg_match('/Version:\s*(.+)/i', $content, $version_match);
                return [
                    'name' => isset($name_match[1]) ? trim($name_match[1]) : 'Unknown',
                    'version' => isset($version_match[1]) ? trim($version_match[1]) : 'Unknown'
                ];
            }
            return ['name' => 'Unknown', 'version' => 'Unknown'];
        }

        // Function to get functions.php info
        function get_functions_info($path) {
            $functions_php = $path . '/functions.php';
            if (file_exists($functions_php)) {
                $size = filesize($functions_php);
                $modified = date('Y-m-d H:i:s', filemtime($functions_php));
                $hash = md5_file($functions_php);
                
                // Check for specific functions
                $content = file_get_contents($functions_php);
                $has_timer = strpos($content, 'countdown') !== false || strpos($content, 'timer') !== false;
                $has_product_query = strpos($content, 'deal4u_get_flash_sale_products') !== false;
                $has_error_handling = strpos($content, 'set_error_handler') !== false;
                
                return [
                    'size' => $size,
                    'modified' => $modified,
                    'hash' => $hash,
                    'has_timer' => $has_timer,
                    'has_product_query' => $has_product_query,
                    'has_error_handling' => $has_error_handling
                ];
            }
            return null;
        }

        // Get current active theme info
        $current_theme = wp_get_theme();
        $active_theme_dir = $current_theme->get_stylesheet();
        $active_theme_path = $current_theme->get_stylesheet_directory();

        echo '<div class="section info">';
        echo '<h2>📋 Current WordPress Theme Settings</h2>';
        echo '<div><strong>Active Theme Name:</strong> ' . $current_theme->get('Name') . '</div>';
        echo '<div><strong>Active Theme Directory:</strong> ' . $active_theme_dir . '</div>';
        echo '<div><strong>Active Theme Path:</strong> ' . $active_theme_path . '</div>';
        echo '<div><strong>Theme Root Directory:</strong> ' . $wp_themes_dir . '</div>';
        echo '</div>';

        // Find all Deal4u related directories
        $deal4u_directories = find_deal4u_directories($wp_themes_dir);

        echo '<div class="section">';
        echo '<h2>📁 All Deal4u Related Directories Found</h2>';
        if (empty($deal4u_directories)) {
            echo '<div class="error">❌ No Deal4u related directories found!</div>';
        } else {
            echo '<table>';
            echo '<tr><th>Directory Name</th><th>Status</th><th>Theme Name</th><th>Version</th><th>Valid Theme</th></tr>';
            
            foreach ($deal4u_directories as $dir) {
                $full_path = $wp_themes_dir . '/' . $dir;
                $is_active = ($dir === $active_theme_dir);
                $is_valid = is_valid_theme_directory($full_path);
                $theme_info = get_theme_info($full_path);
                
                $status_class = $is_active ? 'active-theme' : 'inactive-theme';
                $status_text = $is_active ? '✅ ACTIVE' : '⚠️ INACTIVE';
                $valid_text = $is_valid ? '✅ Valid' : '❌ Invalid';
                
                echo "<tr class='$status_class'>";
                echo "<td><strong>$dir</strong></td>";
                echo "<td>$status_text</td>";
                echo "<td>{$theme_info['name']}</td>";
                echo "<td>{$theme_info['version']}</td>";
                echo "<td>$valid_text</td>";
                echo "</tr>";
            }
            echo '</table>';
        }
        echo '</div>';

        // Detailed analysis of functions.php in each directory
        echo '<div class="section">';
        echo '<h2>🔧 Functions.php Analysis</h2>';
        echo '<table>';
        echo '<tr><th>Directory</th><th>File Size</th><th>Last Modified</th><th>Hash (first 8 chars)</th><th>Has Timer</th><th>Has Product Query</th><th>Has Error Handling</th></tr>';
        
        foreach ($deal4u_directories as $dir) {
            $full_path = $wp_themes_dir . '/' . $dir;
            $functions_info = get_functions_info($full_path);
            
            if ($functions_info) {
                $is_active = ($dir === $active_theme_dir);
                $row_class = $is_active ? 'active-theme' : '';
                
                echo "<tr class='$row_class'>";
                echo "<td><strong>$dir</strong>" . ($is_active ? ' (ACTIVE)' : '') . "</td>";
                echo "<td>" . number_format($functions_info['size']) . " bytes</td>";
                echo "<td>{$functions_info['modified']}</td>";
                echo "<td class='file-hash'>" . substr($functions_info['hash'], 0, 8) . "</td>";
                echo "<td>" . ($functions_info['has_timer'] ? '✅' : '❌') . "</td>";
                echo "<td>" . ($functions_info['has_product_query'] ? '✅' : '❌') . "</td>";
                echo "<td>" . ($functions_info['has_error_handling'] ? '✅' : '❌') . "</td>";
                echo "</tr>";
            } else {
                echo "<tr>";
                echo "<td><strong>$dir</strong></td>";
                echo "<td colspan='6'>❌ functions.php not found</td>";
                echo "</tr>";
            }
        }
        echo '</table>';
        echo '</div>';

        // Check for file conflicts
        echo '<div class="section">';
        echo '<h2>⚠️ Potential Issues Detected</h2>';
        
        $issues = [];
        
        // Check if multiple valid themes exist
        $valid_themes = 0;
        foreach ($deal4u_directories as $dir) {
            $full_path = $wp_themes_dir . '/' . $dir;
            if (is_valid_theme_directory($full_path)) {
                $valid_themes++;
            }
        }
        
        if ($valid_themes > 1) {
            $issues[] = "🔴 Multiple valid Deal4u themes found ($valid_themes). This can cause conflicts.";
        }
        
        // Check if active theme has required features
        $active_functions = get_functions_info($active_theme_path);
        if ($active_functions) {
            if (!$active_functions['has_timer']) {
                $issues[] = "🟡 Active theme missing timer functionality.";
            }
            if (!$active_functions['has_product_query']) {
                $issues[] = "🟡 Active theme missing improved product query.";
            }
            if (!$active_functions['has_error_handling']) {
                $issues[] = "🟡 Active theme missing error handling.";
            }
        }
        
        // Check for different file hashes
        $hashes = [];
        foreach ($deal4u_directories as $dir) {
            $full_path = $wp_themes_dir . '/' . $dir;
            $functions_info = get_functions_info($full_path);
            if ($functions_info) {
                $hashes[$dir] = $functions_info['hash'];
            }
        }
        
        if (count(array_unique($hashes)) > 1) {
            $issues[] = "🔴 Different versions of functions.php detected across directories.";
        }
        
        if (empty($issues)) {
            echo '<div class="success">✅ No major issues detected!</div>';
        } else {
            foreach ($issues as $issue) {
                echo '<div class="warning">' . $issue . '</div>';
            }
        }
        echo '</div>';

        // Recommendations
        echo '<div class="section">';
        echo '<h2>💡 Recommendations</h2>';
        echo '<ol>';
        
        if ($valid_themes > 1) {
            echo '<li><strong>Remove duplicate themes:</strong> Keep only the active theme directory and remove others to prevent conflicts.</li>';
        }
        
        if ($active_functions && (!$active_functions['has_timer'] || !$active_functions['has_product_query'])) {
            echo '<li><strong>Update active theme:</strong> Upload the latest functions.php with timer and product query fixes to the active theme directory.</li>';
        }
        
        if (count(array_unique($hashes)) > 1) {
            echo '<li><strong>Synchronize files:</strong> Ensure all theme directories have the same version of functions.php or remove unused directories.</li>';
        }
        
        echo '<li><strong>Clear cache:</strong> After making changes, clear all caching plugins and server cache.</li>';
        echo '<li><strong>Test functionality:</strong> Verify timer countdown and product display after fixes.</li>';
        echo '</ol>';
        echo '</div>';

        // Quick fix commands
        echo '<div class="section info">';
        echo '<h2>🚀 Quick Fix Commands</h2>';
        echo '<h3>To remove inactive theme directories:</h3>';
        echo '<pre>';
        foreach ($deal4u_directories as $dir) {
            if ($dir !== $active_theme_dir) {
                echo "# Remove inactive theme: $dir\n";
                echo "rm -rf " . $wp_themes_dir . "/$dir\n\n";
            }
        }
        echo '</pre>';
        
        echo '<h3>To copy functions.php to active theme:</h3>';
        echo '<pre>';
        echo "# Upload your latest functions.php to:\n";
        echo $active_theme_path . "/functions.php\n";
        echo '</pre>';
        echo '</div>';
        ?>

        <div style="margin-top: 30px; padding: 20px; background: #f8f9fa; border-radius: 5px;">
            <p><strong>Summary:</strong></p>
            <ul>
                <li><strong>Total Deal4u directories found:</strong> <?php echo count($deal4u_directories); ?></li>
                <li><strong>Active theme directory:</strong> <?php echo $active_theme_dir; ?></li>
                <li><strong>Valid theme directories:</strong> <?php echo $valid_themes; ?></li>
                <li><strong>Issues detected:</strong> <?php echo count($issues); ?></li>
            </ul>
        </div>
    </div>
</body>
</html>
