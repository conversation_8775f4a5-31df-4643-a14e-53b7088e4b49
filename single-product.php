<?php
/**
 * The Template for displaying all single products
 * Deal4u WordPress Theme
 */

defined( 'ABSPATH' ) || exit;

get_header(); ?>

<div class="min-h-screen bg-gray-50">
    <!-- Breadcrumb Navigation -->
    <nav class="bg-white border-b border-gray-200 py-4">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex items-center space-x-2 text-sm text-gray-600">
                <a href="<?php echo esc_url(home_url('/')); ?>" class="hover:text-blue-600 transition-colors duration-200">Home</a>
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>" class="hover:text-blue-600 transition-colors duration-200">Shop</a>
                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                </svg>
                <span class="text-gray-900 font-medium"><?php the_title(); ?></span>
            </div>
        </div>
    </nav>

    <!-- Main Product Content -->
    <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <?php while ( have_posts() ) : ?>
            <?php the_post(); ?>
            <?php global $product; ?>

            <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 p-6 lg:p-8">
                    <!-- Product Images -->
                    <div class="space-y-4 relative">
                        <?php
                        // Get all product images
                        $attachment_ids = $product->get_gallery_image_ids();
                        $main_image_id = $product->get_image_id();

                        // Combine main image with gallery images
                        $all_images = array();
                        if ($main_image_id) {
                            $all_images[] = $main_image_id;
                        }
                        $all_images = array_merge($all_images, $attachment_ids);
                        $all_images = array_unique($all_images);
                        ?>

                        <!-- Main Product Image with Slider -->
                        <div class="aspect-square bg-gray-100 rounded-xl overflow-hidden relative">
                            <?php if (count($all_images) > 1) : ?>
                                <!-- Image Slider -->
                                <div class="image-slider relative w-full h-full">
                                    <?php foreach ($all_images as $index => $image_id) : ?>
                                        <?php
                                        $image_url = wp_get_attachment_image_url($image_id, 'large');
                                        $image_alt = get_post_meta($image_id, '_wp_attachment_image_alt', true);
                                        ?>
                                        <img src="<?php echo esc_url($image_url); ?>"
                                             alt="<?php echo esc_attr($image_alt ?: $product->get_name()); ?>"
                                             class="slider-image absolute inset-0 w-full h-full object-cover transition-opacity duration-300 <?php echo $index === 0 ? 'opacity-100' : 'opacity-0'; ?>"
                                             data-slide="<?php echo $index; ?>">
                                    <?php endforeach; ?>

                                    <!-- Navigation Arrows -->
                                    <button class="slider-prev absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 rounded-full p-2 shadow-lg transition-all duration-200">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                        </svg>
                                    </button>
                                    <button class="slider-next absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/80 hover:bg-white text-gray-800 rounded-full p-2 shadow-lg transition-all duration-200">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </button>
                                </div>
                            <?php else : ?>
                                <!-- Single Image -->
                                <?php
                                $image_url = $main_image_id ? wp_get_attachment_image_url($main_image_id, 'large') : wc_placeholder_img_src('large');
                                $image_alt = $main_image_id ? get_post_meta($main_image_id, '_wp_attachment_image_alt', true) : 'Product placeholder';
                                ?>
                                <img src="<?php echo esc_url($image_url); ?>"
                                     alt="<?php echo esc_attr($image_alt ?: $product->get_name()); ?>"
                                     class="w-full h-full object-cover">
                            <?php endif; ?>

                            <!-- Sale Badge -->
                            <?php if ($product->is_on_sale()) : ?>
                                <div class="absolute top-4 left-4 bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-1 rounded-full text-sm font-bold shadow-lg">
                                    SALE - Save <?php echo esc_html(round((($product->get_regular_price() - $product->get_sale_price()) / $product->get_regular_price()) * 100)); ?>%
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Thumbnail Images -->
                        <?php if (count($all_images) > 1) : ?>
                            <div class="flex gap-2 overflow-x-auto">
                                <?php foreach ($all_images as $index => $image_id) : ?>
                                    <?php $thumb_url = wp_get_attachment_image_url($image_id, 'thumbnail'); ?>
                                    <button class="thumbnail-btn flex-shrink-0 w-16 h-16 bg-gray-100 rounded-lg overflow-hidden border-2 transition-all duration-200 <?php echo $index === 0 ? 'border-blue-500' : 'border-transparent hover:border-gray-300'; ?>"
                                            data-slide="<?php echo $index; ?>">
                                        <img src="<?php echo esc_url($thumb_url); ?>"
                                             alt="Product thumbnail"
                                             class="w-full h-full object-cover">
                                    </button>
                                <?php endforeach; ?>
                            </div>
                        <?php endif; ?>
                    </div>

                    <!-- Product Details -->
                    <div class="space-y-6">
                        <!-- Product Title -->
                        <div>
                            <h1 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-3 leading-tight">
                                <?php the_title(); ?>
                            </h1>

                            <!-- Product Rating -->
                            <div class="flex items-center gap-3 mb-4">
                                <div class="flex items-center">
                                    <?php for($i = 1; $i <= 5; $i++): ?>
                                        <svg class="w-4 h-4 text-yellow-400 fill-current" viewBox="0 0 20 20">
                                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"/>
                                        </svg>
                                    <?php endfor; ?>
                                </div>
                                <span class="text-sm text-gray-600">(4.8/5 - 127 reviews)</span>
                            </div>
                        </div>

                        <!-- Price -->
                        <div class="space-y-2">
                            <div class="flex items-baseline gap-3">
                                <?php if ($product->is_on_sale()) : ?>
                                    <span class="text-2xl lg:text-3xl font-bold text-blue-600">
                                        <?php echo wc_price($product->get_sale_price()); ?>
                                    </span>
                                    <span class="text-lg text-gray-500 line-through">
                                        <?php echo wc_price($product->get_regular_price()); ?>
                                    </span>
                                <?php else : ?>
                                    <span class="text-2xl lg:text-3xl font-bold text-gray-900">
                                        <?php echo wc_price($product->get_price()); ?>
                                    </span>
                                <?php endif; ?>
                            </div>
                            <p class="text-sm text-gray-600">Free shipping on orders over $50</p>
                        </div>

                        <!-- Stock Status -->
                        <div class="flex items-center gap-3">
                            <?php if ($product->is_in_stock()) : ?>
                                <div class="flex items-center gap-2 px-3 py-1 bg-green-100 text-green-800 rounded-full text-sm font-medium">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                                    </svg>
                                    In Stock - Ready to Ship
                                </div>
                            <?php else : ?>
                                <div class="flex items-center gap-2 px-3 py-1 bg-red-100 text-red-800 rounded-full text-sm font-medium">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                    Out of Stock
                                </div>
                            <?php endif; ?>
                        </div>

                        <!-- Product Description -->
                        <div class="prose max-w-none">
                            <h3 class="text-lg font-semibold text-gray-900 mb-3">Product Description</h3>
                            <div class="text-gray-700 text-sm leading-relaxed">
                                <?php echo $product->get_description() ?: $product->get_short_description() ?: 'Premium quality product with exceptional features and performance.'; ?>
                            </div>
                        </div>

                        <!-- Add to Cart Form -->
                        <div class="space-y-4">
                            <?php if ($product->is_purchasable() && $product->is_in_stock()) : ?>
                                <form class="cart" action="<?php echo esc_url(apply_filters('woocommerce_add_to_cart_form_action', $product->get_permalink())); ?>" method="post" enctype='multipart/form-data'>

                                    <?php
                                    // Handle different product types
                                    if ($product->is_type('variable')) {
                                        // Variable product - show variations
                                        $attributes = $product->get_variation_attributes();
                                        $available_variations = $product->get_available_variations();

                                        if (!empty($attributes)) {
                                            echo '<div class="space-y-4 mb-6">';

                                            foreach ($attributes as $attribute_name => $options) {
                                                $attribute_label = wc_attribute_label($attribute_name);
                                                echo '<div>';
                                                echo '<label class="block text-sm font-medium text-gray-900 mb-2">' . esc_html($attribute_label) . ':</label>';

                                                if (taxonomy_exists($attribute_name)) {
                                                    // Taxonomy attribute
                                                    $terms = wc_get_product_terms($product->get_id(), $attribute_name, array('fields' => 'all'));
                                                    echo '<div class="flex flex-wrap gap-2">';
                                                    foreach ($terms as $term) {
                                                        if (in_array($term->slug, $options)) {
                                                            echo '<button type="button" class="variation-option px-3 py-2 border border-gray-300 rounded-lg text-sm hover:border-blue-500 hover:bg-blue-50 transition-colors duration-200" data-attribute="' . esc_attr($attribute_name) . '" data-value="' . esc_attr($term->slug) . '">';
                                                            echo esc_html($term->name);
                                                            echo '</button>';
                                                        }
                                                    }
                                                    echo '</div>';
                                                } else {
                                                    // Custom attribute
                                                    echo '<div class="flex flex-wrap gap-2">';
                                                    foreach ($options as $option) {
                                                        echo '<button type="button" class="variation-option px-3 py-2 border border-gray-300 rounded-lg text-sm hover:border-blue-500 hover:bg-blue-50 transition-colors duration-200" data-attribute="' . esc_attr($attribute_name) . '" data-value="' . esc_attr($option) . '">';
                                                        echo esc_html($option);
                                                        echo '</button>';
                                                    }
                                                    echo '</div>';
                                                }

                                                // Hidden select for WooCommerce
                                                echo '<select name="attribute_' . esc_attr($attribute_name) . '" class="hidden variation-select" data-attribute_name="attribute_' . esc_attr($attribute_name) . '">';
                                                echo '<option value="">Choose ' . esc_html($attribute_label) . '</option>';
                                                foreach ($options as $option) {
                                                    echo '<option value="' . esc_attr($option) . '">' . esc_html($option) . '</option>';
                                                }
                                                echo '</select>';

                                                echo '</div>';
                                            }

                                            echo '</div>';
                                        }

                                        // Hidden variation ID field
                                        echo '<input type="hidden" name="variation_id" class="variation_id" value="0" />';
                                        echo '<input type="hidden" name="product_id" value="' . absint($product->get_id()) . '" />';

                                    } elseif ($product->is_type('grouped')) {
                                        // Grouped product
                                        $grouped_products = $product->get_children();
                                        if ($grouped_products) {
                                            echo '<div class="space-y-3 mb-6">';
                                            echo '<h4 class="text-sm font-medium text-gray-900">Choose Products:</h4>';
                                            foreach ($grouped_products as $grouped_product_id) {
                                                $grouped_product = wc_get_product($grouped_product_id);
                                                if ($grouped_product) {
                                                    echo '<div class="flex items-center justify-between p-3 border border-gray-200 rounded-lg">';
                                                    echo '<div>';
                                                    echo '<h5 class="font-medium text-gray-900">' . esc_html($grouped_product->get_name()) . '</h5>';
                                                    echo '<p class="text-sm text-gray-600">' . $grouped_product->get_price_html() . '</p>';
                                                    echo '</div>';
                                                    echo '<div class="flex items-center gap-2">';
                                                    echo '<input type="number" name="quantity[' . $grouped_product_id . ']" value="0" min="0" class="w-16 px-2 py-1 border border-gray-300 rounded text-center text-sm">';
                                                    echo '</div>';
                                                    echo '</div>';
                                                }
                                            }
                                            echo '</div>';
                                        }
                                    }
                                    ?>

                                    <!-- Quantity Selector -->
                                    <div class="flex items-center gap-3 mb-4">
                                        <label class="text-sm font-medium text-gray-900">Quantity:</label>
                                        <div class="flex items-center border border-gray-300 rounded-lg overflow-hidden">
                                            <button type="button" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium transition-colors duration-200" onclick="decreaseQuantity()">-</button>
                                            <input type="number" id="quantity" name="quantity" value="1" min="1" class="w-16 px-3 py-2 text-center border-0 focus:ring-0 font-medium">
                                            <button type="button" class="px-3 py-2 bg-gray-100 hover:bg-gray-200 text-gray-700 font-medium transition-colors duration-200" onclick="increaseQuantity()">+</button>
                                        </div>
                                    </div>

                                    <!-- Add to Cart Button -->
                                    <button type="submit" name="add-to-cart" value="<?php echo esc_attr($product->get_id()); ?>"
                                            class="w-full bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-medium py-4 px-6 rounded-xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-[1.02] flex items-center justify-center gap-2">
                                        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
                                        </svg>
                                        <span class="add-to-cart-text">Add to Cart</span>
                                    </button>
                                </form>
                            <?php else : ?>
                                <button disabled class="w-full bg-gray-400 text-white font-medium py-4 px-6 rounded-xl cursor-not-allowed">
                                    Currently Unavailable
                                </button>
                            <?php endif; ?>

                            <!-- Product Meta -->
                            <div class="border-t border-gray-200 pt-4 space-y-2">
                                <div class="flex items-center gap-2 text-sm text-gray-600">
                                    <span class="font-medium">SKU:</span>
                                    <span><?php echo $product->get_sku() ?: 'N/A'; ?></span>
                                </div>
                                <div class="flex items-center gap-2 text-sm text-gray-600">
                                    <span class="font-medium">Category:</span>
                                    <span><?php echo wc_get_product_category_list($product->get_id(), ', ') ?: 'Uncategorized'; ?></span>
                                </div>
                                <?php if ($product->get_tags()) : ?>
                                <div class="flex items-center gap-2 text-sm text-gray-600">
                                    <span class="font-medium">Tags:</span>
                                    <span><?php echo wc_get_product_tag_list($product->get_id(), ', '); ?></span>
                                </div>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <?php endwhile; ?>
    </main>
</div>

<!-- Enhanced Product Styles -->
<style>
.image-slider {
    position: relative;
}

.slider-image {
    transition: opacity 0.3s ease-in-out;
}

.variation-option {
    transition: all 0.2s ease-in-out;
    cursor: pointer;
}

.variation-option:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.variation-option.selected {
    border-color: #3b82f6 !important;
    background-color: #eff6ff !important;
    color: #1d4ed8 !important;
}

.thumbnail-btn {
    transition: all 0.2s ease-in-out;
}

.thumbnail-btn:hover {
    transform: scale(1.05);
}

.slider-prev, .slider-next {
    transition: all 0.2s ease-in-out;
}

.slider-prev:hover, .slider-next:hover {
    transform: translateY(-50%) scale(1.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .slider-prev, .slider-next {
        display: none;
    }
}
</style>

<!-- Enhanced Product JavaScript -->
<script>
// Quantity Control
function increaseQuantity() {
    const input = document.getElementById('quantity');
    input.value = parseInt(input.value) + 1;
}

function decreaseQuantity() {
    const input = document.getElementById('quantity');
    if (parseInt(input.value) > 1) {
        input.value = parseInt(input.value) - 1;
    }
}

// Image Slider
document.addEventListener('DOMContentLoaded', function() {
    let currentSlide = 0;
    const slides = document.querySelectorAll('.slider-image');
    const thumbnails = document.querySelectorAll('.thumbnail-btn');
    const prevBtn = document.querySelector('.slider-prev');
    const nextBtn = document.querySelector('.slider-next');

    function showSlide(index) {
        slides.forEach((slide, i) => {
            slide.style.opacity = i === index ? '1' : '0';
        });

        thumbnails.forEach((thumb, i) => {
            thumb.classList.toggle('border-blue-500', i === index);
            thumb.classList.toggle('border-transparent', i !== index);
        });

        currentSlide = index;
    }

    // Thumbnail clicks
    thumbnails.forEach((thumb, index) => {
        thumb.addEventListener('click', () => showSlide(index));
    });

    // Navigation arrows
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            const newIndex = currentSlide > 0 ? currentSlide - 1 : slides.length - 1;
            showSlide(newIndex);
        });
    }

    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            const newIndex = currentSlide < slides.length - 1 ? currentSlide + 1 : 0;
            showSlide(newIndex);
        });
    }

    // Product Variations
    const variationOptions = document.querySelectorAll('.variation-option');
    const variationSelects = document.querySelectorAll('.variation-select');
    const addToCartBtn = document.querySelector('button[name="add-to-cart"]');
    const addToCartText = document.querySelector('.add-to-cart-text');

    variationOptions.forEach(option => {
        option.addEventListener('click', function() {
            const attribute = this.dataset.attribute;
            const value = this.dataset.value;

            // Update visual selection
            document.querySelectorAll(`[data-attribute="${attribute}"]`).forEach(btn => {
                btn.classList.remove('border-blue-500', 'bg-blue-50', 'text-blue-700');
                btn.classList.add('border-gray-300');
            });

            this.classList.remove('border-gray-300');
            this.classList.add('border-blue-500', 'bg-blue-50', 'text-blue-700');

            // Update hidden select
            const select = document.querySelector(`select[data-attribute_name="attribute_${attribute}"]`);
            if (select) {
                select.value = value;
                select.dispatchEvent(new Event('change'));
            }
        });
    });

    // Add to cart form submission
    const cartForm = document.querySelector('form.cart');
    if (cartForm) {
        cartForm.addEventListener('submit', function(e) {
            if (addToCartBtn && addToCartText) {
                addToCartText.innerHTML = `
                    <svg class="w-5 h-5 animate-spin" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                    </svg>
                    Adding to Cart...
                `;
                addToCartBtn.disabled = true;
            }
        });
    }
});
</script>

<?php get_footer(); ?>
