<?php
/**
 * Template Name: Shop Page
 * Deal4u WordPress Theme - Shop Template
 */

// SECURITY: Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// FIXED: Don't redirect, show shop content directly
get_header(); ?>

<!-- Enhanced Professional Shop Page -->
<main class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #3b82f6 2px, transparent 2px), radial-gradient(circle at 75% 75%, #8b5cf6 2px, transparent 2px); background-size: 50px 50px;"></div>
    </div>

    <!-- Enhanced Shop Header -->
    <section class="relative py-20 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-600 text-white overflow-hidden">
        <!-- Header <PERSON> Pattern -->
        <div class="absolute inset-0 opacity-10">
            <div class="absolute inset-0" style="background-image: radial-gradient(circle at 20% 80%, #ffffff 1px, transparent 1px), radial-gradient(circle at 80% 20%, #ffffff 1px, transparent 1px); background-size: 40px 40px;"></div>
        </div>

        <!-- Floating Elements -->
        <div class="absolute top-10 left-10 w-20 h-20 bg-white/10 rounded-full animate-pulse"></div>
        <div class="absolute top-32 right-20 w-16 h-16 bg-white/10 rounded-full animate-pulse" style="animation-delay: 1s;"></div>
        <div class="absolute bottom-20 left-1/4 w-12 h-12 bg-white/10 rounded-full animate-pulse" style="animation-delay: 2s;"></div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <div class="text-center">
                <div class="inline-block mb-6">
                    <div class="text-6xl md:text-8xl mb-4 animate-bounce">🛒</div>
                </div>
                <h1 class="text-5xl md:text-7xl lg:text-8xl font-extrabold mb-6 leading-tight">
                    <span class="bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent">
                        Shop
                    </span>
                </h1>
                <div class="h-1 w-32 bg-gradient-to-r from-white/50 to-purple-200 mx-auto rounded-full mb-8"></div>
                <p class="text-xl md:text-2xl text-blue-100 font-medium max-w-3xl mx-auto leading-relaxed">
                    Browse our <span class="text-white font-semibold">amazing collection</span> and discover the best deals on premium products
                </p>

                <!-- Stats -->
                <div class="mt-12 grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto">
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-2">1000+</div>
                        <div class="text-blue-200">Products</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-2">24/7</div>
                        <div class="text-blue-200">Support</div>
                    </div>
                    <div class="text-center">
                        <div class="text-3xl font-bold text-white mb-2">Free</div>
                        <div class="text-blue-200">Shipping</div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Shop Content -->
    <section class="py-20 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <?php if (class_exists('WooCommerce')): ?>
                <!-- Enhanced Shop Filters -->
                <div class="mb-12 bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-6 border border-white/20">
                    <div class="flex flex-col lg:flex-row gap-6 items-center justify-between">
                        <!-- Search Bar -->
                        <div class="flex-1 max-w-md">
                            <div class="relative">
                                <input type="text" id="product-search" placeholder="Search products..."
                                       class="w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-xl focus:border-blue-500 focus:ring-2 focus:ring-blue-200 transition-all duration-300 text-lg font-medium">
                                <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Filter Buttons -->
                        <div class="flex flex-wrap gap-3">
                            <select id="category-filter" class="px-6 py-3 border-2 border-gray-200 rounded-xl focus:border-purple-500 focus:ring-2 focus:ring-purple-200 transition-all duration-300 font-medium bg-white">
                                <option value="">All Categories</option>
                                <option value="electronics">Electronics</option>
                                <option value="clothing">Clothing</option>
                                <option value="accessories">Accessories</option>
                            </select>

                            <select id="price-filter" class="px-6 py-3 border-2 border-gray-200 rounded-xl focus:border-green-500 focus:ring-2 focus:ring-green-200 transition-all duration-300 font-medium bg-white">
                                <option value="">All Prices</option>
                                <option value="0-50">$0 - $50</option>
                                <option value="50-100">$50 - $100</option>
                                <option value="100-200">$100 - $200</option>
                                <option value="200+">$200+</option>
                            </select>

                            <select id="sort-filter" class="px-6 py-3 border-2 border-gray-200 rounded-xl focus:border-indigo-500 focus:ring-2 focus:ring-indigo-200 transition-all duration-300 font-medium bg-white">
                                <option value="default">Sort by</option>
                                <option value="price-low">Price: Low to High</option>
                                <option value="price-high">Price: High to Low</option>
                                <option value="newest">Newest First</option>
                                <option value="popular">Most Popular</option>
                            </select>
                        </div>
                    </div>

                    <!-- Active Filters Display -->
                    <div id="active-filters" class="mt-4 flex flex-wrap gap-2 hidden">
                        <span class="text-sm font-medium text-gray-600">Active filters:</span>
                    </div>
                </div>

                <!-- Products Section -->
                <?php
                // Get WooCommerce products
                $args = array(
                    'post_type' => 'product',
                    'posts_per_page' => 12,
                    'post_status' => 'publish'
                );
                $products = new WP_Query($args);

                if ($products->have_posts()): ?>
                    <!-- Products Count -->
                    <div class="mb-8 text-center">
                        <h2 class="text-2xl font-bold text-gray-800 mb-2">
                            <span id="products-count"><?php echo $products->found_posts; ?></span> Products Found
                        </h2>
                        <div class="h-1 w-16 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"></div>
                    </div>
                    <!-- Enhanced Product Grid -->
                    <div id="products-grid" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
                        <?php while ($products->have_posts()): $products->the_post();
                            global $product;
                            $image_url = wp_get_attachment_image_url($product->get_image_id(), 'medium');
                            if (!$image_url) {
                                $image_url = wc_placeholder_img_src('medium');
                            }
                            ?>
                            <div class="group bg-white rounded-2xl shadow-xl overflow-hidden hover:shadow-2xl transition-all duration-500 transform hover:-translate-y-2 hover:scale-[1.02] border border-gray-100 backdrop-blur-sm product-card"
                                 data-category="<?php echo esc_attr(wp_get_post_terms($product->get_id(), 'product_cat', array('fields' => 'slugs'))[0] ?? ''); ?>"
                                 data-price="<?php echo esc_attr($product->get_price()); ?>"
                                 data-date="<?php echo esc_attr($product->get_date_created()->date('Y-m-d')); ?>">

                                <!-- Enhanced Product Image -->
                                <div class="relative overflow-hidden h-64">
                                    <img src="<?php echo esc_url($image_url); ?>"
                                         alt="<?php echo esc_attr($product->get_name()); ?>"
                                         class="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110">

                                    <!-- Gradient Overlay on Hover -->
                                    <div class="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

                                    <!-- Enhanced Sale Badge -->
                                    <?php if ($product->is_on_sale()) : ?>
                                        <div class="absolute top-4 left-4 bg-gradient-to-r from-red-500 to-pink-500 text-white px-3 py-2 rounded-full text-sm font-bold shadow-lg animate-pulse sale-badge">
                                            <span class="flex items-center gap-1">
                                                🔥 SALE
                                            </span>
                                        </div>
                                    <?php endif; ?>

                                    <!-- Quick Action Buttons -->
                                    <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-all duration-300 transform translate-y-2 group-hover:translate-y-0 space-y-2">
                                        <!-- Quick View Button -->
                                        <button class="bg-white/90 backdrop-blur-sm text-gray-800 p-2 rounded-full shadow-lg hover:bg-white transition-colors duration-200 quick-view-btn"
                                                title="Quick View">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                            </svg>
                                        </button>

                                        <!-- Wishlist Button -->
                                        <button class="bg-white/90 backdrop-blur-sm text-gray-800 p-2 rounded-full shadow-lg hover:bg-white hover:text-red-500 transition-colors duration-200 wishlist-btn"
                                                title="Add to Wishlist">
                                            <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>

                                <!-- Enhanced Product Info -->
                                <div class="p-6 flex flex-col h-full">
                                    <h3 class="text-xl font-bold text-gray-900 mb-3 line-clamp-2 leading-tight product-title">
                                        <a href="<?php the_permalink(); ?>" class="hover:text-transparent hover:bg-gradient-to-r hover:from-blue-600 hover:to-purple-600 hover:bg-clip-text transition-all duration-300">
                                            <?php the_title(); ?>
                                        </a>
                                    </h3>

                                    <p class="text-gray-600 text-sm mb-4 line-clamp-2 leading-relaxed">
                                        <?php echo wp_trim_words(get_the_excerpt(), 15); ?>
                                    </p>

                                    <!-- Enhanced Price Section -->
                                    <div class="mb-4">
                                        <div class="text-2xl font-extrabold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent product-price">
                                            <?php echo $product->get_price_html(); ?>
                                        </div>
                                        <?php if ($product->is_on_sale()) : ?>
                                            <div class="text-sm text-green-600 font-semibold mt-1">
                                                💰 Save <?php
                                                $regular_price = $product->get_regular_price();
                                                $sale_price = $product->get_sale_price();
                                                if ($regular_price && $sale_price) {
                                                    $savings = $regular_price - $sale_price;
                                                    echo wc_price($savings);
                                                }
                                                ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>

                                    <!-- Enhanced Rating Section -->
                                    <div class="flex items-center gap-2 mb-4">
                                        <div class="flex text-yellow-400">
                                            <?php
                                            $rating = $product->get_average_rating();
                                            for ($i = 1; $i <= 5; $i++) {
                                                if ($i <= $rating) {
                                                    echo '<svg class="w-4 h-4 fill-current" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>';
                                                } else {
                                                    echo '<svg class="w-4 h-4 fill-current text-gray-300" viewBox="0 0 20 20"><path d="M10 15l-5.878 3.09 1.123-6.545L.489 6.91l6.572-.955L10 0l2.939 5.955 6.572.955-4.756 4.635 1.123 6.545z"/></svg>';
                                                }
                                            }
                                            ?>
                                        </div>
                                        <span class="text-gray-600 text-sm font-medium">(<?php echo $product->get_review_count(); ?> reviews)</span>
                                    </div>

                                    <!-- Product Categories -->
                                    <div class="mb-4">
                                        <?php
                                        $categories = wp_get_post_terms($product->get_id(), 'product_cat');
                                        if (!empty($categories)) {
                                            foreach (array_slice($categories, 0, 2) as $category) {
                                                echo '<span class="inline-block bg-gradient-to-r from-blue-100 to-purple-100 text-blue-800 text-xs font-semibold px-3 py-1 rounded-full mr-2 mb-1">' . esc_html($category->name) . '</span>';
                                            }
                                        }
                                        ?>
                                    </div>

                                    <!-- Enhanced Action Buttons -->
                                    <div class="mt-auto pt-4">

                                        <?php if ($product->is_purchasable() && $product->is_in_stock()): ?>
                                            <?php if ($product->is_type('simple')): ?>
                                                <!-- Enhanced Simple Product Buttons -->
                                                <div class="space-y-3">
                                                    <!-- Primary Add to Cart Button -->
                                                    <a href="<?php echo esc_url(home_url('/?add-to-cart=' . $product->get_id())); ?>"
                                                       data-quantity="1"
                                                       data-product_id="<?php echo esc_attr($product->get_id()); ?>"
                                                       data-product_sku="<?php echo esc_attr($product->get_sku()); ?>"
                                                       class="group/btn w-full bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-[1.02] button product_type_simple add_to_cart_button ajax_add_to_cart relative overflow-hidden"
                                                       aria-label="<?php echo esc_attr($product->add_to_cart_description()); ?>"
                                                       rel="nofollow">
                                                        <!-- Button Shine Effect -->
                                                        <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700"></div>

                                                        <svg class="w-5 h-5 transition-transform group-hover/btn:scale-110 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 3h2l.4 2M7 13h10l4-8H5.4m0 0L7 13m0 0l-1.5 6M7 13l-1.5-6m0 0h15M17 21a2 2 0 100-4 2 2 0 000 4zM9 21a2 2 0 100-4 2 2 0 000 4z"></path>
                                                        </svg>
                                                        <span class="text-lg relative z-10">Add to Cart</span>
                                                    </a>

                                                    <!-- Secondary Quick View Button -->
                                                    <a href="<?php echo esc_url(get_permalink($product->get_id())); ?>"
                                                       class="w-full bg-white border-2 border-gray-200 hover:border-blue-300 text-gray-700 hover:text-blue-600 font-semibold py-3 px-4 rounded-xl transition-all duration-300 flex items-center justify-center gap-2 hover:shadow-md"
                                                       title="View Product Details">
                                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                        </svg>
                                                        <span>Quick View</span>
                                                    </a>
                                                </div>
                                            <?php elseif ($product->is_type('variable')): ?>
                                                <!-- Enhanced Variable Product Button -->
                                                <a href="<?php the_permalink(); ?>"
                                                   class="group/btn w-full bg-gradient-to-r from-purple-500 via-pink-500 to-indigo-500 hover:from-purple-600 hover:via-pink-600 hover:to-indigo-600 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-[1.02] relative overflow-hidden">
                                                    <!-- Button Shine Effect -->
                                                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700"></div>

                                                    <svg class="w-5 h-5 transition-transform group-hover/btn:scale-110 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                    </svg>
                                                    <span class="text-lg relative z-10">Select Options</span>
                                                </a>
                                            <?php else: ?>
                                                <!-- Enhanced Other Product Types Button -->
                                                <a href="<?php the_permalink(); ?>"
                                                   class="group/btn w-full bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white font-bold py-4 px-6 rounded-xl transition-all duration-300 flex items-center justify-center gap-2 shadow-lg hover:shadow-xl transform hover:scale-[1.02] relative overflow-hidden">
                                                    <!-- Button Shine Effect -->
                                                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700"></div>

                                                    <svg class="w-5 h-5 transition-transform group-hover/btn:scale-110 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
                                                    </svg>
                                                    <span class="text-lg relative z-10">View Product</span>
                                                </a>
                                            <?php endif; ?>
                                        <?php else: ?>
                                            <!-- Enhanced Out of Stock Button -->
                                            <button class="w-full bg-gradient-to-r from-gray-400 to-gray-500 text-white font-bold py-4 px-6 rounded-xl cursor-not-allowed opacity-75 flex items-center justify-center gap-2">
                                                <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18.364 18.364A9 9 0 005.636 5.636m12.728 12.728L5.636 5.636m12.728 12.728L18.364 5.636M5.636 18.364l12.728-12.728"></path>
                                                </svg>
                                                <span class="text-lg"><?php echo $product->is_in_stock() ? 'Not Available' : 'Out of Stock'; ?></span>
                                            </button>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endwhile; ?>
                    </div>

                    <!-- Enhanced Pagination -->
                    <div class="mt-16 text-center">
                        <div class="inline-flex items-center justify-center space-x-2 bg-white/80 backdrop-blur-sm rounded-2xl shadow-xl p-4 border border-white/20">
                            <?php
                            $pagination = paginate_links(array(
                                'total' => $products->max_num_pages,
                                'prev_text' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path></svg> Previous',
                                'next_text' => 'Next <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path></svg>',
                                'type' => 'array',
                                'mid_size' => 2,
                                'end_size' => 1
                            ));

                            if ($pagination) {
                                foreach ($pagination as $link) {
                                    if (strpos($link, 'current') !== false) {
                                        // Current page
                                        echo '<span class="px-4 py-2 bg-gradient-to-r from-blue-600 to-purple-600 text-white font-bold rounded-xl shadow-lg">' . strip_tags($link) . '</span>';
                                    } elseif (strpos($link, 'dots') !== false) {
                                        // Dots
                                        echo '<span class="px-2 py-2 text-gray-500">...</span>';
                                    } else {
                                        // Regular links
                                        echo '<a href="' . esc_url(preg_match('/href=["\']([^"\']+)["\']/', $link, $matches) ? $matches[1] : '#') . '" class="px-4 py-2 bg-white hover:bg-gradient-to-r hover:from-blue-600 hover:to-purple-600 text-gray-700 hover:text-white font-semibold rounded-xl transition-all duration-300 shadow-md hover:shadow-lg transform hover:scale-105 flex items-center gap-2">' . strip_tags($link) . '</a>';
                                    }
                                }
                            }
                            ?>
                        </div>

                        <!-- Load More Button (Alternative) -->
                        <div class="mt-8">
                            <button id="load-more-btn" class="group/load bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white font-bold py-4 px-8 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 relative overflow-hidden hidden">
                                <!-- Button Shine Effect -->
                                <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover/load:translate-x-full transition-transform duration-700"></div>

                                <span class="relative z-10 flex items-center gap-2">
                                    <svg class="w-5 h-5 transition-transform group-hover/load:rotate-180" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Load More Products
                                </span>
                            </button>
                        </div>
                    </div>

                <?php else: ?>
                    <!-- Enhanced No Products Found -->
                    <div class="text-center py-20">
                        <div class="bg-white/80 backdrop-blur-sm rounded-3xl shadow-2xl p-12 max-w-2xl mx-auto border border-white/20">
                            <div class="text-8xl mb-8 animate-bounce">📦</div>
                            <h3 class="text-4xl font-bold bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent mb-6">No Products Yet</h3>
                            <p class="text-xl text-gray-600 mb-8 leading-relaxed">Products will appear here once you add them to your WooCommerce store. Start building your amazing product catalog!</p>

                            <?php if (current_user_can('manage_options')): ?>
                                <a href="<?php echo admin_url('post-new.php?post_type=product'); ?>"
                                   class="group/add inline-flex items-center gap-3 bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-600 hover:from-blue-700 hover:via-purple-700 hover:to-indigo-700 text-white font-bold py-4 px-8 rounded-2xl transition-all duration-300 shadow-lg hover:shadow-xl transform hover:scale-105 relative overflow-hidden">
                                    <!-- Button Shine Effect -->
                                    <div class="absolute inset-0 bg-gradient-to-r from-transparent via-white/20 to-transparent -skew-x-12 -translate-x-full group-hover/add:translate-x-full transition-transform duration-700"></div>

                                    <svg class="w-6 h-6 transition-transform group-hover/add:scale-110 relative z-10" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                    </svg>
                                    <span class="text-xl relative z-10">Add Your First Product</span>
                                </a>
                            <?php else: ?>
                                <div class="text-gray-500 text-lg">
                                    <p>Check back soon for exciting new products!</p>
                                </div>
                            <?php endif; ?>

                            <!-- Decorative Elements -->
                            <div class="mt-8 flex justify-center space-x-2">
                                <div class="w-3 h-3 bg-blue-400 rounded-full animate-pulse"></div>
                                <div class="w-3 h-3 bg-purple-400 rounded-full animate-pulse" style="animation-delay: 0.2s;"></div>
                                <div class="w-3 h-3 bg-indigo-400 rounded-full animate-pulse" style="animation-delay: 0.4s;"></div>
                            </div>
                        </div>
                    </div>
                <?php endif;
                wp_reset_postdata(); ?>

            <?php else: ?>
                <!-- WooCommerce Not Active -->
                <div class="text-center">
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg p-8 mb-8 max-w-2xl mx-auto">
                        <h2 class="text-2xl font-bold text-yellow-800 mb-4">WooCommerce Required</h2>
                        <p class="text-yellow-700 mb-6">WooCommerce plugin is required for the shop functionality. Please install and activate WooCommerce to see products.</p>
                        <a href="<?php echo admin_url('plugin-install.php?s=woocommerce&tab=search&type=term'); ?>"
                           class="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white font-bold py-3 px-6 rounded-lg transition-all duration-300">
                            Install WooCommerce
                        </a>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </section>
</main>

<!-- Enhanced Shop JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Filter elements
    const searchInput = document.getElementById('product-search');
    const categoryFilter = document.getElementById('category-filter');
    const priceFilter = document.getElementById('price-filter');
    const sortFilter = document.getElementById('sort-filter');
    const productsGrid = document.getElementById('products-grid');
    const productsCount = document.getElementById('products-count');
    const activeFilters = document.getElementById('active-filters');

    // Filter functionality
    function filterProducts() {
        const searchTerm = searchInput.value.toLowerCase();
        const selectedCategory = categoryFilter.value;
        const selectedPriceRange = priceFilter.value;
        const selectedSort = sortFilter.value;

        const products = Array.from(productsGrid.querySelectorAll('.product-card'));
        let visibleProducts = [];

        products.forEach(product => {
            const title = product.querySelector('.product-title').textContent.toLowerCase();
            const category = product.getAttribute('data-category') || '';
            const price = parseFloat(product.getAttribute('data-price')) || 0;

            let visible = true;

            // Search filter
            if (searchTerm && !title.includes(searchTerm)) {
                visible = false;
            }

            // Category filter
            if (selectedCategory && category !== selectedCategory) {
                visible = false;
            }

            // Price filter
            if (selectedPriceRange) {
                const [min, max] = selectedPriceRange.split('-').map(p => p === '+' ? Infinity : parseFloat(p));
                if (price < min || (max !== undefined && price > max)) {
                    visible = false;
                }
            }

            if (visible) {
                visibleProducts.push(product);
                product.style.display = 'block';
                product.style.animation = 'fadeIn 0.5s ease-in-out';
            } else {
                product.style.display = 'none';
            }
        });

        // Sort products
        if (selectedSort && selectedSort !== 'default') {
            sortProducts(visibleProducts, selectedSort);
        }

        // Update count
        if (productsCount) {
            productsCount.textContent = visibleProducts.length;
        }

        // Update active filters
        updateActiveFilters();
    }

    function sortProducts(products, sortBy) {
        products.sort((a, b) => {
            const priceA = parseFloat(a.getAttribute('data-price')) || 0;
            const priceB = parseFloat(b.getAttribute('data-price')) || 0;
            const dateA = new Date(a.getAttribute('data-date') || 0);
            const dateB = new Date(b.getAttribute('data-date') || 0);

            switch (sortBy) {
                case 'price-low':
                    return priceA - priceB;
                case 'price-high':
                    return priceB - priceA;
                case 'newest':
                    return dateB - dateA;
                default:
                    return 0;
            }
        });

        // Reorder DOM elements
        products.forEach(product => {
            productsGrid.appendChild(product);
        });
    }

    function updateActiveFilters() {
        const filters = [];

        if (searchInput.value) filters.push(`Search: "${searchInput.value}"`);
        if (categoryFilter.value) filters.push(`Category: ${categoryFilter.options[categoryFilter.selectedIndex].text}`);
        if (priceFilter.value) filters.push(`Price: ${priceFilter.options[priceFilter.selectedIndex].text}`);
        if (sortFilter.value && sortFilter.value !== 'default') filters.push(`Sort: ${sortFilter.options[sortFilter.selectedIndex].text}`);

        if (filters.length > 0) {
            activeFilters.innerHTML = '<span class="text-sm font-medium text-gray-600">Active filters:</span> ' +
                filters.map(filter => `<span class="inline-block bg-blue-100 text-blue-800 text-xs font-semibold px-2 py-1 rounded-full mr-1">${filter}</span>`).join('');
            activeFilters.classList.remove('hidden');
        } else {
            activeFilters.classList.add('hidden');
        }
    }

    // Event listeners
    if (searchInput) searchInput.addEventListener('input', filterProducts);
    if (categoryFilter) categoryFilter.addEventListener('change', filterProducts);
    if (priceFilter) priceFilter.addEventListener('change', filterProducts);
    if (sortFilter) sortFilter.addEventListener('change', filterProducts);

    // Wishlist functionality
    document.querySelectorAll('.wishlist-btn').forEach(btn => {
        btn.addEventListener('click', function(e) {
            e.preventDefault();
            this.classList.toggle('active');
            this.innerHTML = this.classList.contains('active') ?
                '<svg class="w-5 h-5" fill="currentColor" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path></svg>' :
                '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z"></path></svg>';
        });
    });
});

// CSS animations
const style = document.createElement('style');
style.textContent = `
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(20px); }
        to { opacity: 1; transform: translateY(0); }
    }

    .product-card {
        transition: all 0.3s ease;
    }

    .wishlist-btn.active {
        background: #ef4444 !important;
        color: white !important;
    }
`;
document.head.appendChild(style);
</script>

<?php get_footer(); ?>
