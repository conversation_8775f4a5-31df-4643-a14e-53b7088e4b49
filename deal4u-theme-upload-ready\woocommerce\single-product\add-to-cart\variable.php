<?php
/**
 * Variable product add to cart
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/single-product/add-to-cart/variable.php.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 6.1.0
 */

defined( 'ABSPATH' ) || exit;

global $product;

$attribute_keys  = array_keys( $attributes );
$variations_json = wp_json_encode( $available_variations );
$variations_attr = function_exists( 'wc_esc_json' ) ? wc_esc_json( $variations_json ) : _wp_specialchars( $variations_json, ENT_QUOTES, 'UTF-8', true );

do_action( 'woocommerce_before_add_to_cart_form' ); ?>

<form class="variations_form cart woocommerce-variation-form" action="<?php echo esc_url( apply_filters( 'woocommerce_add_to_cart_form_action', $product->get_permalink() ) ); ?>" method="post" enctype='multipart/form-data' data-product_id="<?php echo absint( $product->get_id() ); ?>" data-product_variations="<?php echo $variations_attr; // WPCS: XSS ok. ?>"><?php
// Debug info for troubleshooting
if (current_user_can('administrator')) {
    echo '<!-- Debug: Product ID: ' . $product->get_id() . ' -->';
    echo '<!-- Debug: Available Variations: ' . count($available_variations) . ' -->';
    echo '<!-- Debug: Attributes: ' . count($attributes) . ' -->';
}
?>
	<?php do_action( 'woocommerce_before_variations_form' ); ?>

	<?php if ( empty( $available_variations ) && false !== $available_variations ) : ?>
		<p class="stock out-of-stock"><?php echo esc_html( apply_filters( 'woocommerce_out_of_stock_message', __( 'This product is currently out of stock and unavailable.', 'woocommerce' ) ) ); ?></p>
	<?php elseif ( empty( $available_variations ) ) : ?>
		<!-- No variations available - treat as simple product -->
		<div class="no-variations-fallback">
			<p class="bg-yellow-100 border border-yellow-400 text-yellow-700 px-4 py-3 rounded mb-4">
				<strong>Note:</strong> This product is configured as variable but has no variations. Contact support for assistance.
			</p>

			<?php if ( $product->is_purchasable() && $product->is_in_stock() ) : ?>
				<div class="quantity-and-cart flex items-center gap-4 mb-4">
					<div class="quantity">
						<label for="quantity_<?php echo esc_attr( $product->get_id() ); ?>" class="sr-only"><?php esc_html_e( 'Quantity', 'woocommerce' ); ?></label>
						<input type="number" id="quantity_<?php echo esc_attr( $product->get_id() ); ?>" class="input-text qty text w-16 px-3 py-2 border border-gray-300 rounded" step="1" min="1" max="<?php echo esc_attr( 0 < $product->get_max_purchase_quantity() ? $product->get_max_purchase_quantity() : '' ); ?>" name="quantity" value="1" title="<?php echo esc_attr_x( 'Qty', 'Product quantity input tooltip', 'woocommerce' ); ?>" size="4" placeholder="" inputmode="numeric" />
					</div>

					<button type="submit" name="add-to-cart" value="<?php echo esc_attr( $product->get_id() ); ?>" class="single_add_to_cart_button button alt bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors">
						<?php echo esc_html( $product->single_add_to_cart_text() ); ?>
					</button>
				</div>
			<?php else : ?>
				<p class="text-red-600 font-semibold">This product is currently unavailable.</p>
			<?php endif; ?>
		</div>
	<?php else : ?>
		<div class="variations-container bg-gray-50 p-6 rounded-lg border">
			<h3 class="text-lg font-semibold text-gray-900 mb-4">Product Options</h3>

			<table class="variations w-full" cellspacing="0" role="presentation">
				<tbody>
					<?php foreach ( $attributes as $attribute_name => $options ) : ?>
						<tr class="border-b border-gray-200 last:border-b-0">
							<th class="label py-3 pr-4 text-left">
								<label for="<?php echo esc_attr( sanitize_title( $attribute_name ) ); ?>" class="text-sm font-medium text-gray-700">
									<?php echo wc_attribute_label( $attribute_name ); // WPCS: XSS ok. ?>
								</label>
							</th>
							<td class="value py-3">
								<div class="flex items-center gap-3">
									<?php
										wc_dropdown_variation_attribute_options(
											array(
												'options'   => $options,
												'attribute' => $attribute_name,
												'product'   => $product,
												'class'     => 'form-select px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500',
											)
										);
										echo end( $attribute_keys ) === $attribute_name ? wp_kses_post( apply_filters( 'woocommerce_reset_variations_link', '<a class="reset_variations text-sm text-blue-600 hover:text-blue-800 underline ml-2" href="#">' . esc_html__( 'Clear', 'woocommerce' ) . '</a>' ) ) : '';
									?>
								</div>
							</td>
						</tr>
					<?php endforeach; ?>
				</tbody>
			</table>
		</div>

		<div class="single_variation_wrap">
			<?php
				/**
				 * Hook: woocommerce_before_single_variation.
				 */
				do_action( 'woocommerce_before_single_variation' );

				/**
				 * Hook: woocommerce_single_variation. Used to output the cart button and placeholder for variation data.
				 *
				 * @since 2.4.0
				 * @hooked woocommerce_single_variation - 10 Empty div that variation data gets printed in
				 * @hooked woocommerce_single_variation_add_to_cart_button - 20 Qty and cart button
				 */
				do_action( 'woocommerce_single_variation' );

				/**
				 * Hook: woocommerce_after_single_variation.
				 */
				do_action( 'woocommerce_after_single_variation' );
			?>
		</div>
	<?php endif; ?>

	<?php do_action( 'woocommerce_after_variations_form' ); ?>
</form>

<?php
do_action( 'woocommerce_after_add_to_cart_form' );
?>

<script type="text/javascript">
jQuery(document).ready(function($) {
    // Ensure variation form is properly initialized
    var $form = $('.variations_form');

    if ($form.length > 0) {
        // Initialize the variation form
        $form.wc_variation_form();

        // Add change event listeners to variation selects
        $form.find('.variations select').on('change', function() {
            $form.trigger('woocommerce_variation_select_change');
            $form.trigger('check_variations');
        });

        // Trigger initial check
        $form.trigger('check_variations');

        console.log('Deal4u: Variation form initialized');
    }

    // Handle variation selection
    $form.on('found_variation', function(event, variation) {
        console.log('Deal4u: Variation found', variation);

        // Update price display
        if (variation.display_price !== variation.display_regular_price) {
            $('.single_variation .woocommerce-variation-price').html(
                '<span class="price">' +
                '<del><span class="woocommerce-Price-amount amount">' + variation.display_regular_price_html + '</span></del> ' +
                '<ins><span class="woocommerce-Price-amount amount">' + variation.display_price_html + '</span></ins>' +
                '</span>'
            );
        } else {
            $('.single_variation .woocommerce-variation-price').html(
                '<span class="price">' +
                '<span class="woocommerce-Price-amount amount">' + variation.display_price_html + '</span>' +
                '</span>'
            );
        }

        // Show add to cart button
        $('.single_add_to_cart_button').prop('disabled', false).removeClass('disabled');
    });

    // Handle when no variation is selected
    $form.on('reset_data', function() {
        console.log('Deal4u: Variation reset');
        $('.single_variation .woocommerce-variation-price').html('');
        $('.single_add_to_cart_button').prop('disabled', true).addClass('disabled');
    });
});
</script>
