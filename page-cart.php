<?php
/**
 * Template Name: Cart Page
 */

get_header(); ?>

<main class="main-content">
    <!-- <PERSON><PERSON>er -->
    <section style="padding: 3rem 0; background: linear-gradient(to right, #2563eb, #9333ea); color: white;">
        <div class="container">
            <div style="text-align: center;">
                <h1 style="font-size: 3rem; font-weight: bold; margin-bottom: 1rem;">Shopping Cart</h1>
                <p style="font-size: 1.25rem; color: #bfdbfe;">Review your items before checkout</p>
            </div>
        </div>
    </section>

    <!-- Cart Content -->
    <section style="padding: 4rem 0; background: #f9fafb;">
        <div class="container">
            <div style="max-width: 6xl; margin: 0 auto;">
                
                <?php if (class_exists('WooCommerce')): ?>
                    <!-- WooCommerce Cart -->
                    <div style="background: white; padding: 2rem; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);">
                        <?php echo do_shortcode('[woocommerce_cart]'); ?>
                    </div>
                    
                    <!-- Continue Shopping -->
                    <div style="text-align: center; margin-top: 2rem;">
                        <a href="<?php echo esc_url(wc_get_page_permalink('shop')); ?>" 
                           style="background: linear-gradient(to right, #6b7280, #4b5563); color: white; font-weight: bold; padding: 1rem 2rem; border-radius: 0.5rem; text-decoration: none; display: inline-flex; align-items: center; transition: all 0.3s;">
                            <i class="fas fa-arrow-left" style="margin-right: 0.5rem;"></i>Continue Shopping
                        </a>
                    </div>
                    
                <?php else: ?>
                    <!-- WooCommerce Not Active -->
                    <div style="background: white; padding: 3rem; border-radius: 1rem; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); text-align: center;">
                        <div style="font-size: 4rem; margin-bottom: 1rem;">🛒</div>
                        <h2 style="font-size: 2rem; font-weight: bold; color: #111827; margin-bottom: 1rem;">Cart Unavailable</h2>
                        <p style="color: #6b7280; margin-bottom: 2rem;">WooCommerce is required for cart functionality.</p>
                        <a href="<?php echo admin_url('plugin-install.php?s=woocommerce&tab=search&type=term'); ?>" 
                           style="background: linear-gradient(to right, #2563eb, #9333ea); color: white; font-weight: bold; padding: 1rem 2rem; border-radius: 0.5rem; text-decoration: none;">
                            Install WooCommerce
                        </a>
                    </div>
                <?php endif; ?>

            </div>
        </div>
    </section>
</main>

<style>
/* Cart Page Styling */
.woocommerce-cart-form {
    margin-bottom: 2rem;
}

.woocommerce-cart-form table {
    width: 100%;
    border-collapse: collapse;
    margin-bottom: 1rem;
}

.woocommerce-cart-form th,
.woocommerce-cart-form td {
    padding: 1rem;
    border-bottom: 1px solid #e5e7eb;
    text-align: left;
}

.woocommerce-cart-form th {
    background: #f9fafb;
    font-weight: 600;
    color: #374151;
}

.woocommerce-cart-form .product-thumbnail img {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 0.5rem;
}

.woocommerce-cart-form .product-name a {
    color: #111827;
    text-decoration: none;
    font-weight: 500;
}

.woocommerce-cart-form .product-name a:hover {
    color: #2563eb;
}

.woocommerce-cart-form .product-price {
    font-weight: 600;
    color: #2563eb;
}

.woocommerce-cart-form .product-quantity input {
    width: 60px;
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.25rem;
    text-align: center;
}

.woocommerce-cart-form .product-remove a {
    color: #ef4444;
    text-decoration: none;
    font-size: 1.2rem;
}

.woocommerce-cart-form .product-remove a:hover {
    color: #dc2626;
}

.cart-collaterals {
    margin-top: 2rem;
}

.cart_totals {
    background: #f9fafb;
    padding: 2rem;
    border-radius: 1rem;
    max-width: 400px;
    margin-left: auto;
}

.cart_totals h2 {
    font-size: 1.5rem;
    font-weight: bold;
    color: #111827;
    margin-bottom: 1rem;
}

.cart_totals table {
    width: 100%;
    margin-bottom: 1rem;
}

.cart_totals th,
.cart_totals td {
    padding: 0.5rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.cart_totals .order-total {
    font-size: 1.25rem;
    font-weight: bold;
    color: #111827;
}

.wc-proceed-to-checkout .checkout-button {
    background: linear-gradient(to right, #2563eb, #9333ea);
    color: white;
    font-weight: bold;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    border: none;
    cursor: pointer;
    font-size: 1rem;
    width: 100%;
    transition: all 0.3s;
}

.wc-proceed-to-checkout .checkout-button:hover {
    transform: scale(1.02);
}

/* Empty cart styling */
.cart-empty {
    text-align: center;
    padding: 3rem;
}

.return-to-shop {
    text-align: center;
    margin-top: 2rem;
}

.return-to-shop .button {
    background: linear-gradient(to right, #2563eb, #9333ea);
    color: white;
    font-weight: bold;
    padding: 1rem 2rem;
    border-radius: 0.5rem;
    text-decoration: none;
    display: inline-block;
    transition: all 0.3s;
}

.return-to-shop .button:hover {
    transform: scale(1.02);
}
</style>

<?php get_footer(); ?>
