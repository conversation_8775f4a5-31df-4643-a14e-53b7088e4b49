<?php
/**
 * The Template for displaying product archives, including the main shop page which is a post type archive
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/archive-product.php.
 *
 * HOWEVER, on occasion WooCommerce will need to update template files and you
 * (the theme developer) will need to copy the new files to your theme to
 * maintain compatibility. We try to do this as little as possible, but it does
 * happen. When this occurs the version of the template file will be bumped and
 * the readme will list any important changes.
 *
 * @see https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 8.6.0
 */

defined( 'ABSPATH' ) || exit;

get_header( 'shop' ); ?>

<?php
/**
 * Hook: woocommerce_before_main_content.
 *
 * @hooked woocommerce_output_content_wrapper - 10 (outputs opening divs for the content)
 * @hooked woocommerce_breadcrumb - 20
 */
do_action( 'woocommerce_before_main_content' );
?>

<!-- Enhanced Professional Shop Page -->
<main class="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-5">
        <div class="absolute inset-0" style="background-image: radial-gradient(circle at 25% 25%, #3b82f6 2px, transparent 2px), radial-gradient(circle at 75% 75%, #8b5cf6 2px, transparent 2px); background-size: 50px 50px;"></div>
    </div>

    <!-- Ultra-Professional Shop Header -->
    <section class="relative py-24 bg-gradient-to-br from-slate-900 via-blue-900 to-indigo-900 text-white overflow-hidden">
        <!-- Advanced Background Effects -->
        <div class="absolute inset-0">
            <!-- Animated Gradient Mesh -->
            <div class="absolute inset-0 bg-gradient-to-br from-blue-600/20 via-purple-600/20 to-indigo-600/20 animate-pulse"></div>
            <!-- Geometric Pattern -->
            <div class="absolute inset-0 opacity-10" style="background-image: radial-gradient(circle at 25% 25%, #3b82f6 2px, transparent 2px), radial-gradient(circle at 75% 75%, #8b5cf6 2px, transparent 2px); background-size: 60px 60px;"></div>
            <!-- Floating Orbs -->
            <div class="absolute top-20 left-20 w-32 h-32 bg-gradient-to-br from-blue-400/30 to-purple-400/30 rounded-full blur-xl animate-float"></div>
            <div class="absolute top-40 right-32 w-24 h-24 bg-gradient-to-br from-purple-400/30 to-indigo-400/30 rounded-full blur-xl animate-float-delayed"></div>
            <div class="absolute bottom-32 left-1/3 w-20 h-20 bg-gradient-to-br from-indigo-400/30 to-blue-400/30 rounded-full blur-xl animate-float-slow"></div>
        </div>

        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
            <!-- Premium Header Content -->
            <div class="text-center mb-16">
                <!-- Logo/Icon Section -->
                <div class="inline-flex items-center justify-center w-24 h-24 bg-gradient-to-br from-white/20 to-white/10 rounded-2xl backdrop-blur-sm border border-white/20 mb-8 shadow-2xl">
                    <svg class="w-12 h-12 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"></path>
                    </svg>
                </div>

                <!-- Main Title -->
                <h1 class="text-6xl md:text-8xl lg:text-9xl font-black mb-6 leading-none">
                    <span class="bg-gradient-to-r from-white via-blue-100 to-purple-100 bg-clip-text text-transparent drop-shadow-2xl">
                        Premium Store
                    </span>
                </h1>

                <!-- Subtitle with Animation -->
                <div class="relative mb-8">
                    <div class="h-1 w-40 bg-gradient-to-r from-blue-400 via-purple-400 to-indigo-400 mx-auto rounded-full mb-6 shadow-lg"></div>
                    <p class="text-2xl md:text-3xl text-blue-100 font-light max-w-4xl mx-auto leading-relaxed">
                        Discover <span class="font-semibold text-white">exceptional products</span> curated for the most discerning customers
                    </p>
                </div>

                <!-- Trust Indicators -->
                <div class="flex flex-wrap justify-center items-center gap-8 mb-12 text-sm text-blue-200">
                    <div class="flex items-center gap-2">
                        <svg class="w-5 h-5 text-green-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Verified Authentic</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <svg class="w-5 h-5 text-yellow-400" fill="currentColor" viewBox="0 0 20 20">
                            <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path>
                        </svg>
                        <span>Premium Quality</span>
                    </div>
                    <div class="flex items-center gap-2">
                        <svg class="w-5 h-5 text-blue-400" fill="currentColor" viewBox="0 0 20 20">
                            <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd"></path>
                        </svg>
                        <span>Fast Delivery</span>
                    </div>
                </div>
            </div>

            <!-- Enhanced Stats Grid - COMPACT VERSION -->
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-5xl mx-auto">
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 text-center hover:bg-white/15 transition-all duration-300 group">
                    <div class="text-xl font-black text-white mb-1 group-hover:scale-110 transition-transform duration-300">1000+</div>
                    <div class="text-blue-200 font-medium text-xs">Premium Products</div>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 text-center hover:bg-white/15 transition-all duration-300 group">
                    <div class="text-xl font-black text-white mb-1 group-hover:scale-110 transition-transform duration-300">24/7</div>
                    <div class="text-blue-200 font-medium text-xs">Expert Support</div>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 text-center hover:bg-white/15 transition-all duration-300 group">
                    <div class="text-xl font-black text-white mb-1 group-hover:scale-110 transition-transform duration-300">Free</div>
                    <div class="text-blue-200 font-medium text-xs">Global Shipping</div>
                </div>
                <div class="bg-white/10 backdrop-blur-sm rounded-2xl p-4 border border-white/20 text-center hover:bg-white/15 transition-all duration-300 group">
                    <div class="text-xl font-black text-white mb-1 group-hover:scale-110 transition-transform duration-300">2Y</div>
                    <div class="text-blue-200 font-medium text-xs">Warranty</div>
                </div>
            </div>
        </div>
    </section>

    <!-- Enhanced Shop Content -->
    <section class="py-20 relative">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">

            <?php
            /**
             * Hook: woocommerce_shop_loop_header.
             *
             * @since 8.6.0
             *
             * @hooked woocommerce_product_taxonomy_archive_header - 10
             */
            do_action( 'woocommerce_shop_loop_header' );
            ?>

            <!-- Ultra-Professional Filter System -->
            <div class="mb-16 bg-white/95 backdrop-blur-xl rounded-3xl shadow-2xl border border-gray-200/50 overflow-hidden">
                <!-- Filter Header -->
                <div class="bg-gradient-to-r from-slate-50 to-blue-50 px-8 py-6 border-b border-gray-200/50">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center gap-3">
                            <div class="w-10 h-10 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.707A1 1 0 013 7V4z"></path>
                                </svg>
                            </div>
                            <div>
                                <h3 class="text-xl font-bold text-gray-900">Advanced Filters</h3>
                                <p class="text-sm text-gray-600">Find exactly what you're looking for</p>
                            </div>
                        </div>
                        <button id="clear-filters" class="px-4 py-2 text-sm font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-100 rounded-lg transition-all duration-200">
                            Clear All
                        </button>
                    </div>
                </div>

                <!-- Filter Controls -->
                <div class="p-8">
                    <div class="grid grid-cols-1 lg:grid-cols-12 gap-6 items-end">
                        <!-- Advanced Search -->
                        <div class="lg:col-span-4">
                            <label class="block text-sm font-semibold text-gray-700 mb-3">Search Products</label>
                            <div class="relative group">
                                <input type="text" id="product-search" placeholder="Search by name, brand, or SKU..."
                                       class="w-full pl-12 pr-4 py-4 border-2 border-gray-200 rounded-2xl focus:border-blue-500 focus:ring-4 focus:ring-blue-100 transition-all duration-300 text-lg font-medium bg-white shadow-sm group-hover:shadow-md">
                                <svg class="absolute left-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 group-focus-within:text-blue-500 transition-colors duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Category Filter -->
                        <div class="lg:col-span-2">
                            <label class="block text-sm font-semibold text-gray-700 mb-3">Category</label>
                            <div class="relative">
                                <select id="category-filter" class="w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:border-purple-500 focus:ring-4 focus:ring-purple-100 transition-all duration-300 font-medium bg-white shadow-sm hover:shadow-md appearance-none cursor-pointer">
                                    <option value="">All Categories</option>
                                    <?php
                                    $categories = get_terms(array(
                                        'taxonomy' => 'product_cat',
                                        'hide_empty' => true,
                                    ));
                                    foreach ($categories as $category) {
                                        echo '<option value="' . esc_attr($category->slug) . '">' . esc_html($category->name) . '</option>';
                                    }
                                    ?>
                                </select>
                                <svg class="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Price Range -->
                        <div class="lg:col-span-2">
                            <label class="block text-sm font-semibold text-gray-700 mb-3">Price Range</label>
                            <div class="relative">
                                <select id="price-filter" class="w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:border-green-500 focus:ring-4 focus:ring-green-100 transition-all duration-300 font-medium bg-white shadow-sm hover:shadow-md appearance-none cursor-pointer">
                                    <option value="">All Prices</option>
                                    <option value="0-50">Under $50</option>
                                    <option value="50-100">$50 - $100</option>
                                    <option value="100-200">$100 - $200</option>
                                    <option value="200-500">$200 - $500</option>
                                    <option value="500-1000">$500 - $1,000</option>
                                    <option value="1000+">Over $1,000</option>
                                </select>
                                <svg class="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- Sort Options -->
                        <div class="lg:col-span-2">
                            <label class="block text-sm font-semibold text-gray-700 mb-3">Sort By</label>
                            <div class="relative">
                                <select id="sort-filter" class="w-full px-4 py-4 border-2 border-gray-200 rounded-2xl focus:border-indigo-500 focus:ring-4 focus:ring-indigo-100 transition-all duration-300 font-medium bg-white shadow-sm hover:shadow-md appearance-none cursor-pointer">
                                    <option value="default">Featured</option>
                                    <option value="price-low">Price: Low to High</option>
                                    <option value="price-high">Price: High to Low</option>
                                    <option value="newest">Newest Arrivals</option>
                                    <option value="popular">Best Sellers</option>
                                    <option value="rating">Highest Rated</option>
                                </select>
                                <svg class="absolute right-4 top-1/2 transform -translate-y-1/2 w-5 h-5 text-gray-400 pointer-events-none" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>

                        <!-- View Toggle -->
                        <div class="lg:col-span-2">
                            <label class="block text-sm font-semibold text-gray-700 mb-3">View</label>
                            <div class="flex bg-gray-100 rounded-2xl p-1 shadow-inner">
                                <button id="grid-view" class="flex-1 flex items-center justify-center px-4 py-3 rounded-xl bg-white shadow-sm text-gray-700 font-medium transition-all duration-200 hover:bg-gray-50">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M5 3a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2V5a2 2 0 00-2-2H5zM5 11a2 2 0 00-2 2v2a2 2 0 002 2h2a2 2 0 002-2v-2a2 2 0 00-2-2H5zM11 5a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2V5zM11 13a2 2 0 012-2h2a2 2 0 012 2v2a2 2 0 01-2 2h-2a2 2 0 01-2-2v-2z"></path>
                                    </svg>
                                </button>
                                <button id="list-view" class="flex-1 flex items-center justify-center px-4 py-3 rounded-xl text-gray-500 font-medium transition-all duration-200 hover:bg-white hover:shadow-sm">
                                    <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                                        <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm0 4a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1z" clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- Active Filters -->
                    <div id="active-filters" class="mt-8 hidden">
                        <div class="flex items-center gap-3 mb-4">
                            <span class="text-sm font-semibold text-gray-700">Active Filters:</span>
                            <div id="filter-tags" class="flex flex-wrap gap-2"></div>
                        </div>
                    </div>
                </div>
            </div>



        <?php if ( woocommerce_product_loop() ) : ?>

            <?php
            /**
             * Hook: woocommerce_before_shop_loop.
             *
             * @hooked woocommerce_output_all_notices - 10
             * @hooked woocommerce_result_count - 20
             * @hooked woocommerce_catalog_ordering - 30
             */
            do_action( 'woocommerce_before_shop_loop' );
            ?>

            <!-- Professional Results Header -->
            <div class="mb-12 bg-white rounded-2xl shadow-lg border border-gray-100 p-6">
                <div class="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
                    <div class="flex items-center gap-4">
                        <div class="w-12 h-12 bg-gradient-to-br from-blue-500 to-purple-500 rounded-xl flex items-center justify-center">
                            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                            </svg>
                        </div>
                        <div>
                            <h2 class="text-2xl font-bold text-gray-900">
                                <span id="products-count" class="text-blue-600"><?php echo wc_get_loop_prop( 'total' ) ? number_format(wc_get_loop_prop( 'total' )) : 0; ?></span>
                                <span class="text-gray-700">Premium Products</span>
                            </h2>
                            <p class="text-sm text-gray-600 mt-1">Carefully curated for quality and value</p>
                        </div>
                    </div>

                    <!-- Results Per Page -->
                    <div class="flex items-center gap-3">
                        <label class="text-sm font-medium text-gray-700">Show:</label>
                        <select id="per-page" class="px-3 py-2 border border-gray-200 rounded-lg focus:border-blue-500 focus:ring-2 focus:ring-blue-100 transition-all duration-200 text-sm font-medium bg-white">
                            <option value="12">12 per page</option>
                            <option value="24">24 per page</option>
                            <option value="48">48 per page</option>
                            <option value="96">96 per page</option>
                        </select>
                    </div>
                </div>

                <!-- Progress Bar -->
                <div class="mt-4 bg-gray-100 rounded-full h-2 overflow-hidden">
                    <div class="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full transition-all duration-500" style="width: 100%"></div>
                </div>
            </div>

            <!-- Enhanced Product Grid -->
            <div id="products-container" class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8 woocommerce columns-<?php echo esc_attr( wc_get_loop_prop( 'columns' ) ); ?> transition-all duration-300">
                <?php
                if ( wc_get_loop_prop( 'total' ) ) {
                    while ( have_posts() ) {
                        the_post();

                        /**
                         * Hook: woocommerce_shop_loop.
                         */
                        do_action( 'woocommerce_shop_loop' );

                        /**
                         * Include the loop content template.
                         */
                        wc_get_template_part( 'content', 'product' );
                    }
                } else {
                    echo '<div class="col-span-full text-center py-12">';
                    echo '<div class="text-6xl mb-4">🛍️</div>';
                    echo '<h3 class="text-2xl font-bold text-gray-700 mb-2">No products found</h3>';
                    echo '<p class="text-gray-500">Try adjusting your filters or search terms</p>';
                    echo '</div>';
                }

                woocommerce_product_loop_end();
                ?>
            </div>

            <!-- Loading Spinner -->
            <div id="loading-spinner" class="hidden text-center py-12">
                <div class="inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
                <p class="mt-4 text-gray-600">Loading products...</p>
            </div>

            <?php
            /**
             * Hook: woocommerce_after_shop_loop.
             *
             * @hooked woocommerce_pagination - 10
             */
            do_action( 'woocommerce_after_shop_loop' );
            ?>

        <?php else : ?>

            <?php
            /**
             * Hook: woocommerce_no_products_found.
             *
             * @hooked wc_no_products_found - 10
             */
            do_action( 'woocommerce_no_products_found' );
            ?>

        <?php endif; ?>

    </div>
</main>

<?php
/**
 * Hook: woocommerce_after_main_content.
 *
 * @hooked woocommerce_output_content_wrapper_end - 10 (outputs closing divs for the content)
 */
do_action( 'woocommerce_after_main_content' );

/**
 * Hook: woocommerce_sidebar.
 *
 * @hooked woocommerce_get_sidebar - 10
 * REMOVED: Sidebar to prevent unwanted menu display in main content area
 */
// do_action( 'woocommerce_sidebar' );
?>

<!-- Enhanced Shop Filters JavaScript -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    const searchInput = document.getElementById('product-search');
    const categoryFilter = document.getElementById('category-filter');
    const priceFilter = document.getElementById('price-filter');
    const sortFilter = document.getElementById('sort-filter');
    const gridViewBtn = document.getElementById('grid-view');
    const listViewBtn = document.getElementById('list-view');
    const productsContainer = document.getElementById('products-container');
    const activeFiltersContainer = document.getElementById('active-filters');
    const loadingSpinner = document.getElementById('loading-spinner');
    const productsCount = document.getElementById('products-count');

    let currentView = 'grid';
    let debounceTimer;

    // Search functionality
    if (searchInput) {
        searchInput.addEventListener('input', function() {
            clearTimeout(debounceTimer);
            debounceTimer = setTimeout(() => {
                filterProducts();
            }, 300);
        });
    }

    // Filter change handlers
    [categoryFilter, priceFilter, sortFilter].forEach(filter => {
        if (filter) {
            filter.addEventListener('change', filterProducts);
        }
    });

    // View toggle functionality
    if (gridViewBtn && listViewBtn) {
        gridViewBtn.addEventListener('click', () => setView('grid'));
        listViewBtn.addEventListener('click', () => setView('list'));
    }

    function setView(view) {
        currentView = view;

        if (view === 'grid') {
            gridViewBtn.classList.add('bg-blue-500', 'text-white');
            gridViewBtn.classList.remove('text-gray-600', 'hover:bg-gray-200');
            listViewBtn.classList.remove('bg-blue-500', 'text-white');
            listViewBtn.classList.add('text-gray-600', 'hover:bg-gray-200');

            productsContainer.className = 'grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8 woocommerce transition-all duration-300';
        } else {
            listViewBtn.classList.add('bg-blue-500', 'text-white');
            listViewBtn.classList.remove('text-gray-600', 'hover:bg-gray-200');
            gridViewBtn.classList.remove('bg-blue-500', 'text-white');
            gridViewBtn.classList.add('text-gray-600', 'hover:bg-gray-200');

            productsContainer.className = 'grid grid-cols-1 gap-4 woocommerce transition-all duration-300';
        }
    }

    function filterProducts() {
        showLoading(true);
        updateActiveFilters();

        // Get filter values
        const searchTerm = searchInput ? searchInput.value.toLowerCase() : '';
        const selectedCategory = categoryFilter ? categoryFilter.value : '';
        const selectedPriceRange = priceFilter ? priceFilter.value : '';
        const selectedSort = sortFilter ? sortFilter.value : '';

        // Get all product elements
        const products = Array.from(productsContainer.querySelectorAll('.product'));
        let visibleProducts = [];

        products.forEach(product => {
            let isVisible = true;

            // Search filter
            if (searchTerm) {
                const productTitle = product.querySelector('.woocommerce-loop-product__title, h2, h3');
                const title = productTitle ? productTitle.textContent.toLowerCase() : '';
                if (!title.includes(searchTerm)) {
                    isVisible = false;
                }
            }

            // Category filter (simplified for now)
            if (selectedCategory && isVisible) {
                // This would need server-side implementation for full functionality
                // For now, we'll show all products when category is selected
            }

            // Price filter
            if (selectedPriceRange && isVisible) {
                const priceElement = product.querySelector('.price .amount, .price');
                if (priceElement) {
                    const priceText = priceElement.textContent.replace(/[^0-9.]/g, '');
                    const price = parseFloat(priceText);

                    if (!isNaN(price)) {
                        const [min, max] = selectedPriceRange.split('-').map(p => p === '+' ? Infinity : parseFloat(p));
                        if (max === undefined) {
                            if (price < min) isVisible = false;
                        } else {
                            if (price < min || price > max) isVisible = false;
                        }
                    }
                }
            }

            // Show/hide product
            if (isVisible) {
                product.style.display = 'block';
                visibleProducts.push(product);
            } else {
                product.style.display = 'none';
            }
        });

        // Sort products
        if (selectedSort && visibleProducts.length > 0) {
            sortProducts(visibleProducts, selectedSort);
        }

        // Update products count
        if (productsCount) {
            productsCount.textContent = visibleProducts.length;
        }

        // Show no results message if needed
        showNoResults(visibleProducts.length === 0);

        setTimeout(() => showLoading(false), 300);
    }

    function sortProducts(products, sortBy) {
        products.sort((a, b) => {
            switch (sortBy) {
                case 'price':
                    return getProductPrice(a) - getProductPrice(b);
                case 'price-desc':
                    return getProductPrice(b) - getProductPrice(a);
                case 'date':
                    return new Date(b.getAttribute('data-date') || 0) - new Date(a.getAttribute('data-date') || 0);
                default:
                    return 0;
            }
        });

        // Reorder DOM elements
        products.forEach(product => {
            productsContainer.appendChild(product);
        });
    }

    function getProductPrice(product) {
        const priceElement = product.querySelector('.price .amount, .price');
        if (priceElement) {
            const priceText = priceElement.textContent.replace(/[^0-9.]/g, '');
            return parseFloat(priceText) || 0;
        }
        return 0;
    }

    function updateActiveFilters() {
        const activeFilters = [];

        if (searchInput && searchInput.value) {
            activeFilters.push(`Search: "${searchInput.value}"`);
        }

        if (categoryFilter && categoryFilter.value) {
            const selectedOption = categoryFilter.options[categoryFilter.selectedIndex];
            activeFilters.push(`Category: ${selectedOption.text}`);
        }

        if (priceFilter && priceFilter.value) {
            const selectedOption = priceFilter.options[priceFilter.selectedIndex];
            activeFilters.push(`Price: ${selectedOption.text}`);
        }

        if (activeFilters.length > 0) {
            activeFiltersContainer.innerHTML = `
                <span class="text-sm text-gray-600">Active filters:</span>
                ${activeFilters.map(filter => `
                    <span class="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        ${filter}
                        <button type="button" class="ml-1 text-blue-600 hover:text-blue-800" onclick="clearFilter('${filter}')">×</button>
                    </span>
                `).join('')}
                <button type="button" class="text-sm text-red-600 hover:text-red-800 underline" onclick="clearAllFilters()">Clear all</button>
            `;
            activeFiltersContainer.classList.remove('hidden');
        } else {
            activeFiltersContainer.classList.add('hidden');
        }
    }

    function showLoading(show) {
        if (show) {
            productsContainer.style.opacity = '0.5';
            loadingSpinner.classList.remove('hidden');
        } else {
            productsContainer.style.opacity = '1';
            loadingSpinner.classList.add('hidden');
        }
    }

    function showNoResults(show) {
        let noResultsDiv = document.getElementById('no-results-message');

        if (show && !noResultsDiv) {
            noResultsDiv = document.createElement('div');
            noResultsDiv.id = 'no-results-message';
            noResultsDiv.className = 'col-span-full text-center py-12';
            noResultsDiv.innerHTML = `
                <div class="text-6xl mb-4">🔍</div>
                <h3 class="text-2xl font-bold text-gray-700 mb-2">No products found</h3>
                <p class="text-gray-500 mb-4">Try adjusting your filters or search terms</p>
                <button onclick="clearAllFilters()" class="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Clear All Filters
                </button>
            `;
            productsContainer.appendChild(noResultsDiv);
        } else if (!show && noResultsDiv) {
            noResultsDiv.remove();
        }
    }

    // Global functions for filter clearing
    window.clearAllFilters = function() {
        if (searchInput) searchInput.value = '';
        if (categoryFilter) categoryFilter.value = '';
        if (priceFilter) priceFilter.value = '';
        if (sortFilter) sortFilter.value = 'menu_order';
        filterProducts();
    };

    window.clearFilter = function(filterText) {
        if (filterText.includes('Search:')) {
            if (searchInput) searchInput.value = '';
        } else if (filterText.includes('Category:')) {
            if (categoryFilter) categoryFilter.value = '';
        } else if (filterText.includes('Price:')) {
            if (priceFilter) priceFilter.value = '';
        }
        filterProducts();
    };
});
</script>

<?php get_footer( 'shop' ); ?>
