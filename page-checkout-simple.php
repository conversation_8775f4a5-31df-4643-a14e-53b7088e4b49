<?php
/**
 * Simple Checkout Page Template
 * Use this if the main checkout template has issues
 */

get_header(); ?>

<main class="main-content">
    <div class="container mx-auto px-4 py-8">
        
        <!-- Page Header -->
        <div class="text-center mb-8">
            <h1 class="text-3xl font-bold text-gray-900 mb-4">Checkout</h1>
            <p class="text-gray-600">Complete your order securely</p>
        </div>

        <!-- Security Notice -->
        <div class="bg-green-50 border border-green-200 rounded-lg p-4 mb-8 text-center">
            <span class="text-green-800 font-medium">🔒 Your payment information is secure and encrypted</span>
        </div>

        <!-- Checkout Content -->
        <div class="bg-white rounded-lg shadow-lg p-6">
            <?php
            if (class_exists('WooCommerce')) {
                // Display WooCommerce checkout
                echo do_shortcode('[woocommerce_checkout]');
            } else {
                echo '<p>WooCommerce is required for checkout functionality.</p>';
            }
            ?>
        </div>

        <!-- Trust Badges -->
        <div class="mt-8 text-center">
            <p class="text-gray-600 mb-4">We accept all major payment methods</p>
            <div class="flex justify-center items-center space-x-6 opacity-70">
                <span class="text-2xl">💳</span>
                <span class="text-2xl">🏦</span>
                <span class="text-2xl">📱</span>
                <span class="text-gray-500 text-sm">Visa • Mastercard • PayPal • Apple Pay</span>
            </div>
        </div>

    </div>
</main>

<style>
/* Simple checkout styling */
.woocommerce-checkout {
    max-width: 1200px;
    margin: 0 auto;
}

.woocommerce-checkout .col2-set {
    display: flex;
    gap: 2rem;
    flex-wrap: wrap;
}

.woocommerce-checkout .col-1,
.woocommerce-checkout .col-2 {
    flex: 1;
    min-width: 300px;
}

.woocommerce-checkout .form-row {
    margin-bottom: 1.5rem;
}

.woocommerce-checkout input,
.woocommerce-checkout select,
.woocommerce-checkout textarea {
    width: 100%;
    padding: 0.875rem;
    border: 2px solid #e5e7eb;
    border-radius: 0.5rem;
    font-size: 1rem;
    box-sizing: border-box;
}

.woocommerce-checkout label {
    display: block;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
    font-size: 0.875rem;
}

.woocommerce-checkout #place_order {
    background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
    color: white;
    font-weight: 700;
    padding: 1.25rem 2rem;
    border-radius: 0.75rem;
    border: none;
    cursor: pointer;
    font-size: 1.125rem;
    width: 100%;
    transition: all 0.3s ease;
}

.woocommerce-checkout #place_order:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px 0 rgba(37, 99, 235, 0.4);
}

@media (max-width: 768px) {
    .woocommerce-checkout .col2-set {
        flex-direction: column;
    }
    
    .woocommerce-checkout .form-row-first,
    .woocommerce-checkout .form-row-last {
        width: 100% !important;
        display: block !important;
        margin-right: 0 !important;
    }
}
</style>

<?php get_footer(); ?>
