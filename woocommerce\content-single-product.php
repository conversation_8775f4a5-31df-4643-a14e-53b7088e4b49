<?php
/**
 * The template for displaying product content in the single-product.php template
 *
 * This template can be overridden by copying it to yourtheme/woocommerce/content-single-product.php.
 *
 * @see     https://woocommerce.com/document/template-structure/
 * @package WooCommerce\Templates
 * @version 3.6.0
 */

defined( 'ABSPATH' ) || exit;

global $product;

/**
 * Hook: woocommerce_before_single_product.
 *
 * @hooked woocommerce_output_all_notices - 10
 */
do_action( 'woocommerce_before_single_product' );

if ( post_password_required() ) {
	echo get_the_password_form(); // WPCS: XSS ok.
	return;
}
?>

<div id="product-<?php the_ID(); ?>" <?php wc_product_class( 'single-product-container', $product ); ?>>
	
	<div class="container mx-auto px-4 py-8">
		<div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
			
			<!-- Product Images -->
			<div class="product-images">
				<?php
				/**
				 * Hook: woocommerce_before_single_product_summary.
				 *
				 * @hooked woocommerce_show_product_sale_flash - 10
				 * @hooked woocommerce_show_product_images - 20
				 */
				do_action( 'woocommerce_before_single_product_summary' );
				?>
			</div>
			
			<!-- Product Summary -->
			<div class="product-summary bg-white p-6 rounded-lg shadow-sm">
				<?php
				/**
				 * Hook: woocommerce_single_product_summary.
				 *
				 * @hooked woocommerce_template_single_title - 5
				 * @hooked woocommerce_template_single_rating - 10
				 * @hooked woocommerce_template_single_price - 10
				 * @hooked woocommerce_template_single_excerpt - 20
				 * @hooked woocommerce_template_single_add_to_cart - 30
				 * @hooked woocommerce_template_single_meta - 40
				 * @hooked woocommerce_template_single_sharing - 50
				 * @hooked WC_Structured_Data::generate_product_data() - 60
				 */
				do_action( 'woocommerce_single_product_summary' );
				?>
			</div>
		</div>
		
		<!-- Product Tabs -->
		<div class="product-tabs mt-12">
			<?php
			/**
			 * Hook: woocommerce_after_single_product_summary.
			 *
			 * @hooked woocommerce_output_product_data_tabs - 10
			 * @hooked woocommerce_upsell_display - 15
			 * @hooked woocommerce_output_related_products - 20
			 */
			do_action( 'woocommerce_after_single_product_summary' );
			?>
		</div>
	</div>
</div>

<?php do_action( 'woocommerce_after_single_product' ); ?>

<style>
/* Enhanced Single Product Styling */
.single-product-container .product-images img {
    border-radius: 12px;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.single-product-container .product-summary {
    border: 1px solid #e5e7eb;
}

.single-product-container .product_title {
    font-size: 2rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 1rem;
    line-height: 1.2;
}

.single-product-container .price {
    font-size: 1.75rem;
    font-weight: 700;
    color: #059669;
    margin-bottom: 1.5rem;
}

.single-product-container .price del {
    color: #6b7280;
    font-size: 1.25rem;
    margin-right: 0.5rem;
}

.single-product-container .woocommerce-product-details__short-description {
    color: #4b5563;
    line-height: 1.6;
    margin-bottom: 2rem;
}

.single-product-container .cart {
    margin-bottom: 2rem;
}

.single-product-container .single_add_to_cart_button {
    background: linear-gradient(135deg, #3b82f6 0%, #8b5cf6 100%);
    color: white;
    padding: 1rem 2rem;
    border-radius: 0.75rem;
    font-weight: 600;
    font-size: 1.125rem;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 14px 0 rgba(59, 130, 246, 0.3);
}

.single-product-container .single_add_to_cart_button:hover {
    background: linear-gradient(135deg, #2563eb 0%, #7c3aed 100%);
    transform: translateY(-1px);
    box-shadow: 0 8px 20px 0 rgba(59, 130, 246, 0.4);
}

.single-product-container .single_add_to_cart_button:disabled {
    background: #9ca3af;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.single-product-container .quantity input {
    border: 2px solid #d1d5db;
    border-radius: 0.5rem;
    padding: 0.75rem;
    width: 80px;
    text-align: center;
    margin-right: 1rem;
}

.single-product-container .product_meta {
    border-top: 1px solid #e5e7eb;
    padding-top: 1.5rem;
    margin-top: 1.5rem;
    color: #6b7280;
    font-size: 0.875rem;
}

.single-product-container .product_meta > span {
    display: block;
    margin-bottom: 0.5rem;
}

/* Variation styling */
.variations-container {
    margin-bottom: 2rem;
}

.variations-container select {
    min-width: 200px;
}

.single_variation_wrap {
    margin-top: 1.5rem;
    padding: 1.5rem;
    background: #f9fafb;
    border-radius: 0.75rem;
    border: 1px solid #e5e7eb;
}

.woocommerce-variation-price {
    font-size: 1.5rem;
    font-weight: 700;
    color: #059669;
    margin-bottom: 1rem;
}

.woocommerce-variation-description {
    color: #4b5563;
    margin-bottom: 1rem;
}

/* Stock status */
.stock {
    font-weight: 600;
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    display: inline-block;
    margin-bottom: 1rem;
}

.stock.in-stock {
    background: #d1fae5;
    color: #065f46;
}

.stock.out-of-stock {
    background: #fee2e2;
    color: #991b1b;
}

/* No variations fallback styling */
.no-variations-fallback {
    margin-bottom: 2rem;
}

.quantity-and-cart {
    align-items: center;
}

@media (max-width: 768px) {
    .single-product-container .grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }
    
    .single-product-container .product_title {
        font-size: 1.5rem;
    }
    
    .single-product-container .price {
        font-size: 1.5rem;
    }
    
    .quantity-and-cart {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }
    
    .quantity-and-cart .quantity {
        align-self: flex-start;
    }
}
</style>
